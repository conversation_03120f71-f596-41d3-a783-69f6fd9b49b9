package airpods

import (
	"airAi/global"
	"time"
)

type Quota struct {
	global.GVA_MODEL
	Uid            uint      `json:"uid" gorm:"index;comment:uid"`
	MonthlyQuota   int       `json:"monthlyQuota" gorm:"default:50;comment:每月使用量"`
	CurrentUsage   int       `json:"currentUsage" gorm:"default:0;comment:当前使用量"`
	ExtraPurchases int       `json:"extraPurchases" gorm:"default:0;comment:额外购买次数"`
	LastResetDate  time.Time `json:"lastResetDate" gorm:"comment:最新重置时间"`
}

func (Quota) TableName() string {
	return "airpods_quota"
}

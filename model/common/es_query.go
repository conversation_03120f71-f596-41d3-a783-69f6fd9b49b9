package common

type TermQuery struct {
	Term map[string]interface{} `json:"term"`
}

type RangeQuery struct {
	Range map[string]interface{} `json:"range"`
}

type BoolQuery struct {
	Must []interface{} `json:"must"`
}

type Query struct {
	<PERSON><PERSON> BoolQuery `json:"bool"`
}

type ESQuery struct {
	Query Query         `json:"query"`
	Size  int           `json:"size"`
	From  int           `json:"from"`
	Sort  []interface{} `json:"sort"`
}

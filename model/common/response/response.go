package response

import (
	"airAi/core/i18n"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

const (
	ERROR   = 7 // 保持向后兼容
	SUCCESS = 0 // 保持向后兼容
)

func Result(code int, data interface{}, msg string, c *gin.Context) {
	// 开始时间
	c.JSON(http.StatusOK, Response{
		code,
		data,
		msg,
	})
}

func Ok(c *gin.Context) {
	Result(SUCCESS, map[string]interface{}{}, "success", c)
}

func OkWithMessage(message string, c *gin.Context) {
	Result(SUCCESS, map[string]interface{}{}, message, c)
}

func OkWithData(data interface{}, c *gin.Context) {
	Result(SUCCESS, data, "success", c)
}

func OkWithDetailed(data interface{}, message string, c *gin.Context) {
	Result(SUCCESS, data, message, c)
}

func FailWithMessage(message string, c *gin.Context) {
	Result(ERROR, map[string]interface{}{}, message, c)
}

func NoAuth(message string, c *gin.Context) {
	// 使用新的国际化认证错误响应，保持向后兼容
	FailWithAuthError(c, 1200, "unauthorized") // ERROR_UNAUTHORIZED
}

func NoInsufficientBalance(message string, c *gin.Context) {
	// 使用新的国际化认证错误响应，保持向后兼容
	FailWithAuthError(c, 1203, "insufficient_balance") // ERROR_INSUFFICIENT_BALANCE
}

// 增强的响应函数，支持国际化和新的错误代码系统
// 这些函数提供了更好的错误处理和多语言支持

// OkWithI18n 返回成功响应（支持国际化）
func OkWithI18n(c *gin.Context, data interface{}) {
	// 使用增强响应系统的兼容函数
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, "success")
	Result(SUCCESS, data, message, c)
}

// FailWithI18nCode 返回带国际化消息和特定错误代码的失败响应
func FailWithI18nCode(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessageWithParams(lang, messageKey, params...)
	Result(errorCode, map[string]interface{}{}, message, c)
}

// FailWithValidationError 返回参数验证错误响应
func FailWithValidationError(c *gin.Context, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, 1100, messageKey, params...) // ERROR_INVALID_PARAMS
}

// FailWithFileError 返回文件相关错误响应
func FailWithFileError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithASRError 返回语音识别错误响应
func FailWithASRError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithTranslateError 返回翻译错误响应
func FailWithTranslateError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithTTSError 返回语音合成错误响应
func FailWithTTSError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithMeetingError 返回会议纪要错误响应
func FailWithMeetingError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithStreamError 返回流式处理错误响应
func FailWithStreamError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// FailWithAuthError 返回认证授权错误响应
func FailWithAuthError(c *gin.Context, errorCode int, messageKey string, params ...interface{}) {
	FailWithI18nCode(c, errorCode, messageKey, params...)
}

// 辅助函数

// getLanguageFromContext 从上下文中获取语言代码
func getLanguageFromContext(c *gin.Context) string {
	// 优先从查询参数获取
	if lang := c.Query("lang"); lang != "" {
		return lang
	}

	// 从表单参数获取
	if lang := c.PostForm("lang"); lang != "" {
		return lang
	}

	// 从请求头获取
	if lang := c.GetHeader("Accept-Language"); lang != "" {
		return lang
	}

	// 从lang请求头获取（兼容现有代码）
	if lang := c.GetHeader("lang"); lang != "" {
		return lang
	}

	// 默认返回中文
	return "zh_CN"
}

// getLocalizedMessage 获取本地化消息（使用go-i18n系统）
func getLocalizedMessage(lang, key string) string {
	// 导入i18n包并使用新的本地化系统
	// 注意：这里需要在文件顶部添加import "airAi/core/i18n"

	// 尝试使用go-i18n系统获取消息
	if message := getMessageFromI18n(lang, key); message != key {
		return message
	}

	// 如果go-i18n系统失败，回退到扩展的消息映射（支持四种语言）
	messages := map[string]map[string]string{
		"zh_CN": {
			"success":              "操作成功",
			"operation_failed":     "操作失败",
			"invalid_params":       "参数无效",
			"missing_params":       "缺少必需参数",
			"invalid_language":     "不支持的语言代码",
			"file_upload_failed":   "文件上传失败",
			"file_open_failed":     "文件打开失败",
			"asr_failed":           "语音识别失败",
			"translate_failed":     "翻译失败",
			"tts_failed":           "语音合成失败",
			"meeting_failed":       "会议纪要生成失败",
			"unauthorized":         "未授权访问",
			"token_invalid":        "访问令牌无效",
			"token_expired":        "访问令牌已过期",
			"insufficient_balance": "账户余额不足",
			"speech_rate_invalid":  "语音速率无效，必须在0.5-2.0范围内",
			"speech_rate_too_slow": "语音速率过慢，最小值为0.5",
			"speech_rate_too_fast": "语音速率过快，最大值为2.0",
		},
		"en": {
			"success":              "Success",
			"operation_failed":     "Operation failed",
			"invalid_params":       "Invalid parameters",
			"missing_params":       "Missing required parameters",
			"invalid_language":     "Unsupported language code",
			"file_upload_failed":   "File upload failed",
			"file_open_failed":     "Failed to open file",
			"asr_failed":           "Speech recognition failed",
			"translate_failed":     "Translation failed",
			"tts_failed":           "Text-to-speech failed",
			"meeting_failed":       "Meeting summary generation failed",
			"unauthorized":         "Unauthorized access",
			"token_invalid":        "Invalid access token",
			"token_expired":        "Access token expired",
			"insufficient_balance": "Insufficient account balance",
			"speech_rate_invalid":  "Invalid speech rate, must be between 0.5-2.0",
			"speech_rate_too_slow": "Speech rate too slow, minimum value is 0.5",
			"speech_rate_too_fast": "Speech rate too fast, maximum value is 2.0",
		},
		"id": {
			"success":              "Berhasil",
			"operation_failed":     "Operasi gagal",
			"invalid_params":       "Parameter tidak valid",
			"missing_params":       "Parameter yang diperlukan hilang",
			"invalid_language":     "Kode bahasa tidak didukung",
			"file_upload_failed":   "Unggah file gagal",
			"file_open_failed":     "Gagal membuka file",
			"asr_failed":           "Pengenalan suara gagal",
			"translate_failed":     "Terjemahan gagal",
			"tts_failed":           "Sintesis suara gagal",
			"meeting_failed":       "Pembuatan ringkasan rapat gagal",
			"unauthorized":         "Akses tidak sah",
			"token_invalid":        "Token akses tidak valid",
			"token_expired":        "Token akses kedaluwarsa",
			"insufficient_balance": "Saldo akun tidak mencukupi",
			"speech_rate_invalid":  "Kecepatan bicara tidak valid, harus antara 0.5-2.0",
			"speech_rate_too_slow": "Kecepatan bicara terlalu lambat, nilai minimum 0.5",
			"speech_rate_too_fast": "Kecepatan bicara terlalu cepat, nilai maksimum 2.0",
		},
		"hi": {
			"success":              "सफल",
			"operation_failed":     "ऑपरेशन असफल",
			"invalid_params":       "अमान्य पैरामीटर",
			"missing_params":       "आवश्यक पैरामीटर गुम",
			"invalid_language":     "असमर्थित भाषा कोड",
			"file_upload_failed":   "फ़ाइल अपलोड असफल",
			"file_open_failed":     "फ़ाइल खोलने में असफल",
			"asr_failed":           "वाक् पहचान असफल",
			"translate_failed":     "अनुवाद असफल",
			"tts_failed":           "वाक् संश्लेषण असफल",
			"meeting_failed":       "बैठक सारांश निर्माण असफल",
			"unauthorized":         "अनधिकृत पहुंच",
			"token_invalid":        "अमान्य पहुंच टोकन",
			"token_expired":        "पहुंच टोकन समाप्त हो गया",
			"insufficient_balance": "खाता शेष अपर्याप्त",
			"speech_rate_invalid":  "अमान्य वाक् गति, 0.5-2.0 के बीच होनी चाहिए",
			"speech_rate_too_slow": "वाक् गति बहुत धीमी, न्यूनतम मान 0.5 है",
			"speech_rate_too_fast": "वाक् गति बहुत तेज़, अधिकतम मान 2.0 है",
		},
	}

	// 标准化语言代码，支持四种语言
	if lang == "zh" || lang == "zh_cn" {
		lang = "zh_CN"
	} else if lang == "en_US" || lang == "en_us" {
		lang = "en"
	} else if lang == "id_ID" || lang == "id_id" || lang == "in" {
		lang = "id"
	} else if lang == "hi_IN" || lang == "hi_in" {
		lang = "hi"
	}

	if langMessages, exists := messages[lang]; exists {
		if message, exists := langMessages[key]; exists {
			return message
		}
	}

	// 如果找不到对应的消息，返回中文默认消息
	if zhMessages, exists := messages["zh_CN"]; exists {
		if message, exists := zhMessages[key]; exists {
			return message
		}
	}

	// 最后的备选方案
	return key
}

// getMessageFromI18n 从go-i18n系统获取消息
func getMessageFromI18n(lang, key string) string {
	// 使用实际的go-i18n系统
	return i18n.GetMessageV2(lang, key)
}

// FailWithValidationErrorFromUtils 处理utils.Verify返回的验证错误
func FailWithValidationErrorFromUtils(c *gin.Context, err error) {
	if err == nil {
		FailWithValidationError(c, "invalid_params")
		return
	}

	// 检查是否为新的ValidationError类型
	if ve, ok := err.(interface {
		GetI18nKey() string
		GetI18nMessage(string) string
	}); ok {
		// 使用新的国际化验证错误
		lang := getLanguageFromContext(c)
		message := ve.GetI18nMessage(lang)

		c.JSON(http.StatusOK, Response{
			Code: 1100, // ERROR_INVALID_PARAMS
			Data: gin.H{},
			Msg:  message,
		})
		return
	}

	// 向后兼容：处理传统的验证错误
	// 这里可以使用我们之前的错误检测逻辑作为回退
	errMsg := err.Error()

	// 检测特定的验证错误类型
	if containsString(errMsg, "值不能为空") {
		FailWithValidationError(c, "validation_field_required")
	} else if containsString(errMsg, "格式校验不通过") {
		FailWithValidationError(c, "validation_format_invalid")
	} else if containsString(errMsg, "长度或值不在合法范围") {
		FailWithValidationError(c, "validation_range_invalid")
	} else if errMsg == "expect struct" {
		FailWithValidationError(c, "validation_struct_expected")
	} else {
		// 默认使用通用验证错误
		FailWithValidationError(c, "invalid_params")
	}
}

// containsString 检查字符串是否包含子字符串
func containsString(s, substr string) bool {
	return len(s) >= len(substr) && indexOfString(s, substr) >= 0
}

// indexOfString 查找子字符串的位置
func indexOfString(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// getLocalizedMessageWithParams 获取带参数的本地化消息
func getLocalizedMessageWithParams(lang, key string, params ...interface{}) string {
	message := getLocalizedMessage(lang, key)
	if len(params) > 0 {
		// 这里可以实现参数替换逻辑
		// 简化版本直接返回消息
		return message
	}
	return message
}

// Enhanced error handling functions that integrate with the new error system

// FailWithAPIError 使用标准化API错误响应
func FailWithAPIError(c *gin.Context, apiErr interface{}) {
	// 检查是否为我们的标准化错误类型
	// 由于import问题，这里使用interface{}类型检查

	// 尝试类型断言获取错误信息
	if err, ok := apiErr.(interface {
		Error() string
		HTTPStatus() int
	}); ok {
		// 设置HTTP状态码
		c.Status(err.HTTPStatus())

		// 返回错误响应
		c.JSON(err.HTTPStatus(), Response{
			Code: err.HTTPStatus(),
			Data: map[string]interface{}{},
			Msg:  err.Error(),
		})
		return
	}

	// 回退到默认错误处理
	if err, ok := apiErr.(error); ok {
		FailWithMessage(err.Error(), c)
	} else {
		FailWithMessage("Unknown error", c)
	}
}

// FailWithStandardError 使用标准化错误代码和消息
func FailWithStandardError(c *gin.Context, errorCode int, errorType string, message string) {
	lang := getLanguageFromContext(c)
	localizedMessage := getLocalizedMessage(lang, message)

	// 根据错误类型设置HTTP状态码
	httpStatus := getHTTPStatusFromErrorType(errorType)

	c.Status(httpStatus)
	c.JSON(httpStatus, Response{
		Code: errorCode,
		Data: map[string]interface{}{},
		Msg:  localizedMessage,
	})
}

// getHTTPStatusFromErrorType 根据错误类型获取HTTP状态码
func getHTTPStatusFromErrorType(errorType string) int {
	switch errorType {
	case "validation":
		return http.StatusBadRequest
	case "authentication":
		return http.StatusUnauthorized
	case "permission":
		return http.StatusForbidden
	case "not_found":
		return http.StatusNotFound
	case "conflict":
		return http.StatusConflict
	case "rate_limit":
		return http.StatusTooManyRequests
	case "timeout":
		return http.StatusRequestTimeout
	case "external_service", "network":
		return http.StatusBadGateway
	case "system":
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// Enhanced validation error functions

// FailWithValidationErrorDetails 返回详细的验证错误
func FailWithValidationErrorDetails(c *gin.Context, field string, reason string) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, "invalid_params")

	c.Status(http.StatusBadRequest)
	c.JSON(http.StatusBadRequest, Response{
		Code: 1100, // ERROR_INVALID_PARAMS
		Data: map[string]interface{}{
			"field":  field,
			"reason": reason,
		},
		Msg: message,
	})
}

// FailWithMissingParameter 返回缺少参数错误
func FailWithMissingParameter(c *gin.Context, parameter string) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, "missing_params")

	c.Status(http.StatusBadRequest)
	c.JSON(http.StatusBadRequest, Response{
		Code: 1101, // ERROR_MISSING_PARAMS
		Data: map[string]interface{}{
			"missing_parameter": parameter,
		},
		Msg: message,
	})
}

// Enhanced service-specific error functions

// FailWithServiceError 返回服务特定错误
func FailWithServiceError(c *gin.Context, service string, errorCode int, messageKey string) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, messageKey)

	httpStatus := http.StatusInternalServerError
	if errorCode >= 1400 && errorCode < 1500 {
		httpStatus = http.StatusBadRequest // File errors
	} else if errorCode >= 1500 && errorCode < 1600 {
		httpStatus = http.StatusBadGateway // ASR errors
	} else if errorCode >= 1600 && errorCode < 1700 {
		httpStatus = http.StatusBadGateway // Translation errors
	} else if errorCode >= 1700 && errorCode < 1800 {
		httpStatus = http.StatusBadGateway // TTS errors
	}

	c.Status(httpStatus)
	c.JSON(httpStatus, Response{
		Code: errorCode,
		Data: map[string]interface{}{
			"service": service,
		},
		Msg: message,
	})
}

// FailWithTimeoutError 返回超时错误
func FailWithTimeoutError(c *gin.Context, operation string) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, "operation_timeout")

	c.Status(http.StatusRequestTimeout)
	c.JSON(http.StatusRequestTimeout, Response{
		Code: 5003, // ERROR_TIMEOUT
		Data: map[string]interface{}{
			"operation": operation,
		},
		Msg: message,
	})
}

// FailWithRateLimitError 返回限流错误
func FailWithRateLimitError(c *gin.Context) {
	lang := getLanguageFromContext(c)
	message := getLocalizedMessage(lang, "rate_limit_exceeded")

	c.Status(http.StatusTooManyRequests)
	c.JSON(http.StatusTooManyRequests, Response{
		Code: 4290, // ERROR_RATE_LIMIT
		Data: map[string]interface{}{},
		Msg:  message,
	})
}

# 多语言会议纪要 API 使用示例

## 概述

`/v1/textMeeting` 端点现在支持多语言会议纪要生成。用户可以指定输出语言，系统将生成相应语言的会议纪要内容。

## API 端点

**URL**: `/v1/textMeeting`  
**方法**: POST  
**内容类型**: multipart/form-data

## 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| `language` | string | 否 | zh | 会议纪要输出语言代码 |
| `targetLanguages` | string | 否 | - | 兼容性参数（建议使用language） |
| `text` | string | 是 | - | 需要转换为会议纪要的文字内容 |

## 支持的语言

| 语言代码 | 语言名称 | 示例 |
|----------|----------|------|
| zh | 中文 | `language=zh` |
| zh_cn | 简体中文 | `language=zh_cn` |
| zh_tw | 繁体中文 | `language=zh_tw` |
| en | English | `language=en` |
| ja | 日本語 | `language=ja` |
| ko | 한국어 | `language=ko` |
| es | Español | `language=es` |
| fr | Français | `language=fr` |
| de | Deutsch | `language=de` |
| it | Italiano | `language=it` |
| pt | Português | `language=pt` |
| ru | Русский | `language=ru` |
| ar | العربية | `language=ar` |

## 使用示例

### 1. 生成中文会议纪要

```bash
curl -X POST http://localhost:8080/v1/textMeeting \
  -F "language=zh" \
  -F "text=今天的会议讨论了项目进展。张三负责前端开发，李四负责后端开发。预计下周完成第一版。"
```

### 2. 生成英文会议纪要

```bash
curl -X POST http://localhost:8080/v1/textMeeting \
  -F "language=en" \
  -F "text=Today's meeting discussed project progress. Zhang San is responsible for frontend development, Li Si for backend development. Expected to complete the first version next week."
```

### 3. 生成日语会议纪要

```bash
curl -X POST http://localhost:8080/v1/textMeeting \
  -F "language=ja" \
  -F "text=今日の会議ではプロジェクトの進捗について話し合いました。張三がフロントエンド開発を担当し、李四がバックエンド開発を担当します。来週に第一版の完成を予定しています。"
```

## 响应格式

```json
{
  "code": 0,
  "data": {
    "meeting": {
      "title": "会议标题",
      "date": "2024-01-15",
      "attendees": ["张三", "李四"],
      "summary": "会议摘要",
      "topics": [
        {
          "details": "讨论内容详情",
          "conclusions": "结论"
        }
      ],
      "action_items": [
        {
          "owner": "负责人",
          "due_date": "2024-01-22",
          "description": "任务描述"
        }
      ]
    },
    "language": "zh"
  },
  "msg": "操作成功"
}
```

## 错误处理

### 无效语言代码

```bash
curl -X POST http://localhost:8080/v1/textMeeting \
  -F "language=invalid" \
  -F "text=会议内容"
```

响应：
```json
{
  "code": 7,
  "data": {},
  "msg": "语言参数无效: 不支持的语言代码: invalid"
}
```

### 空文本内容

```bash
curl -X POST http://localhost:8080/v1/textMeeting \
  -F "language=zh" \
  -F "text="
```

响应：
```json
{
  "code": 7,
  "data": {},
  "msg": "text is empty"
}
```

## JavaScript 示例

```javascript
// 使用 FormData 发送请求
const formData = new FormData();
formData.append('language', 'en');
formData.append('text', 'Meeting content here...');

fetch('/v1/textMeeting', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.code === 0) {
    console.log('Meeting minutes:', data.data.meeting);
    console.log('Language used:', data.data.language);
  } else {
    console.error('Error:', data.msg);
  }
});
```

## Python 示例

```python
import requests

url = 'http://localhost:8080/v1/textMeeting'
data = {
    'language': 'zh',
    'text': '今天的会议讨论了项目进展...'
}

response = requests.post(url, data=data)
result = response.json()

if result['code'] == 0:
    meeting = result['data']['meeting']
    print(f"会议标题: {meeting['title']}")
    print(f"会议摘要: {meeting['summary']}")
    print(f"使用语言: {result['data']['language']}")
else:
    print(f"错误: {result['msg']}")
```

## 兼容性说明

- 新的 `language` 参数优先于旧的 `targetLanguages` 参数
- 如果两个参数都未提供，默认使用中文 (`zh`)
- 旧的 `targetLanguages` 参数仍然支持，确保向后兼容性
- 建议新的集成使用 `language` 参数以获得更好的语义清晰度

## 最佳实践

1. **语言验证**: 在发送请求前验证语言代码的有效性
2. **错误处理**: 始终检查响应中的错误代码和消息
3. **内容质量**: 提供清晰、结构化的会议内容以获得更好的纪要质量
4. **语言一致性**: 确保输入文本语言与期望的输出语言相匹配以获得最佳结果

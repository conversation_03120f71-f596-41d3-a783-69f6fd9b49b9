package main

import (
	"airAi/utils"
	"encoding/json"
	"fmt"
	"log"
)

// 演示多语言会议纪要功能
func main() {
	fmt.Println("=== 多语言会议纪要功能演示 ===\n")

	// 示例会议内容
	meetingContent := `
今天的会议讨论了以下几个重要议题：

1. 项目进展汇报
   - 张三汇报了前端开发进度，目前已完成70%
   - 李四汇报了后端API开发，预计下周完成
   - 王五负责数据库设计，已经完成初版

2. 下阶段工作安排
   - 前端团队需要在本周五前完成用户界面设计
   - 后端团队需要在下周二前完成API测试
   - 测试团队将在下周三开始集成测试

3. 遇到的问题和解决方案
   - 数据库性能问题：决定优化查询语句和添加索引
   - 前端兼容性问题：统一使用最新的浏览器标准

4. 下次会议安排
   - 时间：下周五下午2点
   - 地点：会议室A
   - 议题：项目最终验收准备
`

	// 测试不同语言的会议纪要生成
	languages := []struct {
		code string
		name string
	}{
		{"zh", "中文"},
		{"en", "English"},
		{"ja", "日本語"},
		{"ko", "한국어"},
		{"es", "Español"},
	}

	// service := &airport.UserService{} // 在实际环境中使用
	_ = meetingContent // 在实际环境中会使用这个内容

	for _, lang := range languages {
		fmt.Printf("--- 生成 %s (%s) 会议纪要 ---\n", lang.name, lang.code)

		// 验证语言代码
		if err := utils.ValidateMeetingLanguage(lang.code); err != nil {
			log.Printf("语言验证失败 %s: %v\n", lang.code, err)
			continue
		}

		// 获取语言名称
		languageName, err := utils.GetMeetingLanguageName(lang.code)
		if err != nil {
			log.Printf("获取语言名称失败 %s: %v\n", lang.code, err)
			continue
		}

		fmt.Printf("语言代码: %s\n", lang.code)
		fmt.Printf("语言名称: %s\n", languageName)

		// 注意：以下代码需要真实的AI服务调用，在演示环境中可能需要mock
		// 在实际部署环境中，这将调用真实的AI服务生成会议纪要
		fmt.Printf("调用会议纪要服务...\n")

		// 模拟会议纪要生成（实际环境中会调用AI服务）
		mockMeeting := generateMockMeeting(lang.code, languageName)

		// 序列化为JSON以展示结构
		jsonData, err := json.MarshalIndent(mockMeeting, "", "  ")
		if err != nil {
			log.Printf("JSON序列化失败: %v\n", err)
			continue
		}

		fmt.Printf("生成的会议纪要结构:\n%s\n", string(jsonData))
		fmt.Println()
	}

	// 演示语言验证功能
	fmt.Println("--- 语言验证功能演示 ---")
	testLanguages := []string{"zh", "en", "invalid", "", "ja", "xyz"}

	for _, testLang := range testLanguages {
		err := utils.ValidateMeetingLanguage(testLang)
		if err != nil {
			fmt.Printf("❌ 语言代码 '%s': %v\n", testLang, err)
		} else {
			name, _ := utils.GetMeetingLanguageName(testLang)
			fmt.Printf("✅ 语言代码 '%s': %s\n", testLang, name)
		}
	}

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("使用方法:")
	fmt.Println("1. 发送POST请求到 /v1/textMeeting")
	fmt.Println("2. 参数: language=zh&text=会议内容")
	fmt.Println("3. 支持的语言: zh, en, ja, ko, es, fr, de, it, pt, ru 等")
}

// generateMockMeeting 生成模拟的会议纪要（用于演示）
func generateMockMeeting(languageCode, languageName string) map[string]interface{} {
	// 根据语言生成不同的示例内容
	var title, summary string
	var attendees []string

	switch languageCode {
	case "zh":
		title = "项目进展讨论会议"
		summary = "讨论了项目当前进展、下阶段工作安排和遇到的问题"
		attendees = []string{"张三", "李四", "王五"}
	case "en":
		title = "Project Progress Discussion Meeting"
		summary = "Discussed current project progress, next phase work arrangements and encountered issues"
		attendees = []string{"Zhang San", "Li Si", "Wang Wu"}
	case "ja":
		title = "プロジェクト進捗討議会議"
		summary = "プロジェクトの現在の進捗、次段階の作業安排と遭遇した問題について討議"
		attendees = []string{"張三", "李四", "王五"}
	case "ko":
		title = "프로젝트 진행 토론 회의"
		summary = "프로젝트 현재 진행상황, 다음 단계 작업 배치 및 발생한 문제에 대해 토론"
		attendees = []string{"장삼", "이사", "왕오"}
	case "es":
		title = "Reunión de Discusión del Progreso del Proyecto"
		summary = "Se discutió el progreso actual del proyecto, los arreglos de trabajo de la siguiente fase y los problemas encontrados"
		attendees = []string{"Zhang San", "Li Si", "Wang Wu"}
	default:
		title = "Meeting Title in " + languageName
		summary = "Meeting summary in " + languageName
		attendees = []string{"Attendee 1", "Attendee 2", "Attendee 3"}
	}

	return map[string]interface{}{
		"title":     title,
		"date":      "2024-01-15",
		"attendees": attendees,
		"summary":   summary,
		"topics": []map[string]interface{}{
			{
				"details":     "Project progress details in " + languageName,
				"conclusions": "Conclusions in " + languageName,
			},
		},
		"action_items": []map[string]interface{}{
			{
				"owner":       attendees[0],
				"due_date":    "2024-01-22",
				"description": "Task description in " + languageName,
			},
		},
	}
}

<!DOCTYPE html>
<html>
<head>
    <title>语音识别</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #result {
            border: 1px solid #ccc;
            min-height: 100px;
            padding: 10px;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
            border-radius: 4px;
        }
        button:disabled {
            background-color: #cccccc;
        }
        .status {
            margin-top: 10px;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
<h1>语音识别演示</h1>
<button id="startBtn">开始录音</button>
<button id="stopBtn" disabled>停止录音</button>
<div class="status" id="status">准备就绪</div>
<div id="result"></div>

<script>
    let ws = null;
    let mediaRecorder = null;
    let audioStream = null;
    let audioChunks = [];

    // 更新状态显示
    function updateStatus(message) {
        document.getElementById('status').innerText = message;
        console.log(message);
    }

    // 建立WebSocket连接
    function connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/v1/ws/audioRecognize`;

        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
            updateStatus("WebSocket连接已建立");
            document.getElementById('stopBtn').disabled = false;
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                if (data.error) {
                    document.getElementById('result').innerHTML +=
                        `<p style="color:red;">错误: ${data.error}</p>`;
                } else if (data.text) {
                    document.getElementById('result').innerHTML +=
                        `<p>识别结果: ${data.text}</p>`;
                }
            } catch (e) {
                document.getElementById('result').innerText += event.data + "\n";
            }
        };

        ws.onclose = () => {
            updateStatus("WebSocket连接已关闭");
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('startBtn').disabled = false;
        };

        ws.onerror = (error) => {
            updateStatus("WebSocket错误: " + error.message);
            console.error('WebSocket error:', error);
        };
    }

    // 开始录音
    document.getElementById('startBtn').onclick = async () => {
        try {
            updateStatus("正在请求麦克风权限...");

            // 获取麦克风权限并设置音频参数
            audioStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                },
                video: false
            });

            updateStatus("麦克风已授权，正在初始化录音...");

            // 检查音频轨道
            const audioTracks = audioStream.getAudioTracks();
            if (audioTracks.length === 0) {
                throw new Error("未检测到音频输入设备");
            }

            updateStatus(`使用音频设备: ${audioTracks[0].label}`);

            // 配置MediaRecorder
            const options = {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 16000
            };

            mediaRecorder = new MediaRecorder(audioStream, options);
            connectWebSocket();

            mediaRecorder.ondataavailable = async (event) => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);

                    if (ws && ws.readyState === WebSocket.OPEN) {
                        try {
                            // 转换为ArrayBuffer发送
                            const arrayBuffer = await event.data.arrayBuffer();
                            ws.send(arrayBuffer);
                            updateStatus(`发送音频数据: ${arrayBuffer.byteLength}字节`);
                        } catch (error) {
                            console.error('发送音频数据失败:', error);
                        }
                    }
                }
            };

            mediaRecorder.onerror = (event) => {
                console.error('录音错误:', event.error);
                updateStatus(`录音错误: ${event.error.name}`);
            };

            mediaRecorder.onstop = () => {
                updateStatus("录音已停止");
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({action: "close"}));
                }
            };

            // 每100ms收集一次数据
            mediaRecorder.start(100);
            updateStatus("录音进行中...");
            document.getElementById('startBtn').disabled = true;

        } catch (err) {
            updateStatus("错误: " + err.message);
            console.error('录音失败:', err);

            // 清理资源
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
            }
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
            document.getElementById('startBtn').disabled = false;
        }
    };

    // 停止录音
    document.getElementById('stopBtn').onclick = () => {
        if (mediaRecorder && mediaRecorder.state === 'recording') {
            updateStatus("正在停止录音...");
            mediaRecorder.stop();

            // 停止所有音频轨道
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
            }

            // 关闭WebSocket
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        }
    };
</script>
</body>
</html>
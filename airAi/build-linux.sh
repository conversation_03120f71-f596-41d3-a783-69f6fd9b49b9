#!/bin/bash

# airAi Linux构建脚本
# 专门用于构建Linux版本的可执行文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="airAi"
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME=$(date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -w -s"

echo -e "${BLUE}🐧 airAi Linux构建工具${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "项目: ${PROJECT_NAME}"
echo -e "版本: ${VERSION}"
echo -e "构建时间: ${BUILD_TIME}"
echo -e "Git提交: ${GIT_COMMIT}"
echo ""

# 创建构建目录
mkdir -p dist

# 构建函数
build_linux() {
    local arch=$1
    local output_name="${PROJECT_NAME}-linux-${arch}"
    
    echo -e "${YELLOW}🔨 构建 Linux ${arch} 版本...${NC}"
    
    GOOS=linux GOARCH=${arch} go build \
        -ldflags "${LDFLAGS}" \
        -o "dist/${output_name}" \
        .
    
    if [ $? -eq 0 ]; then
        local size=$(du -h "dist/${output_name}" | cut -f1)
        echo -e "${GREEN}✅ 构建成功: dist/${output_name} (${size})${NC}"
    else
        echo -e "${RED}❌ 构建失败: Linux ${arch}${NC}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}用法:${NC}"
    echo -e "  $0 [选项]"
    echo ""
    echo -e "${GREEN}选项:${NC}"
    echo -e "  amd64     构建 Linux AMD64 版本"
    echo -e "  arm64     构建 Linux ARM64 版本"
    echo -e "  all       构建所有 Linux 版本 (默认)"
    echo -e "  clean     清理构建文件"
    echo -e "  help      显示此帮助信息"
    echo ""
    echo -e "${GREEN}示例:${NC}"
    echo -e "  $0              # 构建所有Linux版本"
    echo -e "  $0 amd64        # 只构建AMD64版本"
    echo -e "  $0 arm64        # 只构建ARM64版本"
    echo -e "  $0 clean        # 清理构建文件"
}

# 清理函数
clean_build() {
    echo -e "${YELLOW}🧹 清理构建文件...${NC}"
    rm -rf dist/airAi-linux-*
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 验证构建结果
verify_build() {
    echo -e "${BLUE}📋 构建结果验证:${NC}"
    echo -e "${BLUE}================================${NC}"
    
    for file in dist/airAi-linux-*; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}文件:${NC} $(basename $file)"
            echo -e "${GREEN}大小:${NC} $(du -h $file | cut -f1)"
            echo -e "${GREEN}类型:${NC} $(file $file | cut -d: -f2 | xargs)"
            echo ""
        fi
    done
}

# 主逻辑
case "${1:-all}" in
    "amd64")
        build_linux "amd64"
        verify_build
        ;;
    "arm64")
        build_linux "arm64"
        verify_build
        ;;
    "all")
        build_linux "amd64"
        build_linux "arm64"
        verify_build
        ;;
    "clean")
        clean_build
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知选项: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac

echo -e "${GREEN}🎉 Linux构建完成！${NC}"
echo -e "${BLUE}💡 提示: 可以使用以下命令在Linux系统上运行:${NC}"
echo -e "   ${YELLOW}./dist/airAi-linux-amd64${NC}  # 在 x86_64 Linux 上"
echo -e "   ${YELLOW}./dist/airAi-linux-arm64${NC}  # 在 ARM64 Linux 上"

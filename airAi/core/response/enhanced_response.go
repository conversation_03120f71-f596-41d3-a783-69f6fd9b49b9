package response

import (
	"airAi/core/consts"
	"airAi/core/i18n"
	"airAi/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// EnhancedResponse 增强的响应结构
type EnhancedResponse struct {
	Code      int         `json:"code"`                 // 错误代码
	Data      interface{} `json:"data"`                 // 响应数据
	Msg       string      `json:"msg"`                  // 响应消息
	Timestamp int64       `json:"timestamp,omitempty"`  // 时间戳（可选）
	RequestId string      `json:"request_id,omitempty"` // 请求ID（可选）
	Lang      string      `json:"lang,omitempty"`       // 响应语言（可选）
}

// ErrorDetail 错误详情结构（用于调试模式）
type ErrorDetail struct {
	Code        int    `json:"code"`
	Category    string `json:"category"`
	Description string `json:"description"`
	Message     string `json:"message"`
}

// EnhancedErrorResponse 增强的错误响应结构（调试模式）
type EnhancedErrorResponse struct {
	EnhancedResponse
	Error *ErrorDetail `json:"error,omitempty"` // 错误详情（调试模式）
}

// ResponseOptions 响应选项
type ResponseOptions struct {
	IncludeTimestamp bool   // 是否包含时间戳
	IncludeRequestId bool   // 是否包含请求ID
	IncludeLang      bool   // 是否包含语言信息
	DebugMode        bool   // 是否启用调试模式
	RequestId        string // 自定义请求ID
}

// DefaultResponseOptions 默认响应选项
var DefaultResponseOptions = ResponseOptions{
	IncludeTimestamp: false,
	IncludeRequestId: false,
	IncludeLang:      false,
	DebugMode:        false,
}

// GetLanguageFromContext 从上下文中获取语言代码
func GetLanguageFromContext(c *gin.Context) string {
	// 优先从查询参数获取
	if lang := c.Query("lang"); lang != "" {
		return lang
	}

	// 从表单参数获取
	if lang := c.PostForm("lang"); lang != "" {
		return lang
	}

	// 从请求头获取
	if lang := c.GetHeader("Accept-Language"); lang != "" {
		return lang
	}

	// 使用工具函数获取（兼容现有代码）
	return utils.GetLang(c)
}

// buildResponse 构建响应对象
func buildResponse(code int, data interface{}, messageKey i18n.MessageKey, lang string, options ResponseOptions, params ...interface{}) interface{} {
	// 获取本地化消息
	message := i18n.GetMessageWithParams(lang, messageKey, params...)

	response := EnhancedResponse{
		Code: code,
		Data: data,
		Msg:  message,
	}

	// 添加可选字段
	if options.IncludeTimestamp {
		response.Timestamp = getCurrentTimestamp()
	}

	if options.IncludeRequestId && options.RequestId != "" {
		response.RequestId = options.RequestId
	}

	if options.IncludeLang {
		response.Lang = i18n.NormalizeLangCode(lang)
	}

	// 调试模式下添加错误详情
	if options.DebugMode && code != consts.SUCCESS {
		if errorInfo, exists := consts.GetErrorInfo(code); exists {
			errorResponse := EnhancedErrorResponse{
				EnhancedResponse: response,
				Error: &ErrorDetail{
					Code:        code,
					Category:    errorInfo.Category,
					Description: errorInfo.Description,
					Message:     message,
				},
			}
			return errorResponse
		}
	}

	return response
}

// SuccessWithI18n 返回成功响应（支持国际化）
func SuccessWithI18n(c *gin.Context, data interface{}, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(consts.SUCCESS, data, i18n.MSG_SUCCESS, lang, opts)
	c.JSON(http.StatusOK, response)
}

// SuccessWithMessageI18n 返回带自定义消息的成功响应（支持国际化）
func SuccessWithMessageI18n(c *gin.Context, data interface{}, messageKey i18n.MessageKey, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(consts.SUCCESS, data, messageKey, lang, opts)
	c.JSON(http.StatusOK, response)
}

// ErrorWithI18n 返回错误响应（支持国际化）
func ErrorWithI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(errorCode, map[string]interface{}{}, messageKey, lang, opts)
	c.JSON(http.StatusOK, response)
}

// ErrorWithParamsI18n 返回带参数的错误响应（支持国际化）
func ErrorWithParamsI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, params []interface{}, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(errorCode, map[string]interface{}{}, messageKey, lang, opts, params...)
	c.JSON(http.StatusOK, response)
}

// ValidationErrorI18n 返回参数验证错误响应（支持国际化）
func ValidationErrorI18n(c *gin.Context, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, consts.ERROR_INVALID_PARAMS, messageKey, options...)
}

// UnauthorizedI18n 返回未授权错误响应（支持国际化）
func UnauthorizedI18n(c *gin.Context, messageKey i18n.MessageKey, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(consts.ERROR_UNAUTHORIZED, map[string]interface{}{}, messageKey, lang, opts)
	c.JSON(http.StatusUnauthorized, response)
}

// InsufficientBalanceI18n 返回余额不足错误响应（支持国际化）
func InsufficientBalanceI18n(c *gin.Context, options ...ResponseOptions) {
	opts := getOptions(options...)
	lang := GetLanguageFromContext(c)

	response := buildResponse(consts.ERROR_INSUFFICIENT_BALANCE, map[string]interface{}{}, i18n.MSG_INSUFFICIENT_BALANCE, lang, opts)
	c.JSON(http.StatusUnauthorized, response)
}

// FileErrorI18n 返回文件相关错误响应（支持国际化）
func FileErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// ASRErrorI18n 返回语音识别错误响应（支持国际化）
func ASRErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// TranslateErrorI18n 返回翻译错误响应（支持国际化）
func TranslateErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// TTSErrorI18n 返回语音合成错误响应（支持国际化）
func TTSErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// MeetingErrorI18n 返回会议纪要错误响应（支持国际化）
func MeetingErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// StreamErrorI18n 返回流式处理错误响应（支持国际化）
func StreamErrorI18n(c *gin.Context, errorCode int, messageKey i18n.MessageKey, options ...ResponseOptions) {
	ErrorWithI18n(c, errorCode, messageKey, options...)
}

// getOptions 获取响应选项，如果没有提供则使用默认选项
func getOptions(options ...ResponseOptions) ResponseOptions {
	if len(options) > 0 {
		return options[0]
	}
	return DefaultResponseOptions
}

// getCurrentTimestamp 获取当前时间戳（毫秒）
func getCurrentTimestamp() int64 {
	return 0 // 这里可以实现实际的时间戳获取逻辑
}

// 向后兼容的包装函数，保持与现有代码的兼容性

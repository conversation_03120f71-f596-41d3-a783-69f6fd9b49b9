package i18n

import (
	"airAi/core/consts"
	"fmt"
)

// MessageKey 消息键类型
type MessageKey string

// 消息键常量定义
const (
	// 通用消息
	MSG_SUCCESS             MessageKey = "success"
	MSG_OPERATION_SUCCESS   MessageKey = "operation_success"
	MSG_OPERATION_FAILED    MessageKey = "operation_failed"
	MSG_INTERNAL_ERROR      MessageKey = "internal_error"
	MSG_SERVICE_UNAVAILABLE MessageKey = "service_unavailable"
	MSG_REQUEST_TIMEOUT     MessageKey = "request_timeout"
	MSG_RATE_LIMIT_EXCEEDED MessageKey = "rate_limit_exceeded"

	// 参数验证消息
	MSG_INVALID_PARAMS     MessageKey = "invalid_params"
	MSG_MISSING_PARAMS     MessageKey = "missing_params"
	MSG_INVALID_FORMAT     MessageKey = "invalid_format"
	MSG_INVALID_LANGUAGE   MessageKey = "invalid_language"
	MSG_INVALID_FILE       MessageKey = "invalid_file"
	MSG_FILE_TOO_LARGE     MessageKey = "file_too_large"
	MSG_UNSUPPORTED_FORMAT MessageKey = "unsupported_format"

	// 认证和授权消息
	MSG_UNAUTHORIZED         MessageKey = "unauthorized"
	MSG_TOKEN_INVALID        MessageKey = "token_invalid"
	MSG_TOKEN_EXPIRED        MessageKey = "token_expired"
	MSG_INSUFFICIENT_BALANCE MessageKey = "insufficient_balance"
	MSG_PERMISSION_DENIED    MessageKey = "permission_denied"
	MSG_ACCOUNT_LOCKED       MessageKey = "account_locked"

	// 用户相关消息
	MSG_USER_NOT_FOUND      MessageKey = "user_not_found"
	MSG_USER_ALREADY_EXISTS MessageKey = "user_already_exists"
	MSG_INVALID_CREDENTIALS MessageKey = "invalid_credentials"
	MSG_CODE_INVALID        MessageKey = "code_invalid"
	MSG_CODE_EXPIRED        MessageKey = "code_expired"
	MSG_PASSWORD_WEAK       MessageKey = "password_weak"

	// 文件处理消息
	MSG_FILE_UPLOAD_FAILED MessageKey = "file_upload_failed"
	MSG_FILE_OPEN_FAILED   MessageKey = "file_open_failed"
	MSG_FILE_READ_FAILED   MessageKey = "file_read_failed"
	MSG_FILE_WRITE_FAILED  MessageKey = "file_write_failed"
	MSG_FILE_NOT_FOUND     MessageKey = "file_not_found"

	// 语音识别消息
	MSG_ASR_FAILED           MessageKey = "asr_failed"
	MSG_ASR_TIMEOUT          MessageKey = "asr_timeout"
	MSG_ASR_UNSUPPORTED_LANG MessageKey = "asr_unsupported_lang"
	MSG_ASR_AUDIO_FORMAT     MessageKey = "asr_audio_format"
	MSG_ASR_AUDIO_TOO_SHORT  MessageKey = "asr_audio_too_short"
	MSG_ASR_AUDIO_TOO_LONG   MessageKey = "asr_audio_too_long"

	// 翻译消息
	MSG_TRANSLATE_FAILED           MessageKey = "translate_failed"
	MSG_TRANSLATE_TIMEOUT          MessageKey = "translate_timeout"
	MSG_TRANSLATE_UNSUPPORTED_LANG MessageKey = "translate_unsupported_lang"
	MSG_TRANSLATE_TEXT_TOO_LONG    MessageKey = "translate_text_too_long"

	// 语音合成消息
	MSG_TTS_FAILED           MessageKey = "tts_failed"
	MSG_TTS_TIMEOUT          MessageKey = "tts_timeout"
	MSG_TTS_UNSUPPORTED_LANG MessageKey = "tts_unsupported_lang"
	MSG_TTS_TEXT_TOO_LONG    MessageKey = "tts_text_too_long"

	// 会议纪要消息
	MSG_MEETING_FAILED           MessageKey = "meeting_failed"
	MSG_MEETING_TIMEOUT          MessageKey = "meeting_timeout"
	MSG_MEETING_CONTENT_EMPTY    MessageKey = "meeting_content_empty"
	MSG_MEETING_CONTENT_TOO_LONG MessageKey = "meeting_content_too_long"

	// 流式处理消息
	MSG_STREAM_FAILED      MessageKey = "stream_failed"
	MSG_STREAM_INTERRUPTED MessageKey = "stream_interrupted"
	MSG_STREAM_TIMEOUT     MessageKey = "stream_timeout"
	MSG_WEBSOCKET_FAILED   MessageKey = "websocket_failed"
)

// 中文消息映射表
var ChineseMessages = map[MessageKey]string{
	// 通用消息
	MSG_SUCCESS:             "操作成功",
	MSG_OPERATION_SUCCESS:   "操作成功",
	MSG_OPERATION_FAILED:    "操作失败",
	MSG_INTERNAL_ERROR:      "内部服务器错误",
	MSG_SERVICE_UNAVAILABLE: "服务暂时不可用",
	MSG_REQUEST_TIMEOUT:     "请求超时",
	MSG_RATE_LIMIT_EXCEEDED: "请求频率过高，请稍后再试",

	// 参数验证消息
	MSG_INVALID_PARAMS:     "参数无效",
	MSG_MISSING_PARAMS:     "缺少必需参数",
	MSG_INVALID_FORMAT:     "参数格式错误",
	MSG_INVALID_LANGUAGE:   "不支持的语言代码",
	MSG_INVALID_FILE:       "文件无效",
	MSG_FILE_TOO_LARGE:     "文件大小超出限制",
	MSG_UNSUPPORTED_FORMAT: "不支持的文件格式",

	// 认证和授权消息
	MSG_UNAUTHORIZED:         "未授权访问",
	MSG_TOKEN_INVALID:        "访问令牌无效",
	MSG_TOKEN_EXPIRED:        "访问令牌已过期",
	MSG_INSUFFICIENT_BALANCE: "账户余额不足",
	MSG_PERMISSION_DENIED:    "权限不足",
	MSG_ACCOUNT_LOCKED:       "账户已被锁定",

	// 用户相关消息
	MSG_USER_NOT_FOUND:      "用户不存在",
	MSG_USER_ALREADY_EXISTS: "用户已存在",
	MSG_INVALID_CREDENTIALS: "用户名或密码错误",
	MSG_CODE_INVALID:        "验证码错误",
	MSG_CODE_EXPIRED:        "验证码已过期",
	MSG_PASSWORD_WEAK:       "密码强度不足",

	// 文件处理消息
	MSG_FILE_UPLOAD_FAILED: "文件上传失败",
	MSG_FILE_OPEN_FAILED:   "文件打开失败",
	MSG_FILE_READ_FAILED:   "文件读取失败",
	MSG_FILE_WRITE_FAILED:  "文件写入失败",
	MSG_FILE_NOT_FOUND:     "文件不存在",

	// 语音识别消息
	MSG_ASR_FAILED:           "语音识别失败",
	MSG_ASR_TIMEOUT:          "语音识别超时",
	MSG_ASR_UNSUPPORTED_LANG: "不支持的语音识别语言",
	MSG_ASR_AUDIO_FORMAT:     "音频格式不支持",
	MSG_ASR_AUDIO_TOO_SHORT:  "音频时长过短",
	MSG_ASR_AUDIO_TOO_LONG:   "音频时长过长",

	// 翻译消息
	MSG_TRANSLATE_FAILED:           "翻译失败",
	MSG_TRANSLATE_TIMEOUT:          "翻译超时",
	MSG_TRANSLATE_UNSUPPORTED_LANG: "不支持的翻译语言",
	MSG_TRANSLATE_TEXT_TOO_LONG:    "文本过长",

	// 语音合成消息
	MSG_TTS_FAILED:           "语音合成失败",
	MSG_TTS_TIMEOUT:          "语音合成超时",
	MSG_TTS_UNSUPPORTED_LANG: "不支持的语音合成语言",
	MSG_TTS_TEXT_TOO_LONG:    "文本过长",

	// 会议纪要消息
	MSG_MEETING_FAILED:           "会议纪要生成失败",
	MSG_MEETING_TIMEOUT:          "会议纪要生成超时",
	MSG_MEETING_CONTENT_EMPTY:    "会议内容不能为空",
	MSG_MEETING_CONTENT_TOO_LONG: "会议内容过长",

	// 流式处理消息
	MSG_STREAM_FAILED:      "流式处理失败",
	MSG_STREAM_INTERRUPTED: "流式处理中断",
	MSG_STREAM_TIMEOUT:     "流式处理超时",
	MSG_WEBSOCKET_FAILED:   "WebSocket连接失败",
}

// 英文消息映射表
var EnglishMessages = map[MessageKey]string{
	// 通用消息
	MSG_SUCCESS:             "Success",
	MSG_OPERATION_SUCCESS:   "Operation successful",
	MSG_OPERATION_FAILED:    "Operation failed",
	MSG_INTERNAL_ERROR:      "Internal server error",
	MSG_SERVICE_UNAVAILABLE: "Service temporarily unavailable",
	MSG_REQUEST_TIMEOUT:     "Request timeout",
	MSG_RATE_LIMIT_EXCEEDED: "Rate limit exceeded, please try again later",

	// 参数验证消息
	MSG_INVALID_PARAMS:     "Invalid parameters",
	MSG_MISSING_PARAMS:     "Missing required parameters",
	MSG_INVALID_FORMAT:     "Invalid parameter format",
	MSG_INVALID_LANGUAGE:   "Unsupported language code",
	MSG_INVALID_FILE:       "Invalid file",
	MSG_FILE_TOO_LARGE:     "File size exceeds limit",
	MSG_UNSUPPORTED_FORMAT: "Unsupported file format",

	// 认证和授权消息
	MSG_UNAUTHORIZED:         "Unauthorized access",
	MSG_TOKEN_INVALID:        "Invalid access token",
	MSG_TOKEN_EXPIRED:        "Access token expired",
	MSG_INSUFFICIENT_BALANCE: "Insufficient account balance",
	MSG_PERMISSION_DENIED:    "Permission denied",
	MSG_ACCOUNT_LOCKED:       "Account locked",

	// 用户相关消息
	MSG_USER_NOT_FOUND:      "User not found",
	MSG_USER_ALREADY_EXISTS: "User already exists",
	MSG_INVALID_CREDENTIALS: "Invalid username or password",
	MSG_CODE_INVALID:        "Invalid verification code",
	MSG_CODE_EXPIRED:        "Verification code expired",
	MSG_PASSWORD_WEAK:       "Password strength insufficient",

	// 文件处理消息
	MSG_FILE_UPLOAD_FAILED: "File upload failed",
	MSG_FILE_OPEN_FAILED:   "Failed to open file",
	MSG_FILE_READ_FAILED:   "Failed to read file",
	MSG_FILE_WRITE_FAILED:  "Failed to write file",
	MSG_FILE_NOT_FOUND:     "File not found",

	// 语音识别消息
	MSG_ASR_FAILED:           "Speech recognition failed",
	MSG_ASR_TIMEOUT:          "Speech recognition timeout",
	MSG_ASR_UNSUPPORTED_LANG: "Unsupported speech recognition language",
	MSG_ASR_AUDIO_FORMAT:     "Unsupported audio format",
	MSG_ASR_AUDIO_TOO_SHORT:  "Audio duration too short",
	MSG_ASR_AUDIO_TOO_LONG:   "Audio duration too long",

	// 翻译消息
	MSG_TRANSLATE_FAILED:           "Translation failed",
	MSG_TRANSLATE_TIMEOUT:          "Translation timeout",
	MSG_TRANSLATE_UNSUPPORTED_LANG: "Unsupported translation language",
	MSG_TRANSLATE_TEXT_TOO_LONG:    "Text too long",

	// 语音合成消息
	MSG_TTS_FAILED:           "Text-to-speech failed",
	MSG_TTS_TIMEOUT:          "Text-to-speech timeout",
	MSG_TTS_UNSUPPORTED_LANG: "Unsupported text-to-speech language",
	MSG_TTS_TEXT_TOO_LONG:    "Text too long",

	// 会议纪要消息
	MSG_MEETING_FAILED:           "Meeting summary generation failed",
	MSG_MEETING_TIMEOUT:          "Meeting summary generation timeout",
	MSG_MEETING_CONTENT_EMPTY:    "Meeting content cannot be empty",
	MSG_MEETING_CONTENT_TOO_LONG: "Meeting content too long",

	// 流式处理消息
	MSG_STREAM_FAILED:      "Streaming processing failed",
	MSG_STREAM_INTERRUPTED: "Streaming processing interrupted",
	MSG_STREAM_TIMEOUT:     "Streaming processing timeout",
	MSG_WEBSOCKET_FAILED:   "WebSocket connection failed",
}

// 印尼语消息映射表
var IndonesianMessages = map[MessageKey]string{
	// 通用消息
	MSG_SUCCESS:             "Berhasil",
	MSG_OPERATION_SUCCESS:   "Operasi berhasil",
	MSG_OPERATION_FAILED:    "Operasi gagal",
	MSG_INTERNAL_ERROR:      "Kesalahan server internal",
	MSG_SERVICE_UNAVAILABLE: "Layanan sementara tidak tersedia",
	MSG_REQUEST_TIMEOUT:     "Timeout permintaan",
	MSG_RATE_LIMIT_EXCEEDED: "Tingkat permintaan terlalu tinggi, silakan coba lagi nanti",

	// 参数验证消息
	MSG_INVALID_PARAMS:     "Parameter tidak valid",
	MSG_MISSING_PARAMS:     "Parameter yang diperlukan hilang",
	MSG_INVALID_FORMAT:     "Format parameter tidak valid",
	MSG_INVALID_LANGUAGE:   "Kode bahasa tidak didukung",
	MSG_INVALID_FILE:       "File tidak valid",
	MSG_FILE_TOO_LARGE:     "Ukuran file melebihi batas",
	MSG_UNSUPPORTED_FORMAT: "Format file tidak didukung",

	// 认证和授权消息
	MSG_UNAUTHORIZED:         "Akses tidak sah",
	MSG_TOKEN_INVALID:        "Token akses tidak valid",
	MSG_TOKEN_EXPIRED:        "Token akses kedaluwarsa",
	MSG_INSUFFICIENT_BALANCE: "Saldo akun tidak mencukupi",
	MSG_PERMISSION_DENIED:    "Izin ditolak",
	MSG_ACCOUNT_LOCKED:       "Akun terkunci",

	// 用户相关消息
	MSG_USER_NOT_FOUND:      "Pengguna tidak ditemukan",
	MSG_USER_ALREADY_EXISTS: "Pengguna sudah ada",
	MSG_INVALID_CREDENTIALS: "Nama pengguna atau kata sandi salah",
	MSG_CODE_INVALID:        "Kode verifikasi salah",
	MSG_CODE_EXPIRED:        "Kode verifikasi kedaluwarsa",
	MSG_PASSWORD_WEAK:       "Kekuatan kata sandi tidak mencukupi",

	// 文件处理消息
	MSG_FILE_UPLOAD_FAILED: "Unggah file gagal",
	MSG_FILE_OPEN_FAILED:   "Gagal membuka file",
	MSG_FILE_READ_FAILED:   "Gagal membaca file",
	MSG_FILE_WRITE_FAILED:  "Gagal menulis file",
	MSG_FILE_NOT_FOUND:     "File tidak ditemukan",

	// 语音识别消息
	MSG_ASR_FAILED:           "Pengenalan suara gagal",
	MSG_ASR_TIMEOUT:          "Timeout pengenalan suara",
	MSG_ASR_UNSUPPORTED_LANG: "Bahasa pengenalan suara tidak didukung",
	MSG_ASR_AUDIO_FORMAT:     "Format audio tidak didukung",
	MSG_ASR_AUDIO_TOO_SHORT:  "Durasi audio terlalu pendek",
	MSG_ASR_AUDIO_TOO_LONG:   "Durasi audio terlalu panjang",

	// 翻译消息
	MSG_TRANSLATE_FAILED:           "Terjemahan gagal",
	MSG_TRANSLATE_TIMEOUT:          "Timeout terjemahan",
	MSG_TRANSLATE_UNSUPPORTED_LANG: "Bahasa terjemahan tidak didukung",
	MSG_TRANSLATE_TEXT_TOO_LONG:    "Teks terlalu panjang",

	// 语音合成消息
	MSG_TTS_FAILED:           "Sintesis suara gagal",
	MSG_TTS_TIMEOUT:          "Timeout sintesis suara",
	MSG_TTS_UNSUPPORTED_LANG: "Bahasa sintesis suara tidak didukung",
	MSG_TTS_TEXT_TOO_LONG:    "Teks terlalu panjang",

	// 会议纪要消息
	MSG_MEETING_FAILED:           "Pembuatan ringkasan rapat gagal",
	MSG_MEETING_TIMEOUT:          "Timeout pembuatan ringkasan rapat",
	MSG_MEETING_CONTENT_EMPTY:    "Konten rapat tidak boleh kosong",
	MSG_MEETING_CONTENT_TOO_LONG: "Konten rapat terlalu panjang",

	// 流式处理消息
	MSG_STREAM_FAILED:      "Pemrosesan streaming gagal",
	MSG_STREAM_INTERRUPTED: "Pemrosesan streaming terputus",
	MSG_STREAM_TIMEOUT:     "Timeout pemrosesan streaming",
	MSG_WEBSOCKET_FAILED:   "Koneksi WebSocket gagal",
}

// 印地语消息映射表
var HindiMessages = map[MessageKey]string{
	// 通用消息
	MSG_SUCCESS:             "सफल",
	MSG_OPERATION_SUCCESS:   "ऑपरेशन सफल",
	MSG_OPERATION_FAILED:    "ऑपरेशन असफल",
	MSG_INTERNAL_ERROR:      "आंतरिक सर्वर त्रुटि",
	MSG_SERVICE_UNAVAILABLE: "सेवा अस्थायी रूप से अनुपलब्ध",
	MSG_REQUEST_TIMEOUT:     "अनुरोध समय समाप्त",
	MSG_RATE_LIMIT_EXCEEDED: "अनुरोध दर बहुत अधिक है, कृपया बाद में पुनः प्रयास करें",

	// 参数验证消息
	MSG_INVALID_PARAMS:     "अमान्य पैरामीटर",
	MSG_MISSING_PARAMS:     "आवश्यक पैरामीटर गुम",
	MSG_INVALID_FORMAT:     "अमान्य पैरामीटर प्रारूप",
	MSG_INVALID_LANGUAGE:   "असमर्थित भाषा कोड",
	MSG_INVALID_FILE:       "अमान्य फ़ाइल",
	MSG_FILE_TOO_LARGE:     "फ़ाइल का आकार सीमा से अधिक",
	MSG_UNSUPPORTED_FORMAT: "असमर्थित फ़ाइल प्रारूप",

	// 认证和授权消息
	MSG_UNAUTHORIZED:         "अनधिकृत पहुंच",
	MSG_TOKEN_INVALID:        "अमान्य पहुंच टोकन",
	MSG_TOKEN_EXPIRED:        "पहुंच टोकन समाप्त हो गया",
	MSG_INSUFFICIENT_BALANCE: "खाता शेष अपर्याप्त",
	MSG_PERMISSION_DENIED:    "अनुमति अस्वीकृत",
	MSG_ACCOUNT_LOCKED:       "खाता लॉक किया गया",

	// 用户相关消息
	MSG_USER_NOT_FOUND:      "उपयोगकर्ता नहीं मिला",
	MSG_USER_ALREADY_EXISTS: "उपयोगकर्ता पहले से मौजूद है",
	MSG_INVALID_CREDENTIALS: "अमान्य उपयोगकर्ता नाम या पासवर्ड",
	MSG_CODE_INVALID:        "अमान्य सत्यापन कोड",
	MSG_CODE_EXPIRED:        "सत्यापन कोड समाप्त हो गया",
	MSG_PASSWORD_WEAK:       "पासवर्ड की मजबूती अपर्याप्त",

	// 文件处理消息
	MSG_FILE_UPLOAD_FAILED: "फ़ाइल अपलोड असफल",
	MSG_FILE_OPEN_FAILED:   "फ़ाइल खोलने में असफल",
	MSG_FILE_READ_FAILED:   "फ़ाइल पढ़ने में असफल",
	MSG_FILE_WRITE_FAILED:  "फ़ाइल लिखने में असफल",
	MSG_FILE_NOT_FOUND:     "फ़ाइल नहीं मिली",

	// 语音识别消息
	MSG_ASR_FAILED:           "वाक् पहचान असफल",
	MSG_ASR_TIMEOUT:          "वाक् पहचान समय समाप्त",
	MSG_ASR_UNSUPPORTED_LANG: "असमर्थित वाक् पहचान भाषा",
	MSG_ASR_AUDIO_FORMAT:     "असमर्थित ऑडियो प्रारूप",
	MSG_ASR_AUDIO_TOO_SHORT:  "ऑडियो अवधि बहुत छोटी",
	MSG_ASR_AUDIO_TOO_LONG:   "ऑडियो अवधि बहुत लंबी",

	// 翻译消息
	MSG_TRANSLATE_FAILED:           "अनुवाद असफल",
	MSG_TRANSLATE_TIMEOUT:          "अनुवाद समय समाप्त",
	MSG_TRANSLATE_UNSUPPORTED_LANG: "असमर्थित अनुवाद भाषा",
	MSG_TRANSLATE_TEXT_TOO_LONG:    "पाठ बहुत लंबा",

	// 语音合成消息
	MSG_TTS_FAILED:           "वाक् संश्लेषण असफल",
	MSG_TTS_TIMEOUT:          "वाक् संश्लेषण समय समाप्त",
	MSG_TTS_UNSUPPORTED_LANG: "असमर्थित वाक् संश्लेषण भाषा",
	MSG_TTS_TEXT_TOO_LONG:    "पाठ बहुत लंबा",

	// 会议纪要消息
	MSG_MEETING_FAILED:           "बैठक सारांश निर्माण असफल",
	MSG_MEETING_TIMEOUT:          "बैठक सारांश निर्माण समय समाप्त",
	MSG_MEETING_CONTENT_EMPTY:    "बैठक सामग्री खाली नहीं हो सकती",
	MSG_MEETING_CONTENT_TOO_LONG: "बैठक सामग्री बहुत लंबी",

	// 流式处理消息
	MSG_STREAM_FAILED:      "स्ट्रीमिंग प्रसंस्करण असफल",
	MSG_STREAM_INTERRUPTED: "स्ट्रीमिंग प्रसंस्करण बाधित",
	MSG_STREAM_TIMEOUT:     "स्ट्रीमिंग प्रसंस्करण समय समाप्त",
	MSG_WEBSOCKET_FAILED:   "WebSocket कनेक्शन असफल",
}

// 语言消息映射表
var LanguageMessages = map[string]map[MessageKey]string{
	consts.LangCodeCN: ChineseMessages,
	consts.LangCodeEN: EnglishMessages,
	consts.LangCodeID: IndonesianMessages,
	consts.LangCodeHI: HindiMessages,
	"zh":              ChineseMessages, // 兼容简化的中文代码
}

// GetMessage 根据语言代码和消息键获取本地化消息
// 优先使用go-i18n实现，如果失败则回退到原有实现
func GetMessage(lang string, key MessageKey) string {
	// 尝试使用新的go-i18n实现
	if message := GetMessageV2(lang, string(key)); message != string(key) {
		return message
	}

	// 回退到原有实现
	normalizedLang := NormalizeLangCode(lang)

	// 获取对应语言的消息映射表
	messages, exists := LanguageMessages[normalizedLang]
	if !exists {
		// 如果不支持该语言，使用默认中文
		messages = ChineseMessages
	}

	// 获取消息
	message, exists := messages[key]
	if !exists {
		// 如果消息不存在，返回消息键作为默认值
		return string(key)
	}

	return message
}

// GetMessageWithParams 获取带参数的本地化消息
// 优先使用go-i18n实现，如果失败则回退到原有实现
func GetMessageWithParams(lang string, key MessageKey, params ...interface{}) string {
	// 尝试使用新的go-i18n实现
	if message := GetMessageWithParamsV2(lang, string(key), params...); message != string(key) {
		return message
	}

	// 回退到原有实现
	message := GetMessage(lang, key)
	if len(params) > 0 {
		return fmt.Sprintf(message, params...)
	}
	return message
}

// NormalizeLangCode 标准化语言代码
func NormalizeLangCode(lang string) string {
	if normalizedLang, exists := consts.SupportedLanguages[lang]; exists {
		return normalizedLang
	}
	// 如果不支持该语言代码，返回默认语言
	return consts.DefaultLangCode
}

// IsLanguageSupported 检查是否支持指定语言
// 优先使用go-i18n实现，如果失败则回退到原有实现
func IsLanguageSupported(lang string) bool {
	// 尝试使用新的go-i18n实现
	if IsLanguageSupportedV2(lang) {
		return true
	}

	// 回退到原有实现
	normalizedLang := NormalizeLangCode(lang)
	_, exists := LanguageMessages[normalizedLang]
	return exists
}

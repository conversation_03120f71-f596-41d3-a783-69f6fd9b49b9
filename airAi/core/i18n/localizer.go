package i18n

import (
	"airAi/core/consts"
	"embed"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

//go:embed locales/*.json
var localeFS embed.FS

// I18nManager 国际化管理器
type I18nManager struct {
	bundle *i18n.Bundle
	mutex  sync.RWMutex
}

// 全局国际化管理器实例
var globalI18nManager *I18nManager
var once sync.Once

// InitI18n 初始化国际化系统
func InitI18n() error {
	var err error
	once.Do(func() {
		globalI18nManager = &I18nManager{
			bundle: i18n.NewBundle(language.Chinese), // 默认语言为中文
		}

		// 注册JSON解析器
		globalI18nManager.bundle.RegisterUnmarshalFunc("json", func(data []byte, v interface{}) error {
			return json.Unmarshal(data, v)
		})

		// 加载消息文件
		err = globalI18nManager.loadMessageFiles()
	})
	return err
}

// loadMessageFiles 加载消息文件
func (m *I18nManager) loadMessageFiles() error {
	// 定义支持的语言文件列表
	localeFiles := []struct {
		filename string
		langCode string
	}{
		{"locales/active.zh-CN.json", "zh-CN"},
		{"locales/active.en.json", "en"},
		{"locales/active.id.json", "id"},
		{"locales/active.hi.json", "hi"},
	}

	// 循环加载所有语言文件
	for _, locale := range localeFiles {
		data, err := localeFS.ReadFile(locale.filename)
		if err != nil {
			return fmt.Errorf("failed to read %s locale file: %w", locale.langCode, err)
		}

		_, err = m.bundle.ParseMessageFileBytes(data, locale.filename)
		if err != nil {
			return fmt.Errorf("failed to parse %s locale file: %w", locale.langCode, err)
		}
	}

	return nil
}

// GetLocalizer 获取本地化器
func (m *I18nManager) GetLocalizer(lang string) *i18n.Localizer {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 标准化语言代码
	normalizedLang := NormalizeLangCode(lang)

	// 创建语言标签，包括回退语言
	var langTags []string
	switch normalizedLang {
	case consts.LangCodeCN:
		langTags = []string{"zh-CN", "zh", "en", "id", "hi"}
	case consts.LangCodeEN:
		langTags = []string{"en", "zh-CN", "zh", "id", "hi"}
	case consts.LangCodeID:
		langTags = []string{"id", "en", "zh-CN", "zh", "hi"}
	case consts.LangCodeHI:
		langTags = []string{"hi", "en", "zh-CN", "zh", "id"}
	default:
		langTags = []string{"zh-CN", "zh", "en", "id", "hi"} // 默认回退到中文
	}

	return i18n.NewLocalizer(m.bundle, langTags...)
}

// GetGlobalLocalizer 获取全局本地化器
func GetGlobalLocalizer(lang string) *i18n.Localizer {
	if globalI18nManager == nil {
		// 如果未初始化，尝试初始化
		if err := InitI18n(); err != nil {
			// 初始化失败，返回nil
			return nil
		}
	}
	return globalI18nManager.GetLocalizer(lang)
}

// LocalizeMessage 本地化消息（使用go-i18n）
func LocalizeMessage(localizer *i18n.Localizer, messageID string, templateData map[string]interface{}) string {
	if localizer == nil {
		return messageID // 如果本地化器为空，返回消息ID
	}

	config := &i18n.LocalizeConfig{
		MessageID:    messageID,
		TemplateData: templateData,
	}

	message, err := localizer.Localize(config)
	if err != nil {
		// 如果本地化失败，返回消息ID作为备选
		return messageID
	}

	return message
}

// GetMessageV2 使用go-i18n获取本地化消息（新版本）
func GetMessageV2(lang string, messageID string, templateData ...map[string]interface{}) string {
	localizer := GetGlobalLocalizer(lang)
	if localizer == nil {
		// 如果获取本地化器失败，使用旧版本作为备选
		return GetMessage(lang, MessageKey(messageID))
	}

	var data map[string]interface{}
	if len(templateData) > 0 {
		data = templateData[0]
	}

	return LocalizeMessage(localizer, messageID, data)
}

// GetMessageWithParamsV2 获取带参数的本地化消息（新版本）
func GetMessageWithParamsV2(lang string, messageID string, params ...interface{}) string {
	localizer := GetGlobalLocalizer(lang)
	if localizer == nil {
		// 如果获取本地化器失败，使用旧版本作为备选
		return GetMessageWithParams(lang, MessageKey(messageID), params...)
	}

	// 将参数转换为模板数据
	templateData := make(map[string]interface{})
	for i, param := range params {
		templateData[fmt.Sprintf("Param%d", i)] = param
	}

	return LocalizeMessage(localizer, messageID, templateData)
}

// 向后兼容的函数包装器

// GetMessageCompat 兼容旧版本的GetMessage函数
func GetMessageCompat(lang string, key MessageKey) string {
	// 优先使用新版本
	message := GetMessageV2(lang, string(key))

	// 如果新版本返回的是消息ID（表示未找到），则使用旧版本
	if message == string(key) {
		return GetMessage(lang, key)
	}

	return message
}

// GetMessageWithParamsCompat 兼容旧版本的GetMessageWithParams函数
func GetMessageWithParamsCompat(lang string, key MessageKey, params ...interface{}) string {
	// 优先使用新版本
	message := GetMessageWithParamsV2(lang, string(key), params...)

	// 如果新版本返回的是消息ID（表示未找到），则使用旧版本
	if message == string(key) {
		return GetMessageWithParams(lang, key, params...)
	}

	return message
}

// IsLanguageSupportedV2 检查是否支持指定语言（新版本）
func IsLanguageSupportedV2(lang string) bool {
	normalizedLang := NormalizeLangCode(lang)

	// 检查是否为支持的语言
	switch normalizedLang {
	case consts.LangCodeCN, consts.LangCodeEN, consts.LangCodeID, consts.LangCodeHI:
		return true
	default:
		return false
	}
}

// GetSupportedLanguages 获取支持的语言列表
func GetSupportedLanguages() []string {
	return []string{consts.LangCodeCN, consts.LangCodeEN, consts.LangCodeID, consts.LangCodeHI}
}

// ReloadMessages 重新加载消息文件（用于开发和测试）
func ReloadMessages() error {
	if globalI18nManager == nil {
		return InitI18n()
	}

	globalI18nManager.mutex.Lock()
	defer globalI18nManager.mutex.Unlock()

	// 重新创建bundle
	globalI18nManager.bundle = i18n.NewBundle(language.Chinese)
	globalI18nManager.bundle.RegisterUnmarshalFunc("json", func(data []byte, v interface{}) error {
		return json.Unmarshal(data, v)
	})

	return globalI18nManager.loadMessageFiles()
}

// GetBundleInfo 获取bundle信息（用于调试）
func GetBundleInfo() map[string]interface{} {
	if globalI18nManager == nil {
		return map[string]interface{}{
			"initialized": false,
		}
	}

	globalI18nManager.mutex.RLock()
	defer globalI18nManager.mutex.RUnlock()

	return map[string]interface{}{
		"initialized":         true,
		"supported_languages": GetSupportedLanguages(),
		"default_language":    language.Chinese.String(),
	}
}

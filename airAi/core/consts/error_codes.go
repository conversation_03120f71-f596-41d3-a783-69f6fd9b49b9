package consts

// 错误代码常量定义
// 为不同的错误场景分配唯一的错误代码，便于客户端错误处理和调试
const (
	// 成功状态码
	SUCCESS = 0 // 操作成功

	// 通用错误代码 (1000-1099)
	ERROR_GENERAL             = 1000 // 通用错误
	ERROR_INTERNAL_SERVER     = 1001 // 内部服务器错误
	ERROR_SERVICE_UNAVAILABLE = 1002 // 服务不可用
	ERROR_TIMEOUT             = 1003 // 请求超时
	ERROR_RATE_LIMIT          = 1004 // 请求频率限制

	// 参数验证错误 (1100-1199)
	ERROR_INVALID_PARAMS     = 1100 // 参数无效
	ERROR_MISSING_PARAMS     = 1101 // 缺少必需参数
	ERROR_INVALID_FORMAT     = 1102 // 参数格式错误
	ERROR_INVALID_LANGUAGE   = 1103 // 语言参数无效
	ERROR_INVALID_FILE       = 1104 // 文件无效
	ERROR_FILE_TOO_LARGE     = 1105 // 文件过大
	ERROR_UNSUPPORTED_FORMAT = 1106 // 不支持的文件格式

	// 认证和授权错误 (1200-1299)
	ERROR_UNAUTHORIZED         = 1200 // 未授权访问
	ERROR_TOKEN_INVALID        = 1201 // Token无效
	ERROR_TOKEN_EXPIRED        = 1202 // Token已过期
	ERROR_INSUFFICIENT_BALANCE = 1203 // 余额不足
	ERROR_PERMISSION_DENIED    = 1204 // 权限不足
	ERROR_ACCOUNT_LOCKED       = 1205 // 账户被锁定

	// 用户相关错误 (1300-1399)
	ERROR_USER_NOT_FOUND      = 1300 // 用户不存在
	ERROR_USER_ALREADY_EXISTS = 1301 // 用户已存在
	ERROR_INVALID_CREDENTIALS = 1302 // 凭据无效
	ERROR_CODE_INVALID        = 1303 // 验证码无效
	ERROR_CODE_EXPIRED        = 1304 // 验证码已过期
	ERROR_PASSWORD_WEAK       = 1305 // 密码强度不足

	// 文件处理错误 (1400-1499)
	ERROR_FILE_UPLOAD    = 1400 // 文件上传失败
	ERROR_FILE_OPEN      = 1401 // 文件打开失败
	ERROR_FILE_READ      = 1402 // 文件读取失败
	ERROR_FILE_WRITE     = 1403 // 文件写入失败
	ERROR_FILE_NOT_FOUND = 1404 // 文件不存在

	// 语音识别错误 (1500-1599)
	ERROR_ASR_FAILED           = 1500 // 语音识别失败
	ERROR_ASR_TIMEOUT          = 1501 // 语音识别超时
	ERROR_ASR_UNSUPPORTED_LANG = 1502 // 不支持的语音识别语言
	ERROR_ASR_AUDIO_FORMAT     = 1503 // 音频格式不支持
	ERROR_ASR_AUDIO_TOO_SHORT  = 1504 // 音频时长过短
	ERROR_ASR_AUDIO_TOO_LONG   = 1505 // 音频时长过长

	// 翻译错误 (1600-1699)
	ERROR_TRANSLATE_FAILED           = 1600 // 翻译失败
	ERROR_TRANSLATE_TIMEOUT          = 1601 // 翻译超时
	ERROR_TRANSLATE_UNSUPPORTED_LANG = 1602 // 不支持的翻译语言
	ERROR_TRANSLATE_TEXT_TOO_LONG    = 1603 // 文本过长

	// 语音合成错误 (1700-1799)
	ERROR_TTS_FAILED           = 1700 // 语音合成失败
	ERROR_TTS_TIMEOUT          = 1701 // 语音合成超时
	ERROR_TTS_UNSUPPORTED_LANG = 1702 // 不支持的语音合成语言
	ERROR_TTS_TEXT_TOO_LONG    = 1703 // 文本过长

	// 会议纪要错误 (1800-1899)
	ERROR_MEETING_FAILED           = 1800 // 会议纪要生成失败
	ERROR_MEETING_TIMEOUT          = 1801 // 会议纪要生成超时
	ERROR_MEETING_CONTENT_EMPTY    = 1802 // 会议内容为空
	ERROR_MEETING_CONTENT_TOO_LONG = 1803 // 会议内容过长

	// 流式处理错误 (1900-1999)
	ERROR_STREAM_FAILED      = 1900 // 流式处理失败
	ERROR_STREAM_INTERRUPTED = 1901 // 流式处理中断
	ERROR_STREAM_TIMEOUT     = 1902 // 流式处理超时
	ERROR_WEBSOCKET_FAILED   = 1903 // WebSocket连接失败

	// 对话上下文错误 (1950-1969)
	ERROR_CONTEXT_INVALID        = 1950 // 对话上下文无效
	ERROR_CONTEXT_WINDOW_INVALID = 1951 // 上下文窗口大小无效
	ERROR_CONVERSATION_NOT_FOUND = 1952 // 对话不存在
	ERROR_CONTEXT_TOO_LARGE      = 1953 // 上下文内容过大
	ERROR_MESSAGE_FORMAT_INVALID = 1954 // 消息格式无效

	// 数据库错误 (2000-2099)
	ERROR_DB_CONNECTION = 2000 // 数据库连接失败
	ERROR_DB_QUERY      = 2001 // 数据库查询失败
	ERROR_DB_INSERT     = 2002 // 数据库插入失败
	ERROR_DB_UPDATE     = 2003 // 数据库更新失败
	ERROR_DB_DELETE     = 2004 // 数据库删除失败

	// 外部API错误 (2100-2199)
	ERROR_EXTERNAL_API         = 2100 // 外部API调用失败
	ERROR_EXTERNAL_API_TIMEOUT = 2101 // 外部API超时
	ERROR_EXTERNAL_API_QUOTA   = 2102 // 外部API配额不足
	ERROR_EXTERNAL_API_AUTH    = 2103 // 外部API认证失败

	// 向后兼容的错误代码
	ERROR = 7 // 保持向后兼容的通用错误代码
)

// ErrorCodeInfo 错误代码信息结构
type ErrorCodeInfo struct {
	Code        int    `json:"code"`        // 错误代码
	Category    string `json:"category"`    // 错误类别
	Description string `json:"description"` // 错误描述
}

// ErrorCodeMap 错误代码映射表，用于获取错误代码的详细信息
var ErrorCodeMap = map[int]ErrorCodeInfo{
	SUCCESS: {SUCCESS, "success", "操作成功"},

	// 通用错误
	ERROR_GENERAL:             {ERROR_GENERAL, "general", "通用错误"},
	ERROR_INTERNAL_SERVER:     {ERROR_INTERNAL_SERVER, "general", "内部服务器错误"},
	ERROR_SERVICE_UNAVAILABLE: {ERROR_SERVICE_UNAVAILABLE, "general", "服务不可用"},
	ERROR_TIMEOUT:             {ERROR_TIMEOUT, "general", "请求超时"},
	ERROR_RATE_LIMIT:          {ERROR_RATE_LIMIT, "general", "请求频率限制"},

	// 参数验证错误
	ERROR_INVALID_PARAMS:     {ERROR_INVALID_PARAMS, "validation", "参数无效"},
	ERROR_MISSING_PARAMS:     {ERROR_MISSING_PARAMS, "validation", "缺少必需参数"},
	ERROR_INVALID_FORMAT:     {ERROR_INVALID_FORMAT, "validation", "参数格式错误"},
	ERROR_INVALID_LANGUAGE:   {ERROR_INVALID_LANGUAGE, "validation", "语言参数无效"},
	ERROR_INVALID_FILE:       {ERROR_INVALID_FILE, "validation", "文件无效"},
	ERROR_FILE_TOO_LARGE:     {ERROR_FILE_TOO_LARGE, "validation", "文件过大"},
	ERROR_UNSUPPORTED_FORMAT: {ERROR_UNSUPPORTED_FORMAT, "validation", "不支持的文件格式"},

	// 认证和授权错误
	ERROR_UNAUTHORIZED:         {ERROR_UNAUTHORIZED, "auth", "未授权访问"},
	ERROR_TOKEN_INVALID:        {ERROR_TOKEN_INVALID, "auth", "Token无效"},
	ERROR_TOKEN_EXPIRED:        {ERROR_TOKEN_EXPIRED, "auth", "Token已过期"},
	ERROR_INSUFFICIENT_BALANCE: {ERROR_INSUFFICIENT_BALANCE, "auth", "余额不足"},
	ERROR_PERMISSION_DENIED:    {ERROR_PERMISSION_DENIED, "auth", "权限不足"},
	ERROR_ACCOUNT_LOCKED:       {ERROR_ACCOUNT_LOCKED, "auth", "账户被锁定"},

	// 用户相关错误
	ERROR_USER_NOT_FOUND:      {ERROR_USER_NOT_FOUND, "user", "用户不存在"},
	ERROR_USER_ALREADY_EXISTS: {ERROR_USER_ALREADY_EXISTS, "user", "用户已存在"},
	ERROR_INVALID_CREDENTIALS: {ERROR_INVALID_CREDENTIALS, "user", "凭据无效"},
	ERROR_CODE_INVALID:        {ERROR_CODE_INVALID, "user", "验证码无效"},
	ERROR_CODE_EXPIRED:        {ERROR_CODE_EXPIRED, "user", "验证码已过期"},
	ERROR_PASSWORD_WEAK:       {ERROR_PASSWORD_WEAK, "user", "密码强度不足"},

	// 文件处理错误
	ERROR_FILE_UPLOAD:    {ERROR_FILE_UPLOAD, "file", "文件上传失败"},
	ERROR_FILE_OPEN:      {ERROR_FILE_OPEN, "file", "文件打开失败"},
	ERROR_FILE_READ:      {ERROR_FILE_READ, "file", "文件读取失败"},
	ERROR_FILE_WRITE:     {ERROR_FILE_WRITE, "file", "文件写入失败"},
	ERROR_FILE_NOT_FOUND: {ERROR_FILE_NOT_FOUND, "file", "文件不存在"},

	// 语音识别错误
	ERROR_ASR_FAILED:           {ERROR_ASR_FAILED, "asr", "语音识别失败"},
	ERROR_ASR_TIMEOUT:          {ERROR_ASR_TIMEOUT, "asr", "语音识别超时"},
	ERROR_ASR_UNSUPPORTED_LANG: {ERROR_ASR_UNSUPPORTED_LANG, "asr", "不支持的语音识别语言"},
	ERROR_ASR_AUDIO_FORMAT:     {ERROR_ASR_AUDIO_FORMAT, "asr", "音频格式不支持"},
	ERROR_ASR_AUDIO_TOO_SHORT:  {ERROR_ASR_AUDIO_TOO_SHORT, "asr", "音频时长过短"},
	ERROR_ASR_AUDIO_TOO_LONG:   {ERROR_ASR_AUDIO_TOO_LONG, "asr", "音频时长过长"},

	// 翻译错误
	ERROR_TRANSLATE_FAILED:           {ERROR_TRANSLATE_FAILED, "translate", "翻译失败"},
	ERROR_TRANSLATE_TIMEOUT:          {ERROR_TRANSLATE_TIMEOUT, "translate", "翻译超时"},
	ERROR_TRANSLATE_UNSUPPORTED_LANG: {ERROR_TRANSLATE_UNSUPPORTED_LANG, "translate", "不支持的翻译语言"},
	ERROR_TRANSLATE_TEXT_TOO_LONG:    {ERROR_TRANSLATE_TEXT_TOO_LONG, "translate", "文本过长"},

	// 语音合成错误
	ERROR_TTS_FAILED:           {ERROR_TTS_FAILED, "tts", "语音合成失败"},
	ERROR_TTS_TIMEOUT:          {ERROR_TTS_TIMEOUT, "tts", "语音合成超时"},
	ERROR_TTS_UNSUPPORTED_LANG: {ERROR_TTS_UNSUPPORTED_LANG, "tts", "不支持的语音合成语言"},
	ERROR_TTS_TEXT_TOO_LONG:    {ERROR_TTS_TEXT_TOO_LONG, "tts", "文本过长"},

	// 会议纪要错误
	ERROR_MEETING_FAILED:           {ERROR_MEETING_FAILED, "meeting", "会议纪要生成失败"},
	ERROR_MEETING_TIMEOUT:          {ERROR_MEETING_TIMEOUT, "meeting", "会议纪要生成超时"},
	ERROR_MEETING_CONTENT_EMPTY:    {ERROR_MEETING_CONTENT_EMPTY, "meeting", "会议内容为空"},
	ERROR_MEETING_CONTENT_TOO_LONG: {ERROR_MEETING_CONTENT_TOO_LONG, "meeting", "会议内容过长"},

	// 流式处理错误
	ERROR_STREAM_FAILED:      {ERROR_STREAM_FAILED, "stream", "流式处理失败"},
	ERROR_STREAM_INTERRUPTED: {ERROR_STREAM_INTERRUPTED, "stream", "流式处理中断"},
	ERROR_STREAM_TIMEOUT:     {ERROR_STREAM_TIMEOUT, "stream", "流式处理超时"},
	ERROR_WEBSOCKET_FAILED:   {ERROR_WEBSOCKET_FAILED, "stream", "WebSocket连接失败"},

	// 对话上下文错误
	ERROR_CONTEXT_INVALID:        {ERROR_CONTEXT_INVALID, "context", "对话上下文无效"},
	ERROR_CONTEXT_WINDOW_INVALID: {ERROR_CONTEXT_WINDOW_INVALID, "context", "上下文窗口大小无效"},
	ERROR_CONVERSATION_NOT_FOUND: {ERROR_CONVERSATION_NOT_FOUND, "context", "对话不存在"},
	ERROR_CONTEXT_TOO_LARGE:      {ERROR_CONTEXT_TOO_LARGE, "context", "上下文内容过大"},
	ERROR_MESSAGE_FORMAT_INVALID: {ERROR_MESSAGE_FORMAT_INVALID, "context", "消息格式无效"},

	// 数据库错误
	ERROR_DB_CONNECTION: {ERROR_DB_CONNECTION, "database", "数据库连接失败"},
	ERROR_DB_QUERY:      {ERROR_DB_QUERY, "database", "数据库查询失败"},
	ERROR_DB_INSERT:     {ERROR_DB_INSERT, "database", "数据库插入失败"},
	ERROR_DB_UPDATE:     {ERROR_DB_UPDATE, "database", "数据库更新失败"},
	ERROR_DB_DELETE:     {ERROR_DB_DELETE, "database", "数据库删除失败"},

	// 外部API错误
	ERROR_EXTERNAL_API:         {ERROR_EXTERNAL_API, "external", "外部API调用失败"},
	ERROR_EXTERNAL_API_TIMEOUT: {ERROR_EXTERNAL_API_TIMEOUT, "external", "外部API超时"},
	ERROR_EXTERNAL_API_QUOTA:   {ERROR_EXTERNAL_API_QUOTA, "external", "外部API配额不足"},
	ERROR_EXTERNAL_API_AUTH:    {ERROR_EXTERNAL_API_AUTH, "external", "外部API认证失败"},

	// 向后兼容
	ERROR: {ERROR, "general", "通用错误"},
}

// GetErrorInfo 获取错误代码的详细信息
func GetErrorInfo(code int) (ErrorCodeInfo, bool) {
	info, exists := ErrorCodeMap[code]
	return info, exists
}

// IsValidErrorCode 检查错误代码是否有效
func IsValidErrorCode(code int) bool {
	_, exists := ErrorCodeMap[code]
	return exists
}

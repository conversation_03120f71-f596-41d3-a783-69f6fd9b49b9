package initialize

import (
	"model/system"
	"os"

	"airAi/global"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"model/airpods"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "mysql":
		return GormMysql()
	case "pgsql":
		return GormPgSql()
	case "oracle":
		return GormOracle()
	case "mssql":
		return GormMssql()
	case "sqlite":
		return GormSqlite()
	default:
		return GormMysql()
	}
}

func RegisterTables() {
	db := global.GVA_DB
	err := db.AutoMigrate(
		airpods.AirpodsUser{},
		airpods.JwtBlacklist{},
		airpods.Quota{},
		airpods.PurchaseRecord{},
		airpods.Feedback{},
		system.SysOperationRecord{},
	)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}
	global.GVA_LOG.Info("register table success")
}

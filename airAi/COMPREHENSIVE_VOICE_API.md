# Comprehensive Voice Processing API

## Overview

The enhanced StreamingVoiceTranslate API now returns comprehensive voice processing data including:

1. **Speech Recognition Results (ASR)**: Original transcribed text from voice input
2. **Translation Results**: Translated text in the target language  
3. **Voice Response Data (TTS)**: Audio response data and metadata

## API Endpoints

### 1. Enhanced StreamingVoiceTranslate (Backward Compatible)

**Endpoint**: `/v1/streamingVoiceTranslate`

**Method**: POST

**Content-Type**: multipart/form-data

**Parameters**:
- `file` (required): Audio file
- `sourceLanguage` (optional): Source language code (default: "zh")
- `targetLanguages` (optional): Target language code (default: "en")

**Response**: Server-Sent Events (SSE) stream

**Enhanced Features**:
- Now includes ASR results prefixed with `[ASR]` in the content
- Maintains backward compatibility with existing clients
- Returns both speech recognition and translation results

### 2. ComprehensiveStreamingVoiceTranslate (New)

**Endpoint**: `/v1/comprehensiveStreamingVoiceTranslate`

**Method**: POST

**Content-Type**: multipart/form-data

**Parameters**:
- `file` (required): Audio file
- `sourceLanguage` (optional): Source language code (default: "zh")
- `targetLanguages` (optional): Target language code (default: "en")

**Response**: Server-Sent Events (SSE) stream with comprehensive voice data

## Response Data Structure

### ComprehensiveVoiceResult

```json
{
  "original_text": "Hello world",
  "asr_partial": false,
  "translated_text": "你好世界", 
  "trans_partial": false,
  "voice_response": {
    "audio_data": "base64_encoded_audio",
    "audio_format": "mp3",
    "duration": 2.5,
    "sample_rate": 16000,
    "metadata": {
      "tts_model": "sambert-zhinan-v1",
      "voice": "default"
    }
  },
  "processing_type": "translation",
  "timestamp": 1234567890,
  "source_language": "en",
  "target_language": "zh",
  "is_partial": false,
  "is_end": false,
  "error": ""
}
```

### Processing Types

- `"asr"`: Speech recognition result
- `"translation"`: Translation result
- `"tts"`: Text-to-speech result
- `"complete"`: Processing completed
- `"error"`: General error
- `"asr_error"`: ASR-specific error
- `"translation_error"`: Translation-specific error
- `"tts_error"`: TTS-specific error

### VoiceResponseData

```json
{
  "audio_data": "base64_encoded_audio_bytes",
  "audio_format": "mp3",
  "duration": 2.5,
  "sample_rate": 16000,
  "metadata": {
    "tts_model": "sambert-zhinan-v1",
    "voice": "default",
    "quality": "high"
  }
}
```

## Usage Examples

### 1. Basic Usage (Enhanced Backward Compatible)

```bash
curl -X POST "http://localhost:8080/v1/streamingVoiceTranslate" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh" \
  -F "targetLanguages=en"
```

**Response Stream**:
```
data: {"content":"[ASR] 你好世界","is_partial":false,"is_end":false}

data: {"content":"Hello world","is_partial":false,"is_end":false}

data: {"content":"","is_partial":false,"is_end":true}
```

### 2. Comprehensive Voice Processing

```bash
curl -X POST "http://localhost:8080/v1/comprehensiveStreamingVoiceTranslate" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh" \
  -F "targetLanguages=en"
```

**Response Stream**:
```
data: {"original_text":"你好世界","asr_partial":false,"processing_type":"asr","timestamp":1234567890,"source_language":"zh","target_language":"en","is_partial":false,"is_end":false}

data: {"translated_text":"Hello","trans_partial":true,"processing_type":"translation","timestamp":1234567890,"source_language":"zh","target_language":"en","is_partial":true,"is_end":false}

data: {"translated_text":" world","trans_partial":false,"processing_type":"translation","timestamp":1234567890,"source_language":"zh","target_language":"en","is_partial":false,"is_end":false}

data: {"voice_response":{"audio_format":"mp3","duration":2.5,"sample_rate":16000,"metadata":{"tts_model":"sambert-zhinan-v1","voice":"default"}},"processing_type":"tts","timestamp":1234567890,"source_language":"zh","target_language":"en","is_partial":false,"is_end":false}

data: {"processing_type":"complete","timestamp":1234567890,"source_language":"zh","target_language":"en","is_partial":false,"is_end":true}
```

## Client Implementation

### JavaScript Example

```javascript
const eventSource = new EventSource('/v1/comprehensiveStreamingVoiceTranslate');

eventSource.onmessage = function(event) {
  const result = JSON.parse(event.data);
  
  switch(result.processing_type) {
    case 'asr':
      console.log('Speech Recognition:', result.original_text);
      break;
    case 'translation':
      console.log('Translation:', result.translated_text);
      break;
    case 'tts':
      console.log('Voice Response:', result.voice_response);
      // Handle audio playback
      break;
    case 'complete':
      console.log('Processing completed');
      eventSource.close();
      break;
    case 'error':
      console.error('Error:', result.error);
      break;
  }
};
```

## Features

### Real-time Processing
- Audio chunks processed immediately during upload
- Parallel ASR, translation, and TTS pipeline
- Minimal latency for real-time conversations

### Comprehensive Data
- **ASR Results**: Original transcribed text with partial/complete status
- **Translation Results**: Streaming translation with incremental updates
- **Voice Response**: Generated audio with metadata and format information

### Error Handling
- Granular error types for different processing stages
- Graceful degradation when individual components fail
- Detailed error messages for debugging

### Backward Compatibility
- Enhanced existing endpoint maintains compatibility
- Traditional clients receive enhanced data in familiar format
- New clients can use comprehensive endpoint for full features

## Performance Characteristics

- **Latency**: Very low - results stream as soon as available
- **Throughput**: High - parallel processing pipeline
- **Resource Usage**: Optimized with proper goroutine management
- **Scalability**: Non-blocking processing for multiple concurrent requests

## Integration Notes

1. **Existing Clients**: No changes required, enhanced data automatically included
2. **New Clients**: Use comprehensive endpoint for full feature access
3. **Audio Formats**: Supports various formats (wav, mp3, pcm, etc.)
4. **Language Support**: Configurable source and target languages
5. **TTS Integration**: Optional voice response generation based on translation results

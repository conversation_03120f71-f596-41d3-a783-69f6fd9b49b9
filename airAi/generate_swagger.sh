#!/bin/bash

# Swagger文档生成脚本
# 解决swag v1.16.x版本中LeftDelim和RightDelim字段兼容性问题

echo "🚀 开始生成Swagger文档..."

# 1. 生成Swagger文档
echo "📝 执行swag init命令..."
swag init -g main.go -d .,../model

if [ $? -ne 0 ]; then
    echo "❌ Swagger文档生成失败"
    exit 1
fi

echo "✅ Swagger文档生成成功"

# 2. 修复docs.go中的兼容性问题
echo "🔧 修复docs.go中的兼容性问题..."

# 检查是否存在LeftDelim和RightDelim字段
if grep -q "LeftDelim\|RightDelim" docs/docs.go; then
    echo "⚠️  发现LeftDelim和RightDelim字段，正在移除..."
    
    # 使用sed移除LeftDelim和RightDelim行
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' '/LeftDelim:/d; /RightDelim:/d' docs/docs.go
    else
        # Linux
        sed -i '/LeftDelim:/d; /RightDelim:/d' docs/docs.go
    fi
    
    echo "✅ 已移除LeftDelim和RightDelim字段"
else
    echo "✅ 未发现兼容性问题"
fi

# 3. 验证生成的文档
echo "🔍 验证生成的文档..."

# 检查关键文件是否存在
if [ ! -f "docs/docs.go" ] || [ ! -f "docs/swagger.json" ] || [ ! -f "docs/swagger.yaml" ]; then
    echo "❌ 缺少必要的文档文件"
    exit 1
fi

# 检查是否包含WebSocket接口
if grep -q "audioRecognize" docs/swagger.json; then
    echo "✅ WebSocket音频识别接口已包含在文档中"
else
    echo "⚠️  未找到WebSocket音频识别接口"
fi

# 4. 编译测试
echo "🔨 编译测试..."
go build -o /tmp/test_build . > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 编译测试通过"
    rm -f /tmp/test_build
else
    echo "❌ 编译测试失败"
    exit 1
fi

echo ""
echo "🎉 Swagger文档生成完成！"
echo ""
echo "📋 生成的文件："
echo "   - docs/docs.go"
echo "   - docs/swagger.json" 
echo "   - docs/swagger.yaml"
echo ""
echo "🌐 访问Swagger UI："
echo "   http://localhost:8888/v1/swagger/index.html"
echo ""
echo "💡 提示："
echo "   - 确保服务器正在运行"
echo "   - 如果遇到问题，请检查路由配置"
echo "   - WebSocket接口: ws://localhost:8888/v1/ws/audioRecognize"

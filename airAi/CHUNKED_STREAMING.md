# Chunked Audio Streaming Implementation

## Overview

The `StreamingVoiceTranslate` endpoint now supports true chunked audio processing, where audio data is processed in real-time as it's being uploaded, rather than waiting for the complete file upload.

## Implementation Details

### 🔄 **Processing Pipeline**

#### Traditional Approach (Default)
```
Client uploads complete file → Server receives entire file → ASR processing → Translation → Response
Timeline: [----Upload----][----ASR----][----Translation----][Response]
Latency: High (sequential processing)
```

#### Chunked Streaming Approach (New)
```
Client uploads chunk 1 → Server processes chunk 1 → ASR + Translation → Stream result
Client uploads chunk 2 → Server processes chunk 2 → ASR + Translation → Stream result
Client uploads chunk 3 → Server processes chunk 3 → ASR + Translation → Stream result
Timeline: [Upload1→ASR1→Trans1→Stream][Upload2→ASR2→Trans2→Stream][Upload3→ASR3→Trans3→Stream]
Latency: Low (parallel processing)
```

### 🚀 **Key Features**

1. **Real-time Processing**: Audio chunks are processed immediately upon receipt
2. **Parallel Pipeline**: Upload, ASR, and translation happen concurrently
3. **Backward Compatibility**: Falls back to traditional processing for existing clients
4. **Error Handling**: Robust error handling for interrupted uploads
5. **Resource Management**: Proper cleanup of goroutines and channels

### 📡 **API Usage**

#### Traditional Mode (Default)
```bash
curl -X POST "http://localhost:8080/v1/streamingVoiceTranslate" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh" \
  -F "targetLanguages=en"
```

#### Chunked Streaming Mode
```bash
curl -X POST "http://localhost:8080/v1/streamingVoiceTranslate?chunked=true" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh" \
  -F "targetLanguages=en"
```

### 🏗️ **Architecture Components**

#### 1. **Multipart Stream Parser**
- Processes multipart form data in real-time
- Extracts form fields and audio data as they arrive
- Uses `io.Pipe` for streaming data transfer

#### 2. **Audio Pipeline**
```go
// Creates a pipe for streaming audio data
audioReader, audioWriter := io.Pipe()

// Streams audio chunks to ASR service
go streamAudioData(part, audioWriter, ctx)

// Processes ASR results and translates
go StreamingVoiceTranslate(sourceLanguage, targetLanguages, audioReader, resultChan)
```

#### 3. **SSE Response Handler**
- Streams translation results in real-time
- Handles client disconnections gracefully
- Provides proper error reporting

### ⚡ **Performance Benefits**

1. **Reduced Latency**: Processing starts during upload
2. **Better Resource Utilization**: Parallel processing pipeline
3. **Improved User Experience**: Faster time-to-first-result
4. **Scalability**: Non-blocking processing for multiple requests

### 🔧 **Implementation Details**

#### Core Functions

1. **`handleStreamingMultipartUpload`**: Parses multipart data in real-time
2. **`streamAudioData`**: Streams audio chunks to processing pipeline
3. **`streamSSEResults`**: Sends translation results via SSE
4. **`handleTraditionalUpload`**: Fallback for backward compatibility

#### Error Handling

- Context cancellation for timeouts
- Graceful handling of client disconnections
- Proper cleanup of resources and goroutines
- Fallback to traditional processing on errors

### 📊 **Expected Performance Improvements**

- **Time to First Result**: 50-70% reduction
- **Overall Latency**: 30-50% improvement for large files
- **Resource Efficiency**: Better CPU and memory utilization
- **Concurrent Requests**: Improved handling of multiple simultaneous uploads

### 🧪 **Testing**

The implementation maintains full backward compatibility:

```bash
# Test traditional mode
go test -v ./api/v1/ear/ -run TestStreamingVoiceTranslate

# Test chunked streaming mode
curl -X POST "http://localhost:8080/v1/streamingVoiceTranslate?chunked=true" \
  -F "file=@test_audio.wav" \
  -F "sourceLanguage=zh" \
  -F "targetLanguages=en"
```

### 🔮 **Future Enhancements**

1. **Adaptive Chunking**: Dynamic chunk size based on network conditions
2. **Quality of Service**: Prioritization for different client types
3. **Caching**: Intelligent caching of partial ASR results
4. **Metrics**: Detailed performance monitoring and analytics

## Migration Guide

### For Existing Clients
No changes required - the endpoint maintains full backward compatibility.

### For New Clients Wanting Chunked Processing
Add `?chunked=true` query parameter to enable the new streaming mode.

### Performance Monitoring
Monitor the following metrics to measure improvement:
- Time to first translation result
- Overall request completion time
- Server resource utilization
- Client satisfaction scores

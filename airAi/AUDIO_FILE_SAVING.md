# Audio File Saving for Incremental Voice Processing

## Overview

This document describes the implementation of audio file saving functionality for the incremental voice processing system. The system now automatically saves generated TTS (Text-to-Speech) audio responses to files on the server filesystem for verification and analysis.

## 🎯 Features Implemented

### 1. **Automatic Audio File Saving** ✅
- All TTS audio responses are automatically saved to disk
- Files are saved in MP3 format (matching the TTS output format)
- Structured naming convention for easy identification
- Configurable output directory

### 2. **File Format and Location** ✅
- **Format**: MP3 (matching the `audio_format` in responses)
- **Naming Convention**: `voice_response_[timestamp]_segment_[segment_id].mp3`
- **Default Directory**: `./audio_output/` (configurable via `AUDIO_OUTPUT_DIR` environment variable)
- **Example**: `voice_response_1751372214_segment_1.mp3`

### 3. **Audio Content Verification** ✅
- Automatic verification of saved audio files
- Format validation (MP3 header verification)
- File size and duration validation
- Metadata consistency checks

### 4. **Enhanced Metadata** ✅
- Audio files include comprehensive metadata
- File path added to response metadata
- Segment tracking and processing type identification
- Translation text preservation for verification

## 🏗️ Implementation Architecture

### Core Components

#### 1. **AudioFileManager** (`utils/audio_file_utils.go`)
```go
type AudioFileManager struct {
    OutputDir string
}

// Key methods:
func (afm *AudioFileManager) SaveAudioFile(audioData []byte, segmentID int, timestamp int64, format string) (string, error)
func (afm *AudioFileManager) VerifyAudioFile(filepath string, expectedDuration float64, expectedFormat string) (*AudioFileInfo, error)
func (afm *AudioFileManager) CleanupOldFiles(maxAge time.Duration) error
```

#### 2. **Enhanced Service Layer** (`service/airport/meeting.go`)
```go
func (s *UserService) processIncrementalVoiceResponse(client *qwen.QwenClient, text, sourceLanguage, targetLanguages string, resultChan chan<- qwen.ComprehensiveVoiceResult, timestamp int64, segmentID int)
func (s *UserService) saveIncrementalAudioFile(voiceResponse *qwen.VoiceResponseData, segmentID int, timestamp int64) (string, error)
```

#### 3. **Audio File Information Structure**
```go
type AudioFileInfo struct {
    FilePath  string    `json:"file_path"`
    FileSize  int64     `json:"file_size"`
    Format    string    `json:"format"`
    Duration  float64   `json:"duration"`
    CreatedAt time.Time `json:"created_at"`
    IsValid   bool      `json:"is_valid"`
    DataSize  int       `json:"data_size"`
}
```

## 📁 File Organization

### Directory Structure
```
./audio_output/
├── voice_response_1751372214_segment_1.mp3
├── voice_response_1751372214_segment_2.mp3
├── voice_response_1751372215_segment_1.mp3
└── voice_response_1751372215_segment_2.mp3
```

### Naming Convention Details
- **`voice_response`**: Fixed prefix for identification
- **`[timestamp]`**: Unix timestamp when processing started
- **`segment`**: Fixed separator
- **`[segment_id]`**: Incremental segment number (1, 2, 3, ...)
- **`.mp3`**: File extension matching audio format

## 🔧 Configuration

### Environment Variables
```bash
# Set custom audio output directory
export AUDIO_OUTPUT_DIR="/path/to/custom/audio/output"

# Default: ./audio_output
```

### Directory Creation
- Output directory is automatically created if it doesn't exist
- Permissions set to `0755` for proper access
- Error handling for permission issues

## 📊 Enhanced Response Structure

### TTS Response with File Information
```json
{
  "voice_response": {
    "audio_data": "base64_encoded_audio_bytes",
    "audio_format": "mp3",
    "duration": 2.1,
    "sample_rate": 16000,
    "metadata": {
      "segment_id": "1",
      "processing_type": "incremental",
      "text_length": "9",
      "translated_text": "你好世界",
      "tts_model": "sambert-zhinan-v1",
      "voice": "default",
      "status": "generated",
      "saved_file_path": "./audio_output/voice_response_1751372214_segment_1.mp3"
    }
  },
  "processing_type": "tts",
  "timestamp": 1751372214,
  "is_partial": false,
  "is_end": false
}
```

### Metadata Fields
| Field | Description | Example |
|-------|-------------|---------|
| `segment_id` | Unique segment identifier | `"1"` |
| `processing_type` | Processing method | `"incremental"` |
| `text_length` | Length of translated text | `"9"` |
| `translated_text` | Original translated text | `"你好世界"` |
| `saved_file_path` | Path to saved audio file | `"./audio_output/voice_response_1751372214_segment_1.mp3"` |

## 🧪 Verification Process

### Automatic Verification
1. **File Existence**: Verify file was created successfully
2. **Format Validation**: Check MP3 header and structure
3. **Size Validation**: Ensure file size matches audio data
4. **Duration Validation**: Verify duration metadata consistency
5. **Content Integrity**: Validate audio data integrity

### Verification Results
```go
audioFileInfo := &AudioFileInfo{
    FilePath:  "./audio_output/voice_response_1751372214_segment_1.mp3",
    FileSize:  45678,
    Format:    "mp3",
    Duration:  2.1,
    CreatedAt: time.Now(),
    IsValid:   true,
    DataSize:  45678,
}
```

## 🔍 Audio Content Validation

### MP3 Format Validation
```go
// Validates MP3 files by checking for:
// 1. ID3 tag header ("ID3")
// 2. MP3 frame sync bytes (0xFF 0xFB)
func validateAudioFormat(audioData []byte, expectedFormat string) error
```

### Supported Formats
- **MP3**: Primary format with ID3 tag and frame sync validation
- **WAV**: RIFF header validation
- **Generic**: Basic non-empty data validation

## 📈 Performance Considerations

### Efficient File Operations
- **Asynchronous Saving**: File operations don't block response delivery
- **Error Resilience**: File save failures don't interrupt processing
- **Memory Efficient**: Direct byte array writing without buffering
- **Concurrent Safe**: Multiple segments can be saved simultaneously

### Resource Management
- **Automatic Cleanup**: Old files can be automatically removed
- **Configurable Retention**: Customizable file age limits
- **Directory Management**: Automatic directory creation and maintenance

## 🧪 Testing Coverage

### Test Suites
1. **`audio_file_test.go`**: Core audio file management functionality
2. **`audio_integration_test.go`**: Integration with incremental voice processing
3. **Verification Tests**: Format validation and content verification
4. **Cleanup Tests**: File retention and cleanup functionality

### Test Results
```bash
=== RUN   TestAudioFileManager
--- PASS: TestAudioFileManager (0.00s)
=== RUN   TestIncrementalAudioFileSaving
--- PASS: TestIncrementalAudioFileSaving (0.00s)
=== RUN   TestAudioContentVerification
--- PASS: TestAudioContentVerification (0.00s)
=== RUN   TestAudioFileCleanup
--- PASS: TestAudioFileCleanup (0.00s)
PASS
```

## 🔧 Usage Examples

### Manual Verification
```bash
# List saved audio files
ls -la ./audio_output/

# Check file properties
file ./audio_output/voice_response_1751372214_segment_1.mp3

# Play audio file (macOS)
afplay ./audio_output/voice_response_1751372214_segment_1.mp3

# Play audio file (Linux)
mpv ./audio_output/voice_response_1751372214_segment_1.mp3
```

### Programmatic Access
```go
// Get audio file manager
audioManager := utils.GetDefaultAudioFileManager()

// Get file information
audioFileInfo, err := audioManager.GetAudioFileInfo(filePath)
if err == nil {
    fmt.Printf("File: %s, Size: %d bytes, Duration: %.1fs\n", 
        audioFileInfo.FilePath, 
        audioFileInfo.FileSize, 
        audioFileInfo.Duration)
}

// Cleanup old files
err = audioManager.CleanupOldFiles(24 * time.Hour)
```

## 🎯 Benefits Achieved

### 1. **Content Verification** ✅
- **Manual Listening**: Saved files can be manually played to verify speech content
- **Automated Testing**: Audio files enable automated quality assurance
- **Debugging**: Issues can be traced through saved audio files
- **Quality Control**: Content accuracy can be verified against expected translations

### 2. **System Reliability** ✅
- **Error Tracking**: Failed audio generation can be identified
- **Performance Monitoring**: Audio generation times can be measured
- **Content Auditing**: All generated audio is preserved for review
- **Compliance**: Audio content can be reviewed for compliance requirements

### 3. **Development Support** ✅
- **Testing**: Comprehensive test coverage with real audio files
- **Debugging**: Audio content issues can be reproduced and fixed
- **Validation**: Translation accuracy can be verified through audio playback
- **Documentation**: Audio examples for API documentation

## 🔄 Integration with Incremental Processing

### Seamless Integration
- Audio saving is integrated into the incremental voice processing pipeline
- No impact on response latency or streaming performance
- Automatic file saving for every TTS generation
- Enhanced metadata for tracking and verification

### Processing Flow
```
User Input → ASR → Translation → TTS Generation → Audio File Saving → Response Delivery
                                      ↓
                              Automatic Verification
                                      ↓
                              Metadata Enhancement
```

## ✅ Summary

The audio file saving functionality provides:

1. **✅ Automatic Saving**: All TTS audio responses saved to structured files
2. **✅ Format Verification**: MP3 format validation and content verification
3. **✅ Enhanced Metadata**: Comprehensive tracking and identification
4. **✅ Quality Assurance**: Manual and automated content verification
5. **✅ Performance**: No impact on streaming response performance
6. **✅ Reliability**: Robust error handling and recovery
7. **✅ Testing**: Comprehensive test coverage and validation

The implementation enables complete verification of the incremental voice processing system's audio output quality and correctness, ensuring that the fixed content duplication issues are properly resolved and that generated audio matches the expected translated content.

#!/bin/bash

# 流式文本聊天API测试脚本
# 用于测试新增的条件流式响应功能

echo "=== 流式文本聊天API测试 ==="
echo ""

# 服务器地址
SERVER_URL="http://localhost:8888"
API_ENDPOINT="/v1/streamTextChat"

# 测试1: 流式响应 - Qwen
echo "1. 测试流式响应 (Qwen):"
echo "请求: stream=true, speechType=qwen"
echo "响应格式: Server-Sent Events"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "text": "你好，请简单介绍一下你自己",
    "speechType": "qwen", 
    "stream": true
  }' \
  --no-buffer

echo ""
echo "----------------------------------------"
echo ""

# 测试2: 流式响应 - DeepSeek  
echo "2. 测试流式响应 (DeepSeek):"
echo "请求: stream=true, speechType=deepseek"
echo "响应格式: Server-Sent Events"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "text": "请告诉我今天的天气如何",
    "speechType": "deepseek",
    "stream": true
  }' \
  --no-buffer

echo ""
echo "----------------------------------------"
echo ""

# 测试3: 普通响应 - Qwen
echo "3. 测试普通响应 (Qwen):"
echo "请求: stream=false, speechType=qwen"
echo "响应格式: JSON"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "text": "你好，请简单介绍一下你自己",
    "speechType": "qwen",
    "stream": false
  }' | jq '.'

echo ""
echo "----------------------------------------"
echo ""

# 测试4: 普通响应 - DeepSeek
echo "4. 测试普通响应 (DeepSeek):"
echo "请求: stream=false, speechType=deepseek"
echo "响应格式: JSON"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "text": "请告诉我今天的天气如何",
    "speechType": "deepseek",
    "stream": false
  }' | jq '.'

echo ""
echo "----------------------------------------"
echo ""

# 测试5: 默认参数测试
echo "5. 测试默认参数:"
echo "请求: 只提供text，其他使用默认值"
echo "默认: speechType=qwen, stream=false"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "text": "你好"
  }' | jq '.'

echo ""
echo "----------------------------------------"
echo ""

# 测试6: 参数验证测试
echo "6. 测试参数验证:"
echo "请求: 缺少必需的text参数"
echo "预期: 返回参数验证错误"
echo ""

curl -X POST "${SERVER_URL}${API_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "speechType": "qwen",
    "stream": false
  }' | jq '.'

echo ""
echo "=== 测试完成 ==="
echo ""
echo "使用说明:"
echo "1. 确保服务器运行在 ${SERVER_URL}"
echo "2. 流式响应会实时显示AI生成的内容"
echo "3. 普通响应会一次性返回完整结果"
echo "4. 可以通过修改 speechType 切换AI模型"
echo "5. 可以通过修改 stream 参数控制响应方式"

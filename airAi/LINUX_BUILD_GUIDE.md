# airAi Linux构建和部署指南

本指南详细说明如何构建和部署airAi项目的Linux版本。

## 🐧 构建Linux版本

### 方法1：使用Makefile（推荐）

#### 构建所有Linux版本
```bash
make build-linux
```
生成文件：
- `dist/airAi-linux-amd64` - 适用于x86_64 Linux系统
- `dist/airAi-linux-arm64` - 适用于ARM64 Linux系统

#### 构建特定架构
```bash
# 只构建AMD64版本
make build-linux-amd64

# 只构建ARM64版本  
make build-linux-arm64
```

#### 构建所有平台版本
```bash
make cross-compile
```
生成所有平台的可执行文件，包括Linux、macOS、Windows。

### 方法2：使用专用构建脚本

```bash
# 构建所有Linux版本
./build-linux.sh

# 构建特定架构
./build-linux.sh amd64
./build-linux.sh arm64

# 查看帮助
./build-linux.sh help

# 清理构建文件
./build-linux.sh clean
```

### 方法3：手动构建

```bash
# Linux AMD64
GOOS=linux GOARCH=amd64 go build -o airAi-linux-amd64 .

# Linux ARM64
GOOS=linux GOARCH=arm64 go build -o airAi-linux-arm64 .
```

## 📦 部署到Linux服务器

### 1. 上传可执行文件

```bash
# 使用scp上传
scp dist/airAi-linux-amd64 user@server:/opt/airAi/

# 或使用rsync
rsync -av dist/airAi-linux-amd64 user@server:/opt/airAi/
```

### 2. 设置执行权限

```bash
chmod +x /opt/airAi/airAi-linux-amd64
```

### 3. 创建符号链接（可选）

```bash
ln -s /opt/airAi/airAi-linux-amd64 /usr/local/bin/airAi
```

### 4. 配置文件部署

```bash
# 上传配置文件
scp config.yaml user@server:/opt/airAi/
scp config.test.yaml user@server:/opt/airAi/

# 或创建生产配置
scp config.yaml user@server:/opt/airAi/config.production.yaml
```

## 🚀 运行airAi

### 直接运行

```bash
# 使用默认配置
./airAi-linux-amd64

# 指定配置文件
./airAi-linux-amd64 -c config.production.yaml

# 使用环境变量
GVA_CONFIG=config.production.yaml ./airAi-linux-amd64
```

### 使用systemd服务（推荐）

创建服务文件 `/etc/systemd/system/airai.service`：

```ini
[Unit]
Description=airAi Service
After=network.target

[Service]
Type=simple
User=airai
Group=airai
WorkingDirectory=/opt/airAi
ExecStart=/opt/airAi/airAi-linux-amd64 -c config.production.yaml
Restart=always
RestartSec=5
Environment=GIN_MODE=release

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=airai

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/airAi/log

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start airai

# 设置开机自启
sudo systemctl enable airai

# 查看状态
sudo systemctl status airai

# 查看日志
sudo journalctl -u airai -f
```

## 🐳 Docker部署

### 构建Docker镜像

```bash
make docker-build
```

### 运行Docker容器

```bash
# 基本运行
make docker-run

# 自定义运行
docker run -d \
  --name airai \
  -p 8888:8888 \
  -v /path/to/config:/app/config \
  -v /path/to/logs:/app/log \
  airAi:latest
```

## 🔧 环境要求

### 系统要求
- Linux内核版本 >= 3.10
- glibc >= 2.17（对于AMD64）
- 内存 >= 512MB
- 磁盘空间 >= 1GB

### 网络要求
- 端口8888（默认HTTP端口）
- 访问外部AI服务（DeepSeek API）
- 访问Redis服务器
- 访问数据库服务器

### 依赖服务
- Redis（缓存和会话存储）
- MySQL/PostgreSQL（数据存储）
- 讯飞语音服务（语音识别和TTS）

## 📊 性能优化

### 1. 编译优化

构建时已包含优化标志：
```bash
-ldflags "-w -s"  # 去除调试信息，减小文件大小
```

### 2. 运行时优化

```bash
# 设置Go运行时参数
export GOMAXPROCS=4          # 限制CPU核心数
export GOGC=100              # 垃圾回收频率
export GOMEMLIMIT=1GiB       # 内存限制
```

### 3. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

## 🔍 故障排除

### 1. 权限问题
```bash
# 检查文件权限
ls -la airAi-linux-amd64

# 设置正确权限
chmod +x airAi-linux-amd64
```

### 2. 端口占用
```bash
# 检查端口占用
netstat -tlnp | grep 8888
lsof -i :8888

# 修改配置文件中的端口
```

### 3. 依赖服务连接
```bash
# 检查Redis连接
redis-cli ping

# 检查数据库连接
mysql -h host -u user -p

# 检查网络连通性
curl -I https://api.deepseek.com
```

### 4. 日志查看
```bash
# 查看应用日志
tail -f /opt/airAi/log/server.log

# 查看系统日志
journalctl -u airai -f

# 查看错误日志
grep ERROR /opt/airAi/log/server.log
```

## 📋 检查清单

部署前检查：
- [ ] 可执行文件已上传并有执行权限
- [ ] 配置文件已正确配置
- [ ] 依赖服务（Redis、数据库）可访问
- [ ] 网络端口已开放
- [ ] 系统资源充足
- [ ] 日志目录可写
- [ ] 服务用户已创建
- [ ] systemd服务文件已配置

部署后验证：
- [ ] 服务正常启动
- [ ] HTTP接口可访问
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] AI服务调用正常
- [ ] 日志正常输出
- [ ] 性能指标正常

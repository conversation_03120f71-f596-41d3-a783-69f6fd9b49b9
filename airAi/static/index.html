<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传识别</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 50px;
        }
        .container {
            max-width: 400px;
            margin: auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        select, input, button {
            margin-top: 10px;
            width: 100%;
            padding: 10px;
            font-size: 16px;
        }
        button {
            background-color: blue;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: darkblue;
        }
        pre {
            text-align: left;
            background: #f4f4f4;
            padding: 10px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>文件上传识别</h2>
        <input type="file" id="fileInput">
        <select id="typeSelect">
            <option value="chatgpt">ChatGPT</option>
            <option value="google">Google</option>
        </select>
        <button id="uploadBtn" onclick="uploadFile()">上传</button>
        <div class="loading" id="loadingSpinner"></div>
        <pre id="response"></pre>
    </div>

    <script>
        function uploadFile() {
            const fileInput = document.getElementById("fileInput");
            const typeSelect = document.getElementById("typeSelect").value;
            const uploadBtn = document.getElementById("uploadBtn");
            const loadingSpinner = document.getElementById("loadingSpinner");
            const responseBox = document.getElementById("response");
            console.log(typeSelect);

            if (!fileInput.files.length) {
                alert("请选择一个文件！");
                return;
            }

            // 显示加载动画 & 禁用按钮
            uploadBtn.disabled = true;
            uploadBtn.textContent = "上传中...";
            loadingSpinner.style.display = "block";

            const formData = new FormData();
            formData.append("file", fileInput.files[0]);
            formData.append("type", typeSelect);

            fetch("http://*************:8889/fileUploadAndDownload/recognition", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log(data.data.resp.name);
                // responseBox.textContent = JSON.stringify(data.data.resp.name, null, 2);
                responseBox.textContent = "bird name : "+ data.data.resp.name;
            })
            .catch(error => {
                responseBox.textContent = "请求失败，请检查服务器！";
            })
            .finally(() => {
                    // 恢复按钮 & 隐藏加载动画
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = "上传";
                    loadingSpinner.style.display = "none";
                });
        }
    </script>
</body>
</html>

# getUserMedia错误解决方案

## 错误描述

在服务器环境中部署WebSocket音频识别应用后，用户点击录音按钮时出现JavaScript错误：
```
Cannot read properties of undefined (reading 'getUserMedia')
```

## 问题根源分析

### 1. 浏览器安全策略
现代浏览器出于安全考虑，限制了在非安全上下文中访问敏感API：
- `navigator.mediaDevices` 只在安全上下文中可用
- 安全上下文要求：HTTPS协议、localhost、127.0.0.1

### 2. API可用性检查缺失
原代码直接调用 `navigator.mediaDevices.getUserMedia()` 而没有检查API是否存在。

### 3. 浏览器兼容性问题
不同浏览器对WebRTC API的支持程度不同，需要兼容性处理。

## 已实施的解决方案

### 1. 增强的API存在性检查

```javascript
function checkAudioSupport() {
    const issues = [];
    
    // 检查基本API支持
    if (!navigator.mediaDevices) {
        issues.push('navigator.mediaDevices 不可用');
    }
    
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        issues.push('getUserMedia API 不支持');
    }
    
    if (!window.MediaRecorder) {
        issues.push('MediaRecorder API 不支持');
    }
    
    // 检查协议安全性
    if (location.protocol !== 'https:' && 
        location.hostname !== 'localhost' && 
        location.hostname !== '127.0.0.1') {
        issues.push('非HTTPS协议可能限制麦克风访问');
    }
    
    return issues;
}
```

### 2. 兼容性处理函数

```javascript
async function getUserMediaCompat(constraints) {
    // 现代浏览器
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        return await navigator.mediaDevices.getUserMedia(constraints);
    }
    
    // 旧版浏览器兼容性处理
    const getUserMedia = navigator.getUserMedia || 
                       navigator.webkitGetUserMedia || 
                       navigator.mozGetUserMedia || 
                       navigator.msGetUserMedia;
    
    if (getUserMedia) {
        return new Promise((resolve, reject) => {
            getUserMedia.call(navigator, constraints, resolve, reject);
        });
    }
    
    throw new Error('此浏览器不支持音频录制功能');
}
```

### 3. 用户友好的错误处理

```javascript
async function startRecording() {
    try {
        // 检查浏览器音频录制支持
        const supportIssues = checkAudioSupport();
        if (supportIssues.length > 0) {
            addResult('❌ 浏览器兼容性问题:', 'error');
            supportIssues.forEach(issue => {
                addResult(`  • ${issue}`, 'error');
            });
            
            // 提供解决方案建议
            addResult('💡 解决方案建议:', 'info');
            if (location.protocol !== 'https:') {
                addResult('  • 使用HTTPS协议访问页面', 'info');
            }
            addResult('  • 使用Chrome、Firefox、Safari等现代浏览器', 'info');
            addResult('  • 确保浏览器版本较新', 'info');
            return;
        }

        // 使用兼容性函数获取媒体流
        const stream = await getUserMediaCompat({
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            }
        });
        
        // 继续录音逻辑...
        
    } catch (error) {
        // 详细的错误处理
        let errorMessage = '录音失败';
        if (error.name === 'NotAllowedError') {
            errorMessage = '麦克风权限被拒绝，请允许访问麦克风';
        } else if (error.name === 'NotFoundError') {
            errorMessage = '未找到麦克风设备';
        } else if (error.name === 'NotSupportedError') {
            errorMessage = '浏览器不支持录音功能';
        } else {
            errorMessage = `录音失败: ${error.message}`;
        }
        
        addResult(`❌ ${errorMessage}`, 'error');
    }
}
```

## 诊断工具

### 1. 浏览器兼容性检查页面
创建了专门的诊断工具：`/test/browser_compatibility.html`

功能包括：
- 基本环境信息检查
- API支持状态检测
- 音频格式兼容性测试
- 实际功能测试
- 解决方案建议

### 2. 快速诊断功能
在主测试页面中添加了快速诊断按钮，可以：
- 检查当前环境状态
- 识别兼容性问题
- 提供即时解决建议

## 服务器配置解决方案

### 1. HTTPS配置（推荐）

#### 使用Let's Encrypt免费证书
```bash
# 安装certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 在Go应用中配置HTTPS
server := &http.Server{
    Addr:    ":8443",
    Handler: router,
}

log.Fatal(server.ListenAndServeTLS(
    "/etc/letsencrypt/live/yourdomain.com/fullchain.pem",
    "/etc/letsencrypt/live/yourdomain.com/privkey.pem",
))
```

#### 使用Nginx反向代理
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket支持
    location /v1/ws/ {
        proxy_pass http://localhost:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 2. 开发环境解决方案

#### 使用localhost
```bash
# 确保使用localhost而不是IP地址
http://localhost:8888/test/websocket_audio_test.html
```

#### Chrome开发者标志（仅开发用）
```bash
google-chrome --unsafely-treat-insecure-origin-as-secure=http://your-ip:8888 --user-data-dir=/tmp/chrome-dev
```

## 测试和验证

### 1. 使用诊断工具
1. 打开兼容性检查页面：`/test/browser_compatibility.html`
2. 执行所有检查项目
3. 根据结果进行相应配置

### 2. 功能测试步骤
1. 打开主测试页面：`/test/websocket_audio_test.html`
2. 点击"快速诊断"按钮
3. 检查所有项目是否通过
4. 尝试连接WebSocket
5. 测试录音功能

### 3. 验证清单
- [ ] 浏览器支持检查通过
- [ ] HTTPS协议配置正确
- [ ] WebSocket连接成功
- [ ] 麦克风权限获取正常
- [ ] 音频录制功能正常
- [ ] 错误处理机制完善

## 常见问题和解决方案

### Q1: 在HTTPS环境下仍然提示不支持
**解决方案**：
- 检查证书是否有效
- 确认浏览器版本是否过旧
- 尝试清除浏览器缓存

### Q2: localhost环境下正常，部署后出错
**解决方案**：
- 配置HTTPS证书
- 检查域名DNS解析
- 确认防火墙设置

### Q3: 移动端浏览器不支持
**解决方案**：
- 确保使用HTTPS
- 测试不同移动浏览器
- 检查移动端权限设置

### Q4: 企业网络环境限制
**解决方案**：
- 联系网络管理员开放相关端口
- 配置企业证书
- 使用企业内部CA证书

## 监控和维护

### 1. 错误监控
```javascript
// 添加全局错误监听
window.addEventListener('error', (event) => {
    if (event.message.includes('getUserMedia')) {
        console.error('getUserMedia error detected:', event);
        // 发送错误报告到服务器
    }
});
```

### 2. 兼容性统计
```javascript
// 收集浏览器兼容性数据
const compatibilityData = {
    userAgent: navigator.userAgent,
    protocol: location.protocol,
    mediaDevicesSupported: !!navigator.mediaDevices,
    getUserMediaSupported: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    mediaRecorderSupported: !!window.MediaRecorder
};

// 发送到服务器进行统计分析
```

通过以上解决方案，可以有效解决 `navigator.mediaDevices` 未定义的问题，确保WebSocket音频识别功能在各种环境下都能正常工作。

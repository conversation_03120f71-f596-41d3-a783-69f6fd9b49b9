# API响应系统迁移指南

本指南帮助开发者从现有的响应系统迁移到新的增强响应系统，该系统支持多语言和详细的错误代码。

## 迁移概述

新的响应系统提供：
- 多语言支持（中文/英文）
- 详细的错误代码分类
- 向后兼容性
- 更好的错误处理

## 迁移步骤

### 1. 现有代码保持不变

**重要**: 现有的API代码无需立即修改，新系统完全向后兼容。

```go
// 这些代码继续正常工作
response.OkWithData(data, c)
response.FailWithMessage("error message", c)
response.NoAuth("unauthorized", c)
```

### 2. 逐步迁移到新系统

#### 成功响应迁移

**旧方式**:
```go
response.OkWithData(data, c)
```

**新方式**:
```go
response.OkWithI18n(c, data)
```

**优势**: 自动支持多语言响应消息

#### 错误响应迁移

**旧方式**:
```go
response.FailWithMessage("Failed to upload", c)
```

**新方式**:
```go
response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
```

**优势**: 
- 具体的错误代码（1400而不是7）
- 自动多语言错误消息
- 更好的客户端错误处理

### 3. 常见迁移模式

#### 文件处理错误

**旧代码**:
```go
file, err := c.FormFile("file")
if err != nil {
    response.FailWithMessage("Failed to upload", c)
    return
}

src, err := file.Open()
if err != nil {
    response.FailWithMessage("Failed to open", c)
    return
}
```

**新代码**:
```go
file, err := c.FormFile("file")
if err != nil {
    response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
    return
}

src, err := file.Open()
if err != nil {
    response.FailWithFileError(c, consts.ERROR_FILE_OPEN, "file_open_failed")
    return
}
```

#### 参数验证错误

**旧代码**:
```go
if text == "" {
    response.FailWithMessage("text is empty", c)
    return
}
```

**新代码**:
```go
if text == "" {
    response.FailWithValidationError(c, "missing_params")
    return
}
```

#### 服务调用错误

**旧代码**:
```go
result, err := service.ProcessAudio(audio)
if err != nil {
    response.FailWithMessage(err.Error(), c)
    return
}
```

**新代码**:
```go
result, err := service.ProcessAudio(audio)
if err != nil {
    response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")
    return
}
```

## 错误代码映射表

### 常用错误场景映射

| 旧错误消息 | 新错误代码 | 新消息键 | 函数 |
|-----------|-----------|---------|------|
| "Failed to upload" | 1400 | "file_upload_failed" | `FailWithFileError` |
| "Failed to open" | 1401 | "file_open_failed" | `FailWithFileError` |
| "text is empty" | 1101 | "missing_params" | `FailWithValidationError` |
| "recognition failure" | 1500 | "asr_failed" | `FailWithASRError` |
| "translation failed" | 1600 | "translate_failed" | `FailWithTranslateError` |
| "语言参数无效" | 1103 | "invalid_language" | `FailWithValidationError` |

## API文档更新

### Swagger注释更新

**旧注释**:
```go
// @Param     file  formData  file true "音频"
// @Success  200   {object}  response.Response{data=airRsq.RealTime,msg=string}
```

**新注释**:
```go
// @Param     file  formData  file true "音频文件"
// @Param     lang  query     string false "响应语言代码 (zh_CN, en)" default(zh_CN)
// @Success  200   {object}  response.Response{data=airRsq.RealTime,msg=string}
```

## 客户端适配

### JavaScript客户端示例

**处理多语言响应**:
```javascript
// 发送请求时指定语言
const response = await fetch('/v1/recognize?lang=en', {
    method: 'POST',
    body: formData
});

const result = await response.json();

// 根据错误代码处理错误
if (result.code !== 0) {
    switch (result.code) {
        case 1400:
            console.error('File upload failed:', result.msg);
            break;
        case 1401:
            console.error('File open failed:', result.msg);
            break;
        case 1500:
            console.error('Speech recognition failed:', result.msg);
            break;
        default:
            console.error('Unknown error:', result.msg);
    }
}
```

### Python客户端示例

```python
import requests

# 发送请求时指定语言
response = requests.post(
    'http://localhost:8080/v1/recognize',
    params={'lang': 'zh_CN'},
    files={'file': open('audio.wav', 'rb')}
)

result = response.json()

# 错误处理
if result['code'] != 0:
    error_handlers = {
        1400: lambda: print(f"文件上传失败: {result['msg']}"),
        1401: lambda: print(f"文件打开失败: {result['msg']}"),
        1500: lambda: print(f"语音识别失败: {result['msg']}"),
    }
    
    handler = error_handlers.get(result['code'])
    if handler:
        handler()
    else:
        print(f"未知错误: {result['msg']}")
```

## 测试更新

### 单元测试迁移

**旧测试**:
```go
func TestRecognize(t *testing.T) {
    // ... 设置测试
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    
    assert.Equal(t, float64(7), response["code"])
    assert.Contains(t, response["msg"], "Failed")
}
```

**新测试**:
```go
func TestRecognize(t *testing.T) {
    // ... 设置测试
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    
    assert.Equal(t, float64(1400), response["code"]) // 具体错误代码
    
    // 测试多语言
    // 中文响应
    assert.Equal(t, "文件上传失败", response["msg"])
    
    // 英文响应（在另一个测试中）
    // assert.Equal(t, "File upload failed", response["msg"])
}
```

## 性能考虑

### 内存使用
- 消息映射表在启动时加载，运行时内存占用很小
- 语言查找是O(1)操作，性能影响可忽略

### 响应时间
- 新系统不会增加显著的响应时间
- 消息查找比字符串拼接更高效

## 最佳实践

### 1. 渐进式迁移
- 优先迁移新开发的API
- 对现有API进行逐步迁移
- 保持向后兼容性

### 2. 错误代码选择
```go
// 好的做法：使用具体的错误代码
response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")

// 避免：使用通用错误代码
response.FailWithI18nCode(c, consts.ERROR_GENERAL, "general_error")
```

### 3. 消息键命名
- 使用描述性的消息键
- 保持一致的命名约定
- 避免硬编码消息文本

### 4. 文档维护
- 更新API文档说明支持的语言
- 记录所有错误代码的含义
- 提供客户端集成示例

## 常见问题

### Q: 现有客户端是否需要修改？
A: 不需要。新系统完全向后兼容，现有客户端可以继续正常工作。

### Q: 如何添加新的语言支持？
A: 在 `airAi/core/i18n/messages.go` 中添加新语言的消息映射表。

### Q: 错误代码会改变吗？
A: 不会。一旦分配的错误代码不会改变，确保客户端兼容性。

### Q: 性能是否会受到影响？
A: 影响很小。消息查找是O(1)操作，比字符串操作更高效。

## 迁移检查清单

- [ ] 了解新的错误代码分类
- [ ] 更新API函数使用新的响应方法
- [ ] 更新Swagger文档注释
- [ ] 添加语言参数支持
- [ ] 更新单元测试
- [ ] 更新客户端错误处理逻辑
- [ ] 验证向后兼容性
- [ ] 更新API文档

## 支持

如有迁移相关问题，请参考：
- [增强API响应系统文档](./ENHANCED_API_RESPONSE_SYSTEM.md)
- 代码示例：`airAi/api/v1/ear/real_time.go`
- 测试用例：`airAi/api/v1/ear/enhanced_response_system_test.go`

# Swagger文档生成问题解决方案

## 问题描述

在执行 `swag init -g main.go -d .,../model` 命令时遇到以下错误：

```
Unknown field 'LeftDelim' in struct literal LeftDelim: "{{",
RightDelim: "}}",
```

## 根本原因分析

### 1. 版本兼容性问题
- **系统安装的swag版本**: v1.16.3
- **项目go.mod中的版本**: v1.8.12 → 已升级到 v1.16.4
- **问题**: 新版本swag生成的代码包含了运行时不支持的字段

### 2. API变更
在swag的新版本中，`LeftDelim` 和 `RightDelim` 字段在 `swag.Spec` 结构体中被移除或改变了使用方式，但生成器仍然会生成这些字段。

## 解决方案

### 方案一：自动化脚本（推荐）

我们创建了 `generate_swagger.sh` 脚本来自动处理这个问题：

```bash
#!/bin/bash
# 1. 生成Swagger文档
swag init -g main.go -d .,../model

# 2. 自动移除兼容性问题字段
sed -i '' '/LeftDelim:/d; /RightDelim:/d' docs/docs.go  # macOS
# sed -i '/LeftDelim:/d; /RightDelim:/d' docs/docs.go    # Linux

# 3. 验证和测试
go build -o /tmp/test_build .
```

**使用方法：**
```bash
chmod +x generate_swagger.sh
./generate_swagger.sh
```

### 方案二：手动修复

1. **正常生成文档：**
   ```bash
   swag init -g main.go -d .,../model
   ```

2. **手动编辑 `docs/docs.go` 文件：**
   
   **修改前：**
   ```go
   var SwaggerInfo = &swag.Spec{
       Version:          "1.0",
       Host:             "localhost:8888",
       BasePath:         "/v1",
       Schemes:          []string{},
       Title:            "AirAi API",
       Description:      "AirAi WebSocket音频识别和翻译API服务",
       InfoInstanceName: "swagger",
       SwaggerTemplate:  docTemplate,
       LeftDelim:        "{{",    // 删除这行
       RightDelim:       "}}",    // 删除这行
   }
   ```

   **修改后：**
   ```go
   var SwaggerInfo = &swag.Spec{
       Version:          "1.0",
       Host:             "localhost:8888",
       BasePath:         "/v1",
       Schemes:          []string{},
       Title:            "AirAi API",
       Description:      "AirAi WebSocket音频识别和翻译API服务",
       InfoInstanceName: "swagger",
       SwaggerTemplate:  docTemplate,
   }
   ```

## 项目配置优化

### 1. 添加了Swagger注释到main.go

```go
// @title           AirAi API
// @version         1.0
// @description     AirAi WebSocket音频识别和翻译API服务
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8888
// @BasePath  /v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
```

### 2. 升级了依赖版本

```bash
go get -u github.com/swaggo/swag/cmd/swag
```

升级的主要依赖：
- `github.com/swaggo/swag`: v1.8.12 → v1.16.4
- 相关依赖也得到了相应升级

## 验证结果

### 1. 生成的文档文件
- ✅ `docs/docs.go` - Go代码文件
- ✅ `docs/swagger.json` - JSON格式API文档
- ✅ `docs/swagger.yaml` - YAML格式API文档

### 2. 包含的API接口
- ✅ WebSocket音频识别接口: `/ws/audioRecognize`
- ✅ 所有现有的REST API接口
- ✅ 正确的认证配置

### 3. 编译测试
- ✅ 无编译错误
- ✅ 无运行时错误

## 访问Swagger UI

启动服务器后，可以通过以下URL访问Swagger文档：

```
http://localhost:8888/v1/swagger/index.html
```

## WebSocket接口测试

WebSocket音频识别接口地址：
```
ws://localhost:8888/v1/ws/audioRecognize
```

可以使用项目中的测试页面进行测试：
```
http://localhost:8888/test/websocket_audio_test.html
```

## 最佳实践建议

### 1. 使用自动化脚本
- 每次需要更新API文档时使用 `./generate_swagger.sh`
- 避免手动执行 `swag init` 命令

### 2. 版本管理
- 定期更新swag版本以获得最新功能
- 在CI/CD中集成文档生成流程

### 3. 文档维护
- 保持API注释的完整性和准确性
- 定期检查生成的文档是否包含所有接口

### 4. 问题预防
- 在升级swag版本后及时测试文档生成
- 保留工作的配置作为备份

## 故障排除

### 如果仍然遇到问题：

1. **清理并重新生成：**
   ```bash
   rm -rf docs/
   ./generate_swagger.sh
   ```

2. **检查Go模块：**
   ```bash
   go mod tidy
   go mod download
   ```

3. **验证swag版本：**
   ```bash
   swag --version
   go list -m github.com/swaggo/swag
   ```

4. **检查语法错误：**
   ```bash
   go build .
   ```

这个解决方案已经在当前项目中验证通过，可以正常生成和使用Swagger文档。

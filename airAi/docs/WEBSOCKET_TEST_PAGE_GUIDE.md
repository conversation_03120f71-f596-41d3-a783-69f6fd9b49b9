# WebSocket音频识别测试页面使用指南

## 概述

本文档介绍了增强版WebSocket音频识别测试页面的功能和使用方法。该测试页面位于 `airAi/test/websocket_audio_test.html`，提供了完整的WebSocket音频识别功能测试。

## 主要功能

### 1. 🔗 WebSocket连接管理
- **连接建立**：自动检测协议（HTTP/HTTPS）并建立相应的WebSocket连接
- **连接状态监控**：实时显示连接状态、连接时间和URL信息
- **自动重连**：连接断开时提供重连功能
- **连接信息显示**：显示WebSocket URL和连接持续时间

### 2. 🎤 实时录音识别
- **麦克风权限管理**：自动请求和管理麦克风访问权限
- **高质量录音**：支持16kHz采样率、单声道、回声消除等专业设置
- **实时数据传输**：录音数据实时分块发送到服务器
- **录音状态监控**：显示录音状态、数据传输统计
- **错误处理**：完善的权限拒绝、设备不可用等错误处理

### 3. 📁 音频文件上传识别
- **多格式支持**：支持MP3、WAV、M4A、AAC、OGG等主流音频格式
- **拖拽上传**：支持文件拖拽到指定区域进行上传
- **文件验证**：自动验证文件类型和大小（最大50MB）
- **音频预览**：提供音频播放器预览选中的文件
- **分块上传**：大文件分块传输，避免内存溢出
- **上传进度**：实时显示上传进度条和状态

### 4. 📊 数据传输监控
- **实时统计**：显示发送数据包数量、字节数、接收消息数
- **活动日志**：记录最近的数据传输活动
- **性能监控**：监控数据传输效率和网络状态
- **统计重置**：支持重置统计数据

### 5. 🎯 识别结果显示
- **分类显示**：区分部分结果、最终结果、翻译结果、错误信息
- **时间戳**：每条结果都带有精确的时间戳
- **颜色编码**：不同类型的结果使用不同颜色标识
- **自动滚动**：新结果自动滚动到可视区域
- **结果限制**：限制显示数量，避免页面过长

## 使用步骤

### 基本使用流程

1. **打开测试页面**
   ```
   http://localhost:8888/test/websocket_audio_test.html
   ```

2. **建立WebSocket连接**
   - 点击"连接WebSocket"按钮
   - 等待连接状态变为"已连接到服务器"

3. **选择音频输入方式**
   
   **方式一：实时录音**
   - 点击"开始录音"按钮
   - 允许浏览器访问麦克风
   - 开始说话，音频数据会实时发送到服务器
   - 点击"停止录音"结束录音

   **方式二：文件上传**
   - 点击文件上传区域或拖拽音频文件
   - 选择支持的音频文件格式
   - 预览音频文件信息和播放
   - 点击"上传并识别"开始处理

4. **查看识别结果**
   - 识别结果会实时显示在结果区域
   - 包括原文识别和翻译结果
   - 可以查看数据传输统计信息

## 功能特性详解

### WebSocket通信协议

```javascript
// 连接URL格式
ws://localhost:8888/v1/ws/audioRecognize  // HTTP环境
wss://domain.com/v1/ws/audioRecognize     // HTTPS环境

// 数据发送格式
- 二进制数据：音频数据块
- 文本消息："stop" - 停止信号
- 文本消息："test message" - 测试消息
```

### 音频参数配置

```javascript
// 录音参数
{
    sampleRate: 16000,        // 采样率16kHz
    channelCount: 1,          // 单声道
    echoCancellation: true,   // 回声消除
    noiseSuppression: true,   // 噪声抑制
    autoGainControl: true     // 自动增益控制
}

// 编码格式
mimeType: 'audio/webm;codecs=opus'  // 首选格式
fallback: 'audio/webm'              // 备用格式
```

### 文件上传机制

```javascript
// 分块上传参数
chunkSize: 64 * 1024  // 64KB per chunk
maxFileSize: 50MB     // 最大文件大小
uploadDelay: 10ms     // 块间延迟

// 支持的文件格式
allowedTypes: [
    'audio/mp3', 'audio/wav', 'audio/m4a', 
    'audio/aac', 'audio/ogg', 'audio/mpeg'
]
```

## 错误处理和故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查服务器是否运行在正确端口
   - 确认防火墙设置允许WebSocket连接
   - 检查浏览器控制台的错误信息

2. **麦克风权限被拒绝**
   - 在浏览器地址栏点击锁图标
   - 允许麦克风访问权限
   - 刷新页面重新尝试

3. **音频文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过50MB
   - 检查WebSocket连接状态

4. **识别结果异常**
   - 检查音频质量和清晰度
   - 确认网络连接稳定
   - 查看服务器日志错误信息

### 调试功能

- **数据传输监控**：实时查看数据包发送和接收情况
- **连接状态显示**：监控WebSocket连接状态变化
- **详细日志记录**：所有操作都有时间戳记录
- **测试消息功能**：发送测试消息验证连接

## 性能优化建议

### 录音优化
- 使用质量较好的麦克风设备
- 在安静环境中进行录音
- 保持适当的说话音量和距离

### 文件上传优化
- 选择合适的音频格式（推荐MP3或WAV）
- 控制文件大小，避免过大文件
- 确保网络连接稳定

### 浏览器兼容性
- 推荐使用Chrome、Firefox、Safari等现代浏览器
- 确保浏览器支持WebRTC和MediaRecorder API
- 启用浏览器的音频权限

## 安全注意事项

1. **权限管理**
   - 仅在需要时授予麦克风权限
   - 定期检查浏览器权限设置

2. **数据隐私**
   - 音频数据仅用于识别测试
   - 不会在客户端存储敏感音频内容

3. **网络安全**
   - 生产环境建议使用HTTPS/WSS
   - 实施适当的访问控制和认证

## 技术实现细节

### 关键JavaScript函数

- `connectWebSocket()` - 建立WebSocket连接
- `startRecording()` - 开始麦克风录音
- `uploadAudioFile()` - 上传音频文件
- `handleRecognitionResult()` - 处理识别结果
- `updateTransferStats()` - 更新传输统计

### CSS样式特性

- 响应式设计，支持移动设备
- 现代化UI界面，用户体验友好
- 状态指示器，清晰的视觉反馈
- 拖拽上传区域，直观的交互设计

这个增强版测试页面提供了完整的WebSocket音频识别功能测试，包括详细的中文注释、错误处理、性能监控和用户友好的界面设计。

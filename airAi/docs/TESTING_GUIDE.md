# WebSocket音频识别测试指南

## 测试环境准备

### 1. 启动服务器
```bash
cd airAi
go run main.go
```

### 2. 确认服务器运行
- 服务器应该在端口8888上运行
- 访问 `http://localhost:8888` 确认服务正常

## 测试步骤

### 第一步：音频录制功能诊断

1. **打开调试工具**
   ```
   http://localhost:8888/test/audio_debug.html
   ```

2. **执行基础检查**
   - 点击"检查浏览器支持" - 确认所有API都支持
   - 点击"检查音频格式支持" - 确认至少有一种格式被支持

3. **测试麦克风访问**
   - 点击"测试麦克风访问"
   - 允许浏览器访问麦克风
   - 确认显示"✅ 麦克风访问成功"

4. **测试音频录制**
   - 点击"测试音频录制"
   - 在录制期间说话或制造声音
   - 确认显示"✅ 录制测试成功"和非零字节数据

### 第二步：WebSocket音频识别测试

1. **打开主测试页面**
   ```
   http://localhost:8888/test/websocket_audio_test.html
   ```

2. **建立WebSocket连接**
   - 点击"连接WebSocket"
   - 确认状态显示"已连接到服务器"

3. **预测试音频功能**
   - 点击"测试音频录制"按钮
   - 确认音频录制功能正常

4. **开始实际录音测试**
   - 点击"开始录音"
   - 允许麦克风权限（如果需要）
   - 开始说话，观察以下指标：
     - 数据传输统计中的"发送数据包"应该递增
     - "发送字节数"应该不断增加且不为0
     - 识别结果区域应该显示音频数据块发送信息

5. **验证数据传输**
   - 确认每个数据块大小不为0字节
   - 观察服务器日志中的音频数据接收信息
   - 检查是否有识别结果返回

## 预期结果

### 正常情况下应该看到：

1. **调试工具输出**
   ```
   [时间] ✅ 麦克风访问成功
   [时间] 音频轨道标签: Default - 内置麦克风
   [时间] 采样率: 16000Hz
   [时间] 声道数: 1
   [时间] 接收到数据: 1234 字节 (总计: 1234 字节)
   [时间] ✅ 录制测试成功，总共接收 5678 字节
   ```

2. **主测试页面输出**
   ```
   🎤 正在请求麦克风权限...
   ✅ 麦克风权限获取成功
   🔧 使用音频格式: audio/webm;codecs=opus
   🎤 MediaRecorder已启动，开始录音...
   📊 生成音频数据块 #1: 1.2 KB (audio/webm;codecs=opus)
   📤 已发送数据块 #1: 1.2 KB
   📊 生成音频数据块 #2: 1.5 KB (audio/webm;codecs=opus)
   📤 已发送数据块 #2: 1.5 KB
   ```

3. **服务器日志输出**
   ```
   INFO    接收到音频数据    {"size": 1234, "type": "binary", "timestamp": "10:30:15.123"}
   DEBUG   音频数据写入成功  {"size": 1234, "format": "binary"}
   ```

## 问题排查

### 问题1：音频数据为0字节

**症状**：
- 录音启动但数据包大小始终为0
- 显示"⚠️ 接收到空音频数据块"

**排查步骤**：
1. 确认麦克风权限已授予
2. 在录音时说话或制造声音
3. 检查麦克风设备是否正常工作
4. 尝试不同的浏览器
5. 确认在HTTPS环境下测试（如果可能）

**解决方案**：
- 使用调试工具检查音频格式支持
- 增加录音音量
- 检查系统麦克风设置

### 问题2：WebSocket连接失败

**症状**：
- 显示"❌ WebSocket连接错误"
- 无法建立连接

**排查步骤**：
1. 确认服务器正在运行
2. 检查端口8888是否可访问
3. 查看浏览器控制台错误信息
4. 检查防火墙设置

### 问题3：麦克风权限被拒绝

**症状**：
- 显示"❌ 麦克风权限被拒绝"

**解决方案**：
1. 点击浏览器地址栏的锁图标
2. 允许麦克风访问权限
3. 刷新页面重新尝试

### 问题4：浏览器不支持

**症状**：
- 显示"❌ 浏览器不支持录音功能"

**解决方案**：
- 使用Chrome、Firefox、Safari等现代浏览器
- 确保浏览器版本较新
- 在HTTPS环境下测试

## 性能验证

### 数据传输性能
- 每秒应该发送1-2个数据包
- 每个数据包大小应该在1-5KB之间
- 总传输速率应该在8-32 Kbps范围内

### 识别延迟
- 音频数据应该实时发送到服务器
- 服务器应该能够接收并处理音频数据
- 识别结果应该在合理时间内返回

## 调试技巧

### 1. 浏览器开发者工具
- 打开F12开发者工具
- 查看Console标签页的日志输出
- 检查Network标签页的WebSocket连接

### 2. 服务器日志
- 观察服务器控制台输出
- 查看音频数据接收日志
- 检查错误和警告信息

### 3. 音频设备检查
- 确认系统音频设备正常工作
- 测试其他应用的录音功能
- 检查麦克风音量设置

## 成功标准

测试成功的标准：
- [ ] 浏览器兼容性检查通过
- [ ] 麦克风权限正常获取
- [ ] 音频录制生成非零数据
- [ ] WebSocket连接稳定
- [ ] 音频数据正确传输到服务器
- [ ] 服务器正确接收和处理音频数据
- [ ] 数据传输统计显示正常
- [ ] 无错误或警告信息

完成以上测试后，WebSocket音频识别功能应该能够正常工作，音频数据不再为0字节。

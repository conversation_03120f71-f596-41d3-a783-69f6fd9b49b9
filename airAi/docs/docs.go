// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/v1/audioTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "http 语音翻译",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/chat": {
            "get": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "聊天推送 websocket",
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/checkCode": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "登陆",
                "parameters": [
                    {
                        "description": "account,code",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.CheckCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "login详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/comprehensiveStreamingVoiceTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "综合流式语音翻译 - 返回ASR、翻译和TTS结果",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回综合语音处理结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/concurrentStreamingVoiceTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "并发流式语音翻译（实时ASR+翻译）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回实时翻译结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/feedback": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "意见反馈",
                "parameters": [
                    {
                        "description": "content",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.Feedback"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/fileUploadAndDownload/upload": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ExaFileUploadAndDownload"
                ],
                "summary": "上传文件示例",
                "parameters": [
                    {
                        "type": "file",
                        "description": "上传文件示例",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传文件示例,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.ExaFileResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/getCode": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Exchange"
                ],
                "summary": "发送验证码",
                "parameters": [
                    {
                        "description": "手机号, 密码, 验证码",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.RegisterCode"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回包括用户信息,token,过期时间",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/requst.RegisterCode"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/getUserInfo": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "获取用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/airpods.AirpodsUser"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/googleLogin": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "Google登陆",
                "parameters": [
                    {
                        "description": "phone",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/airpods.AirpodsUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Google登陆",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.Response"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/incrementalStreamingVoiceTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "增量流式语音翻译 - 实时增量处理每个语音段",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回增量语音处理结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/legacyStreamingVoiceTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "传统流式语音翻译（等待完整上传后处理）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回翻译结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/logOut": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "退出登陆",
                "responses": {
                    "200": {
                        "description": "上传文件示例,返回包括文件详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/login": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "登陆",
                "parameters": [
                    {
                        "description": "account",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "login详情",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.LoginResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/meeting": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "http 会议纪要/语音翻译",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/recognize": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "识别语音转文字（支持多语言响应）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "响应语言代码 (zh_CN, en)",
                        "name": "lang",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.RealTime"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/resetPassword": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "重置密码",
                "parameters": [
                    {
                        "description": "phone,code,password",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ResetPassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "绑定手机号",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/speechRecognition": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "语音识别（PfRealtimeV2模型，支持国际化）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "源语言代码，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "default": "zh_CN",
                        "description": "响应语言代码 (zh_CN, en)",
                        "name": "lang",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回语音识别结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/types.TranscriptionData"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/streamTextChat": {
            "post": {
                "description": "根据请求参数中的stream字段决定返回方式：stream=true时返回流式响应，stream=false时返回普通响应",
                "produces": [
                    "application/json",
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "智能文字聊天接口 - 支持流式和非流式响应",
                "parameters": [
                    {
                        "description": "text,speechType,stream",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/streamingSpeechRecognition": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "流式语音识别（PfRealtimeV2模型）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回语音识别结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/streamingVoiceTranslate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "text/event-stream"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "流式语音翻译 - 返回ASR和翻译结果（无TTS音频生成）",
                "parameters": [
                    {
                        "type": "file",
                        "description": "音频",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Server-Sent Events流式返回ASR转录和翻译结果",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/v1/syncChat": {
            "post": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "http 请求对话",
                "parameters": [
                    {
                        "description": "account",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/textChat": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "文字聊天",
                "parameters": [
                    {
                        "description": "text,speechType",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/textMeeting": {
            "post": {
                "description": "根据输入的文字内容生成结构化的会议纪要，支持多种语言输出",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "http 文字转会议纪要（支持多语言）",
                "parameters": [
                    {
                        "type": "string",
                        "description": "会议纪要输出语言，如:zh(中文),en(英文),ja(日语),ko(韩语)等，默认zh",
                        "name": "language",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要转换为会议纪要的文字内容",
                        "name": "text",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/translate": {
            "post": {
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "文字翻译",
                "parameters": [
                    {
                        "type": "string",
                        "description": "需要翻译的文字",
                        "name": "text",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "来源语言，如:zh，默认zh",
                        "name": "sourceLanguage",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "需要翻译的语言，如:en，默认en",
                        "name": "targetLanguages",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/tts": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "阿里文字转语音",
                "parameters": [
                    {
                        "description": "text,speechType,speechRate",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.TtsResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/updateUser": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "description": "phone",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/airpods.AirpodsUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新用户信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/v1/xfTtsChat": {
            "post": {
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Ear"
                ],
                "summary": "讯飞文字转语音",
                "parameters": [
                    {
                        "description": "text,speechType",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/requst.ChatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回消息返回数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/response.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/response.TtsResponse"
                                        },
                                        "msg": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/ws/audio": {
            "get": {
                "description": "前端通过WebSocket发送音频数据，服务端实时识别返回文结果",
                "tags": [
                    "Ear"
                ],
                "summary": "WebSocket音频识别（千问AI）",
                "responses": {}
            }
        },
        "/ws/audioRecognize": {
            "get": {
                "description": "前端通过WebSocket发送音频数据，服务端实时识别并返回文本和翻译结果",
                "tags": [
                    "Ear"
                ],
                "summary": "WebSocket音频识别（千问AI）",
                "responses": {}
            }
        }
    },
    "definitions": {
        "airpods.AirpodsUser": {
            "type": "object",
            "properties": {
                "ID": {
                    "description": "主键ID",
                    "type": "integer"
                },
                "authorityId": {
                    "description": "用户角色ID",
                    "type": "integer"
                },
                "createdAt": {
                    "description": "创建时间",
                    "type": "string"
                },
                "email": {
                    "description": "用户邮箱",
                    "type": "string"
                },
                "enable": {
                    "description": "用户是否被冻结 1正常 2冻结",
                    "type": "integer"
                },
                "headerImg": {
                    "description": "用户头像",
                    "type": "string"
                },
                "nickName": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "originSetting": {
                    "description": "配置",
                    "allOf": [
                        {
                            "$ref": "#/definitions/common.JSONMap"
                        }
                    ]
                },
                "phone": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "updatedAt": {
                    "description": "更新时间",
                    "type": "string"
                },
                "userName": {
                    "description": "用户登录名",
                    "type": "string"
                },
                "uuid": {
                    "description": "用户UUID",
                    "type": "string"
                }
            }
        },
        "common.JSONMap": {
            "type": "object",
            "additionalProperties": true
        },
        "example.ExaFileUploadAndDownload": {
            "type": "object",
            "properties": {
                "key": {
                    "description": "编号",
                    "type": "string"
                },
                "name": {
                    "description": "文件名",
                    "type": "string"
                },
                "tag": {
                    "description": "文件标签",
                    "type": "string"
                },
                "url": {
                    "description": "文件地址",
                    "type": "string"
                }
            }
        },
        "requst.ChatRequest": {
            "type": "object",
            "required": [
                "text"
            ],
            "properties": {
                "speechRate": {
                    "description": "语音速率：0.5-2.0，默认1.0为正常语速",
                    "type": "number",
                    "default": 1
                },
                "speechType": {
                    "description": "deepseek/qwen",
                    "type": "string",
                    "default": "qwen"
                },
                "stream": {
                    "description": "是否启用流式响应：true=流式返回AI响应，false=普通响应",
                    "type": "boolean",
                    "default": false
                },
                "text": {
                    "description": "文本文件",
                    "type": "string"
                }
            }
        },
        "requst.CheckCodeRequest": {
            "type": "object",
            "properties": {
                "account": {
                    "description": "手机号",
                    "type": "string"
                },
                "code": {
                    "description": "验证码",
                    "type": "integer"
                }
            }
        },
        "requst.Feedback": {
            "type": "object",
            "required": [
                "content"
            ],
            "properties": {
                "content": {
                    "description": "意见",
                    "type": "string",
                    "example": "意见"
                }
            }
        },
        "requst.LoginRequest": {
            "type": "object",
            "properties": {
                "account": {
                    "description": "手机号",
                    "type": "string"
                },
                "code": {
                    "description": "使用验证码登陆时填",
                    "type": "integer"
                },
                "password": {
                    "description": "使用密码时填写",
                    "type": "string"
                }
            }
        },
        "requst.RegisterCode": {
            "type": "object",
            "required": [
                "account"
            ],
            "properties": {
                "account": {
                    "description": "Code    int64  ` + "`" + `json:\"code\" example:\"123456\" ` + "`" + `                  // 验证码",
                    "type": "string",
                    "example": "账号"
                }
            }
        },
        "requst.ResetPassword": {
            "type": "object",
            "required": [
                "code",
                "password",
                "phone"
            ],
            "properties": {
                "code": {
                    "description": "验证码",
                    "type": "integer",
                    "example": 123456
                },
                "password": {
                    "description": "密码",
                    "type": "string"
                },
                "phone": {
                    "description": "手机号",
                    "type": "string",
                    "example": "手机号"
                }
            }
        },
        "response.ExaFileResponse": {
            "type": "object",
            "properties": {
                "file": {
                    "$ref": "#/definitions/example.ExaFileUploadAndDownload"
                }
            }
        },
        "response.LoginResponse": {
            "type": "object",
            "properties": {
                "expiresAt": {
                    "description": "token过期时间",
                    "type": "integer"
                },
                "token": {
                    "description": "登陆token",
                    "type": "string"
                },
                "user": {
                    "description": "用户",
                    "allOf": [
                        {
                            "$ref": "#/definitions/response.User"
                        }
                    ]
                }
            }
        },
        "response.RealTime": {
            "type": "object",
            "properties": {
                "text": {
                    "description": "文本",
                    "type": "string"
                }
            }
        },
        "response.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "msg": {
                    "type": "string"
                }
            }
        },
        "response.TtsResponse": {
            "type": "object",
            "properties": {
                "output": {
                    "description": "输出字节",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "response.User": {
            "type": "object",
            "properties": {
                "email": {
                    "description": "用户邮箱",
                    "type": "string"
                },
                "enable": {
                    "description": "用户",
                    "type": "integer"
                },
                "headerImg": {
                    "description": "用户头像",
                    "type": "string"
                },
                "nickName": {
                    "description": "用户昵称",
                    "type": "string"
                },
                "phone": {
                    "description": "用户手机号",
                    "type": "string"
                },
                "userName": {
                    "description": "用户登录名",
                    "type": "string"
                },
                "uuid": {
                    "description": "用户UUID",
                    "type": "string"
                }
            }
        },
        "types.TranscriptionData": {
            "type": "object",
            "properties": {
                "language": {
                    "type": "string"
                },
                "texts": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "words": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/types.Word"
                    }
                }
            }
        },
        "types.Word": {
            "type": "object",
            "properties": {
                "end": {
                    "type": "number"
                },
                "num": {
                    "type": "integer"
                },
                "start": {
                    "type": "number"
                },
                "text": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8888",
	BasePath:         "/v1",
	Schemes:          []string{},
	Title:            "AirAi API",
	Description:      "AirAi WebSocket音频识别和翻译API服务",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}

# 服务器部署和HTTPS配置指南

## 问题背景

在生产环境中部署WebSocket音频识别应用时，经常遇到 `navigator.mediaDevices` 为 `undefined` 的错误。这主要是由于现代浏览器的安全策略限制导致的。

## 浏览器安全策略

### 1. HTTPS要求
现代浏览器（Chrome 47+, Firefox 55+, Safari 11+）要求在以下情况下使用HTTPS才能访问麦克风：
- 非localhost域名
- 非127.0.0.1地址
- 生产环境部署

### 2. 安全上下文要求
`navigator.mediaDevices` 只在安全上下文（Secure Context）中可用：
- HTTPS协议
- localhost (HTTP也可以)
- 127.0.0.1 (HTTP也可以)
- file:// 协议

## 解决方案

### 方案一：配置HTTPS (推荐)

#### 1. 使用Let's Encrypt免费证书

```bash
# 安装certbot
sudo apt-get update
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 证书文件位置
# /etc/letsencrypt/live/yourdomain.com/fullchain.pem
# /etc/letsencrypt/live/yourdomain.com/privkey.pem
```

#### 2. 修改Go应用支持HTTPS

```go
// main.go
package main

import (
    "crypto/tls"
    "log"
    "net/http"
)

func main() {
    // 配置TLS
    tlsConfig := &tls.Config{
        MinVersion: tls.VersionTLS12,
    }
    
    server := &http.Server{
        Addr:      ":8443", // HTTPS端口
        Handler:   router,  // 你的路由器
        TLSConfig: tlsConfig,
    }
    
    // 启动HTTPS服务器
    log.Println("Starting HTTPS server on :8443")
    log.Fatal(server.ListenAndServeTLS(
        "/etc/letsencrypt/live/yourdomain.com/fullchain.pem",
        "/etc/letsencrypt/live/yourdomain.com/privkey.pem",
    ))
}
```

#### 3. 自动HTTP重定向到HTTPS

```go
// 添加HTTP到HTTPS重定向
go func() {
    log.Println("Starting HTTP redirect server on :8080")
    log.Fatal(http.ListenAndServe(":8080", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        http.Redirect(w, r, "https://"+r.Host+r.URL.String(), http.StatusMovedPermanently)
    })))
}()
```

### 方案二：使用Nginx反向代理

#### 1. 安装和配置Nginx

```bash
# 安装Nginx
sudo apt-get install nginx

# 配置文件 /etc/nginx/sites-available/airai
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://localhost:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket支持
    location /v1/ws/ {
        proxy_pass http://localhost:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

#### 2. 启用配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/airai /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 方案三：开发环境解决方案

#### 1. 使用localhost

```bash
# 确保使用localhost而不是IP地址
http://localhost:8888/test/websocket_audio_test.html
```

#### 2. Chrome开发者标志

```bash
# 启动Chrome时添加标志（仅用于开发）
google-chrome --unsafely-treat-insecure-origin-as-secure=http://your-ip:8888 --user-data-dir=/tmp/chrome-dev
```

#### 3. 自签名证书（开发用）

```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 在Go应用中使用
server.ListenAndServeTLS("cert.pem", "key.pem")
```

## 应用代码修改

### 1. 增强的兼容性检查

已在测试页面中添加了完整的兼容性检查：

```javascript
function checkAudioSupport() {
    const issues = [];
    
    if (!navigator.mediaDevices) {
        issues.push('navigator.mediaDevices 不可用');
    }
    
    if (location.protocol !== 'https:' && 
        location.hostname !== 'localhost' && 
        location.hostname !== '127.0.0.1') {
        issues.push('非HTTPS协议可能限制麦克风访问');
    }
    
    return issues;
}
```

### 2. 降级处理

```javascript
async function getUserMediaCompat(constraints) {
    // 现代浏览器
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        return await navigator.mediaDevices.getUserMedia(constraints);
    }
    
    // 旧版浏览器兼容性处理
    const getUserMedia = navigator.getUserMedia || 
                       navigator.webkitGetUserMedia || 
                       navigator.mozGetUserMedia || 
                       navigator.msGetUserMedia;
    
    if (getUserMedia) {
        return new Promise((resolve, reject) => {
            getUserMedia.call(navigator, constraints, resolve, reject);
        });
    }
    
    throw new Error('此浏览器不支持音频录制功能');
}
```

## 部署检查清单

### 部署前检查
- [ ] 域名DNS解析正确
- [ ] SSL证书有效且未过期
- [ ] 防火墙开放443端口
- [ ] Go应用正常运行在内网端口
- [ ] Nginx配置正确且重启

### 功能测试
- [ ] HTTPS访问正常
- [ ] WebSocket连接成功
- [ ] 麦克风权限获取正常
- [ ] 音频录制功能正常
- [ ] 数据传输正常

### 浏览器测试
- [ ] Chrome浏览器测试通过
- [ ] Firefox浏览器测试通过
- [ ] Safari浏览器测试通过
- [ ] 移动端浏览器测试通过

## 故障排除

### 1. 证书问题
```bash
# 检查证书有效性
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/fullchain.pem -text -noout

# 检查证书过期时间
openssl x509 -in /etc/letsencrypt/live/yourdomain.com/fullchain.pem -enddate -noout
```

### 2. Nginx配置问题
```bash
# 检查Nginx配置语法
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看访问日志
sudo tail -f /var/log/nginx/access.log
```

### 3. 防火墙问题
```bash
# 检查防火墙状态
sudo ufw status

# 开放HTTPS端口
sudo ufw allow 443/tcp
sudo ufw allow 80/tcp
```

### 4. 浏览器调试
- 打开开发者工具 (F12)
- 查看Console标签页的错误信息
- 检查Network标签页的请求状态
- 查看Security标签页的证书信息

## 监控和维护

### 1. 证书自动续期
```bash
# 添加crontab任务
sudo crontab -e

# 每月1号凌晨2点检查并续期证书
0 2 1 * * /usr/bin/certbot renew --quiet && /usr/bin/systemctl reload nginx
```

### 2. 日志监控
```bash
# 监控应用日志
tail -f /var/log/airai/app.log

# 监控Nginx访问日志
tail -f /var/log/nginx/access.log | grep "ws/audioRecognize"
```

### 3. 性能监控
- 监控SSL握手时间
- 监控WebSocket连接数
- 监控音频数据传输量
- 监控服务器资源使用情况

通过以上配置，可以确保WebSocket音频识别应用在生产环境中正常工作，解决 `navigator.mediaDevices` 未定义的问题。

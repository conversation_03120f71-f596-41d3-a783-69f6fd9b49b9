# Qwen API Key Management

## Overview

This document describes the centralized API key management system for Qwen services, which replaces the previous hardcoded API key approach with a flexible, configurable solution.

## Changes Made

### 1. Centralized Configuration Structure

Created a comprehensive configuration structure in `config/qwen.go`:

```go
type QwenConfig struct {
    APIKey      string                `mapstructure:"api-key" json:"api-key" yaml:"api-key"`
    BaseURL     string                `mapstructure:"base-url" json:"base-url" yaml:"base-url"`
    DefaultModel string               `mapstructure:"default-model" json:"default-model" yaml:"default-model"`
    ASR         QwenASRConfig         `mapstructure:"asr" json:"asr" yaml:"asr"`
    Translation QwenTranslationConfig `mapstructure:"translation" json:"translation" yaml:"translation"`
    TTS         QwenTTSConfig         `mapstructure:"tts" json:"tts" yaml:"tts"`
}
```

### 2. Configuration Helper Functions

Created `other_api/qwen/config_helper.go` with utility functions:

- `GetAPIKey()` - Retrieves API key with priority: Environment Variable > Config File > Default
- `GetBaseURL()` - Retrieves base URL with fallback logic
- `GetASRConfig()` - Returns ASR-specific configuration with defaults
- `GetTranslationConfig()` - Returns translation configuration with defaults
- `GetTTSConfig()` - Returns TTS configuration with defaults
- `ValidateConfig()` - Validates that required configuration is present

### 3. Updated Configuration Files

#### config.yaml
Added comprehensive Qwen configuration section:

```yaml
qwen:
    api-key: sk-130c858aa2a345949409d91ff45e0367
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1/
    default-model: qwen-plus
    
    asr:
        default-model: paraformer-realtime-v2
        translation-model: gummy-realtime-v1
        default-format: mp3
        default-source-language: zh
        default-target-languages:
            - en
            - ja
        connection-timeout: 30
        max-reconnect-attempts: 3
    
    translation:
        default-model: qwen-plus
        default-source-language: zh
        default-target-language: en
        request-timeout: 30
    
    tts:
        default-model: sambert-zhichu-v1
        default-voice: zhichu
        default-speech-rate: 1.0
        default-format: mp3
        request-timeout: 30
```

### 4. Updated Code Files

#### Files Modified:
- `other_api/qwen/api.go` - Removed hardcoded constants, updated `NewQwenClient()`
- `other_api/qwen/sambert.go` - Updated to use `GetAPIKey()`
- `api/v1/ear/websocket_handler.go` - Updated to use centralized configuration
- `config/config.go` - Added QwenConfig to main Server struct

## Usage

### 1. Environment Variable (Recommended for Production)

Set the API key as an environment variable:

```bash
export QWEN_API_KEY="your-actual-api-key-here"
export QWEN_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1/"
```

### 2. Configuration File

Update the `config.yaml` file:

```yaml
qwen:
    api-key: "your-actual-api-key-here"
    base-url: "https://dashscope.aliyuncs.com/compatible-mode/v1/"
```

### 3. In Code

Use the helper functions to access configuration:

```go
import "airAi/other_api/qwen"

// Get API key
apiKey := qwen.GetAPIKey()

// Get ASR configuration
asrConfig := qwen.GetASRConfig()

// Validate configuration
if err := qwen.ValidateConfig(); err != nil {
    log.Fatal("Qwen configuration error:", err)
}
```

## Priority Order

The configuration system follows this priority order:

1. **Environment Variables** (highest priority)
   - `QWEN_API_KEY`
   - `QWEN_BASE_URL`

2. **Configuration File** (config.yaml)
   - `qwen.api-key`
   - `qwen.base-url`

3. **Default Values** (lowest priority)
   - Built-in defaults for base URL and other settings

## Benefits

### 1. Security
- API keys can be stored in environment variables instead of code
- No more hardcoded secrets in the repository
- Easy to rotate API keys without code changes

### 2. Flexibility
- Different configurations for different environments (dev, staging, prod)
- Easy to override settings per deployment
- Centralized configuration management

### 3. Maintainability
- Single source of truth for all Qwen-related configuration
- Easy to add new configuration options
- Consistent configuration patterns across the application

### 4. Error Handling
- Proper validation of required configuration
- Clear error messages when configuration is missing
- Graceful fallbacks to default values

## Migration Guide

### For Developers

1. **Remove any hardcoded API keys** from your code
2. **Use the helper functions** instead:
   ```go
   // Old way (don't do this)
   apiKey := "sk-130c858aa2a345949409d91ff45e0367"
   
   // New way
   apiKey := qwen.GetAPIKey()
   ```

3. **Set environment variables** for local development:
   ```bash
   export QWEN_API_KEY="your-dev-api-key"
   ```

### For Deployment

1. **Set environment variables** in your deployment environment
2. **Update config.yaml** if using file-based configuration
3. **Verify configuration** using the validation functions

## Error Handling

The system provides clear error messages for common configuration issues:

- **Missing API Key**: "Qwen API key is missing. Please set QWEN_API_KEY environment variable or configure it in config.yaml"
- **Missing Base URL**: "Qwen base URL is missing. Please set QWEN_BASE_URL environment variable or configure it in config.yaml"

## Testing

To test the configuration system:

1. **Unit Tests**: Use the validation functions to ensure configuration is loaded correctly
2. **Integration Tests**: Test with different configuration sources (env vars, config file)
3. **Manual Testing**: Verify API calls work with the new configuration system

## Future Enhancements

Potential improvements for the future:

1. **Configuration Encryption**: Encrypt sensitive configuration values
2. **Dynamic Configuration**: Support for runtime configuration updates
3. **Configuration Validation**: More comprehensive validation rules
4. **Configuration Templates**: Pre-defined configuration templates for different use cases

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Check environment variable: `echo $QWEN_API_KEY`
   - Verify config.yaml has the correct key
   - Ensure the application has access to environment variables

2. **Configuration Not Loading**
   - Verify config.yaml syntax is correct
   - Check file permissions
   - Ensure the config file is in the correct location

3. **Build Errors**
   - Run `go mod tidy` to ensure dependencies are correct
   - Check import paths are correct
   - Verify all files compile individually

### Debug Commands

```bash
# Check environment variables
env | grep QWEN

# Validate YAML syntax
yamllint config.yaml

# Test compilation
go build ./other_api/qwen/
go build ./api/v1/ear/
go build .
```

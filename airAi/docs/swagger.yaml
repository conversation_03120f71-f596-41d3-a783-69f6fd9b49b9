basePath: /v1
definitions:
  airpods.AirpodsUser:
    properties:
      ID:
        description: 主键ID
        type: integer
      authorityId:
        description: 用户角色ID
        type: integer
      createdAt:
        description: 创建时间
        type: string
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户是否被冻结 1正常 2冻结
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      originSetting:
        allOf:
        - $ref: '#/definitions/common.JSONMap'
        description: 配置
      phone:
        description: 用户手机号
        type: string
      updatedAt:
        description: 更新时间
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
  common.JSONMap:
    additionalProperties: true
    type: object
  example.ExaFileUploadAndDownload:
    properties:
      key:
        description: 编号
        type: string
      name:
        description: 文件名
        type: string
      tag:
        description: 文件标签
        type: string
      url:
        description: 文件地址
        type: string
    type: object
  requst.ChatRequest:
    properties:
      speechRate:
        default: 1
        description: 语音速率：0.5-2.0，默认1.0为正常语速
        type: number
      speechType:
        default: qwen
        description: deepseek/qwen
        type: string
      stream:
        default: false
        description: 是否启用流式响应：true=流式返回AI响应，false=普通响应
        type: boolean
      text:
        description: 文本文件
        type: string
    required:
    - text
    type: object
  requst.CheckCodeRequest:
    properties:
      account:
        description: 手机号
        type: string
      code:
        description: 验证码
        type: integer
    type: object
  requst.Feedback:
    properties:
      content:
        description: 意见
        example: 意见
        type: string
    required:
    - content
    type: object
  requst.LoginRequest:
    properties:
      account:
        description: 手机号
        type: string
      code:
        description: 使用验证码登陆时填
        type: integer
      password:
        description: 使用密码时填写
        type: string
    type: object
  requst.RegisterCode:
    properties:
      account:
        description: Code    int64  `json:"code" example:"123456" `                  //
          验证码
        example: 账号
        type: string
    required:
    - account
    type: object
  requst.ResetPassword:
    properties:
      code:
        description: 验证码
        example: 123456
        type: integer
      password:
        description: 密码
        type: string
      phone:
        description: 手机号
        example: 手机号
        type: string
    required:
    - code
    - password
    - phone
    type: object
  response.ExaFileResponse:
    properties:
      file:
        $ref: '#/definitions/example.ExaFileUploadAndDownload'
    type: object
  response.LoginResponse:
    properties:
      expiresAt:
        description: token过期时间
        type: integer
      token:
        description: 登陆token
        type: string
      user:
        allOf:
        - $ref: '#/definitions/response.User'
        description: 用户
    type: object
  response.RealTime:
    properties:
      text:
        description: 文本
        type: string
    type: object
  response.Response:
    properties:
      code:
        type: integer
      data: {}
      msg:
        type: string
    type: object
  response.TtsResponse:
    properties:
      output:
        description: 输出字节
        items:
          type: integer
        type: array
    type: object
  response.User:
    properties:
      email:
        description: 用户邮箱
        type: string
      enable:
        description: 用户
        type: integer
      headerImg:
        description: 用户头像
        type: string
      nickName:
        description: 用户昵称
        type: string
      phone:
        description: 用户手机号
        type: string
      userName:
        description: 用户登录名
        type: string
      uuid:
        description: 用户UUID
        type: string
    type: object
  types.TranscriptionData:
    properties:
      language:
        type: string
      texts:
        additionalProperties:
          type: string
        type: object
      words:
        items:
          $ref: '#/definitions/types.Word'
        type: array
    type: object
  types.Word:
    properties:
      end:
        type: number
      num:
        type: integer
      start:
        type: number
      text:
        type: string
    type: object
host: localhost:8888
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: AirAi WebSocket音频识别和翻译API服务
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: AirAi API
  version: "1.0"
paths:
  /v1/audioTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: http 语音翻译
      tags:
      - Ear
  /v1/chat:
    get:
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      summary: 聊天推送 websocket
      tags:
      - Ear
  /v1/checkCode:
    post:
      consumes:
      - application/json
      parameters:
      - description: account,code
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.CheckCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: login详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 登陆
      tags:
      - Ear
  /v1/comprehensiveStreamingVoiceTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回综合语音处理结果
          schema:
            type: string
      summary: 综合流式语音翻译 - 返回ASR、翻译和TTS结果
      tags:
      - Ear
  /v1/concurrentStreamingVoiceTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回实时翻译结果
          schema:
            type: string
      summary: 并发流式语音翻译（实时ASR+翻译）
      tags:
      - Ear
  /v1/feedback:
    post:
      consumes:
      - application/json
      parameters:
      - description: content
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.Feedback'
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 意见反馈
      tags:
      - Ear
  /v1/fileUploadAndDownload/upload:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 上传文件示例
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传文件示例,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.ExaFileResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 上传文件示例
      tags:
      - ExaFileUploadAndDownload
  /v1/getCode:
    post:
      parameters:
      - description: 手机号, 密码, 验证码
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.RegisterCode'
      produces:
      - application/json
      responses:
        "200":
          description: 返回包括用户信息,token,过期时间
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/requst.RegisterCode'
                msg:
                  type: string
              type: object
      summary: 发送验证码
      tags:
      - Exchange
  /v1/getUserInfo:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 获取用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/airpods.AirpodsUser'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 获取用户信息
      tags:
      - Ear
  /v1/googleLogin:
    post:
      consumes:
      - application/json
      parameters:
      - description: phone
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/airpods.AirpodsUser'
      produces:
      - application/json
      responses:
        "200":
          description: Google登陆
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.Response'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: Google登陆
      tags:
      - Ear
  /v1/incrementalStreamingVoiceTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回增量语音处理结果
          schema:
            type: string
      summary: 增量流式语音翻译 - 实时增量处理每个语音段
      tags:
      - Ear
  /v1/legacyStreamingVoiceTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回翻译结果
          schema:
            type: string
      summary: 传统流式语音翻译（等待完整上传后处理）
      tags:
      - Ear
  /v1/logOut:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: 上传文件示例,返回包括文件详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 退出登陆
      tags:
      - Ear
  /v1/login:
    post:
      consumes:
      - application/json
      parameters:
      - description: account
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: login详情
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.LoginResponse'
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 登陆
      tags:
      - Ear
  /v1/meeting:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: http 会议纪要/语音翻译
      tags:
      - Ear
  /v1/recognize:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频文件
        in: formData
        name: file
        required: true
        type: file
      - default: zh_CN
        description: 响应语言代码 (zh_CN, en)
        in: query
        name: lang
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.RealTime'
                msg:
                  type: string
              type: object
      summary: 识别语音转文字（支持多语言响应）
      tags:
      - Ear
  /v1/resetPassword:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: phone,code,password
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ResetPassword'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定手机号
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 重置密码
      tags:
      - Ear
  /v1/speechRecognition:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频文件
        in: formData
        name: file
        required: true
        type: file
      - description: 源语言代码，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - default: zh_CN
        description: 响应语言代码 (zh_CN, en)
        in: query
        name: lang
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回语音识别结果
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/types.TranscriptionData'
                msg:
                  type: string
              type: object
      summary: 语音识别（PfRealtimeV2模型，支持国际化）
      tags:
      - Ear
  /v1/streamTextChat:
    post:
      description: 根据请求参数中的stream字段决定返回方式：stream=true时返回流式响应，stream=false时返回普通响应
      parameters:
      - description: text,speechType,stream
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      - text/event-stream
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      summary: 智能文字聊天接口 - 支持流式和非流式响应
      tags:
      - Ear
  /v1/streamingSpeechRecognition:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频文件
        in: formData
        name: file
        required: true
        type: file
      - description: 源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回语音识别结果
          schema:
            type: string
      summary: 流式语音识别（PfRealtimeV2模型）
      tags:
      - Ear
  /v1/streamingVoiceTranslate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 音频
        in: formData
        name: file
        required: true
        type: file
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - text/event-stream
      responses:
        "200":
          description: Server-Sent Events流式返回ASR转录和翻译结果
          schema:
            type: string
      summary: 流式语音翻译 - 返回ASR和翻译结果（无TTS音频生成）
      tags:
      - Ear
  /v1/syncChat:
    post:
      consumes:
      - application/json
      parameters:
      - description: account
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: http 请求对话
      tags:
      - Ear
  /v1/textChat:
    post:
      parameters:
      - description: text,speechType
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      summary: 文字聊天
      tags:
      - Ear
  /v1/textMeeting:
    post:
      consumes:
      - multipart/form-data
      description: 根据输入的文字内容生成结构化的会议纪要，支持多种语言输出
      parameters:
      - description: 会议纪要输出语言，如:zh(中文),en(英文),ja(日语),ko(韩语)等，默认zh
        in: formData
        name: language
        type: string
      - description: 需要转换为会议纪要的文字内容
        in: formData
        name: text
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: http 文字转会议纪要（支持多语言）
      tags:
      - Ear
  /v1/translate:
    post:
      consumes:
      - multipart/form-data
      parameters:
      - description: 需要翻译的文字
        in: formData
        name: text
        required: true
        type: string
      - description: 来源语言，如:zh，默认zh
        in: formData
        name: sourceLanguage
        type: string
      - description: 需要翻译的语言，如:en，默认en
        in: formData
        name: targetLanguages
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
                msg:
                  type: string
              type: object
      summary: 文字翻译
      tags:
      - Ear
  /v1/tts:
    post:
      parameters:
      - description: text,speechType,speechRate
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TtsResponse'
                msg:
                  type: string
              type: object
      summary: 阿里文字转语音
      tags:
      - Ear
  /v1/updateUser:
    post:
      consumes:
      - application/json
      parameters:
      - description: phone
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/airpods.AirpodsUser'
      produces:
      - application/json
      responses:
        "200":
          description: 更新用户信息
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  type: string
                msg:
                  type: string
              type: object
      security:
      - ApiKeyAuth: []
      summary: 更新用户信息
      tags:
      - Ear
  /v1/xfTtsChat:
    post:
      parameters:
      - description: text,speechType
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/requst.ChatRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 返回消息返回数据
          schema:
            allOf:
            - $ref: '#/definitions/response.Response'
            - properties:
                data:
                  $ref: '#/definitions/response.TtsResponse'
                msg:
                  type: string
              type: object
      summary: 讯飞文字转语音
      tags:
      - Ear
  /ws/audio:
    get:
      description: 前端通过WebSocket发送音频数据，服务端实时识别返回文结果
      responses: {}
      summary: WebSocket音频识别（千问AI）
      tags:
      - Ear
  /ws/audioRecognize:
    get:
      description: 前端通过WebSocket发送音频数据，服务端实时识别并返回文本和翻译结果
      responses: {}
      summary: WebSocket音频识别（千问AI）
      tags:
      - Ear
securityDefinitions:
  ApiKeyAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"

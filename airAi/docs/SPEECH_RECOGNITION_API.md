# Speech Recognition API Documentation

## Overview

This document describes the implementation of speech recognition API endpoints using the PfRealtimeV2 model. The implementation provides both streaming and non-streaming modes for audio processing with proper error handling and structured response formats.

## API Endpoints

### 1. Non-Streaming Speech Recognition

**Endpoint**: `POST /v1/speechRecognition`

**Description**: Processes complete audio files and returns final ASR results using the PfRealtimeV2 model.

**Parameters**:
- `file` (formData, required): Audio file to be processed
- `sourceLanguage` (formData, optional): Source language code (default: "zh")

**Response Format**:
```json
{
  "code": 0,
  "data": {
    "original_text": "识别的语音文本",
    "words": [...],
    "language": "zh"
  },
  "msg": "success"
}
```

**Example Usage**:
```bash
curl -X POST http://localhost:8888/v1/speechRecognition \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh"
```

### 2. Streaming Speech Recognition

**Endpoint**: `POST /v1/streamingSpeechRecognition`

**Description**: Processes audio files in real-time and returns partial ASR results via Server-Sent Events (SSE).

**Parameters**:
- `file` (formData, required): Audio file to be processed
- `sourceLanguage` (formData, optional): Source language code (default: "zh")

**Response Format**: Server-Sent Events (SSE)
```
Content-Type: text/event-stream

data: {"original_text":"你好","is_partial":true,"is_end":false,"language":"zh"}

data: {"original_text":"你好世界","is_partial":false,"is_end":false,"language":"zh"}

data: {"original_text":"","is_partial":false,"is_end":true,"language":"zh"}
```

**Example Usage**:
```bash
curl -X POST http://localhost:8888/v1/streamingSpeechRecognition \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh" \
  -H "Accept: text/event-stream"
```

## Implementation Details

### Service Layer

#### SpeechRecognition Method
```go
func (s *UserService) SpeechRecognition(sourceLanguage string, audio io.Reader) (*types.TranscriptionData, error)
```
- Uses PfRealtimeV2 model for speech recognition
- Processes complete audio files synchronously
- Returns final transcription results

#### StreamingSpeechRecognition Method
```go
func (s *UserService) StreamingSpeechRecognition(sourceLanguage string, audio io.Reader, resultChan chan<- qwen.StreamingTranscriptionResult) error
```
- Uses PfRealtimeV2 model for real-time speech recognition
- Streams partial results through channels
- Supports concurrent audio processing

### API Layer

#### Non-Streaming Handler
- Handles multipart file uploads
- Validates input parameters
- Returns structured JSON responses
- Includes proper error handling

#### Streaming Handler
- Sets appropriate SSE headers
- Manages WebSocket connections
- Handles client disconnections gracefully
- Provides real-time result streaming

### Response Structure

#### Non-Streaming Response
```go
result := map[string]interface{}{
    "original_text": recognizedText,
    "words":         data.Words,
    "language":      data.Language,
}
```

#### Streaming Response
```go
asrResponse := map[string]interface{}{
    "original_text": result.Text,
    "is_partial":    result.IsPartial,
    "is_end":        result.IsEnd,
    "language":      result.Language,
}
```

## Key Features

### ✅ PfRealtimeV2 Model Integration
- Uses the latest PfRealtimeV2 model for improved accuracy
- Supports multiple languages (zh, en, etc.)
- Optimized for real-time processing

### ✅ Dual Mode Support
- **Non-streaming**: Complete file processing with final results
- **Streaming**: Real-time processing with partial results

### ✅ Structured Response Format
- Clean separation of ASR results using `original_text` field
- Consistent error handling across both modes
- Language identification in responses

### ✅ Robust Error Handling
- Null safety checks for missing data
- Proper timeout management
- Client disconnection handling
- Structured error responses

### ✅ Authentication & Middleware
- Integrated with existing middleware patterns
- Operation recording for audit trails
- CORS support for cross-origin requests

## Router Integration

The endpoints are integrated into the existing router structure:

```go
// 语音识别接口（使用PfRealtimeV2模型）
earRouter.POST("/speechRecognition", airportApi.SpeechRecognition)               // 语音识别（非流式）
earRouter.POST("/streamingSpeechRecognition", airportApi.StreamingSpeechRecognition) // 流式语音识别
```

## Testing

### Test Coverage
- Unit tests for both API endpoints
- Error handling validation
- Response structure verification
- PfRealtimeV2 model usage validation
- Integration tests for router configuration

### Test Results
All tests pass successfully:
- `TestSpeechRecognition`: ✅ PASS
- `TestStreamingSpeechRecognition`: ✅ PASS
- `TestPfRealtimeV2ModelUsage`: ✅ PASS
- `TestAPIEndpointIntegration`: ✅ PASS

## Usage Examples

### JavaScript Client (Streaming)
```javascript
const eventSource = new EventSource('/v1/streamingSpeechRecognition');
eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.original_text) {
        console.log('ASR Result:', data.original_text);
    }
    if (data.is_end) {
        eventSource.close();
    }
};
```

### Python Client (Non-streaming)
```python
import requests

files = {'file': open('audio.wav', 'rb')}
data = {'sourceLanguage': 'zh'}
response = requests.post('http://localhost:8888/v1/speechRecognition', 
                        files=files, data=data)
result = response.json()
print(result['data']['original_text'])
```

## Performance Considerations

- **Latency**: Streaming mode provides lower latency for real-time applications
- **Throughput**: Non-streaming mode better for batch processing
- **Memory**: Efficient channel-based communication for streaming
- **Scalability**: Concurrent processing support for multiple requests

## Future Enhancements

- Support for additional audio formats
- Confidence scores in responses
- Word-level timing information
- Multi-language detection
- Custom vocabulary support

# 增强的API响应系统

本文档描述了新的多语言支持和错误代码系统的实现和使用方法。

## 概述

新的API响应系统提供了以下增强功能：

1. **多语言支持（国际化）**: 支持中文和英文响应消息
2. **详细的错误代码**: 为不同错误场景提供唯一的错误代码
3. **向后兼容性**: 保持与现有API的完全兼容
4. **统一的响应格式**: 标准化的响应结构

## 多语言支持

### 语言参数

客户端可以通过以下方式指定响应语言：

1. **查询参数**: `?lang=zh_CN` 或 `?lang=en`
2. **表单参数**: `lang=zh_CN`
3. **请求头**: `Accept-Language: zh-CN,zh;q=0.9,en;q=0.8`
4. **自定义请求头**: `lang: zh_CN`

### 支持的语言代码

| 语言代码 | 语言名称 | 说明 |
|---------|---------|------|
| `zh_CN`, `zh`, `zh_cn` | 简体中文 | 默认语言 |
| `en`, `en_US`, `en_us` | 英文 | 英语支持 |
| `id`, `id_ID`, `id_id`, `in` | 印尼语 | 印度尼西亚语支持 |
| `hi`, `hi_IN`, `hi_in` | 印地语 | 印度语支持（天城文字） |

### 示例请求

```bash
# 使用查询参数指定语言
curl -X POST "http://localhost:8080/v1/recognize?lang=en" \
  -F "file=@audio.wav"

# 印尼语响应
curl -X POST "http://localhost:8080/v1/recognize?lang=id" \
  -F "file=@audio.wav"

# 印地语响应
curl -X POST "http://localhost:8080/v1/recognize?lang=hi" \
  -F "file=@audio.wav"

# 使用请求头指定语言
curl -X POST "http://localhost:8080/v1/recognize" \
  -H "Accept-Language: hi" \
  -F "file=@audio.wav"
```

## 错误代码系统

### 错误代码分类

| 代码范围 | 类别 | 说明 |
|---------|------|------|
| 0 | 成功 | 操作成功 |
| 1000-1099 | 通用错误 | 服务器内部错误、超时等 |
| 1100-1199 | 参数验证错误 | 参数无效、缺失等 |
| 1200-1299 | 认证授权错误 | 未授权、Token过期等 |
| 1300-1399 | 用户相关错误 | 用户不存在、凭据无效等 |
| 1400-1499 | 文件处理错误 | 文件上传、读取失败等 |
| 1500-1599 | 语音识别错误 | ASR相关错误 |
| 1600-1699 | 翻译错误 | 翻译服务相关错误 |
| 1700-1799 | 语音合成错误 | TTS相关错误 |
| 1800-1899 | 会议纪要错误 | 会议纪要生成相关错误 |
| 1900-1999 | 流式处理错误 | 流式处理、WebSocket错误 |

### 常用错误代码

| 错误代码 | 错误名称 | 中文消息 | 英文消息 |
|---------|---------|---------|---------|
| 0 | SUCCESS | 操作成功 | Success |
| 1100 | ERROR_INVALID_PARAMS | 参数无效 | Invalid parameters |
| 1101 | ERROR_MISSING_PARAMS | 缺少必需参数 | Missing required parameters |
| 1103 | ERROR_INVALID_LANGUAGE | 不支持的语言代码 | Unsupported language code |
| 1400 | ERROR_FILE_UPLOAD | 文件上传失败 | File upload failed |
| 1401 | ERROR_FILE_OPEN | 文件打开失败 | Failed to open file |
| 1500 | ERROR_ASR_FAILED | 语音识别失败 | Speech recognition failed |
| 1600 | ERROR_TRANSLATE_FAILED | 翻译失败 | Translation failed |
| 1700 | ERROR_TTS_FAILED | 语音合成失败 | Text-to-speech failed |

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "data": {
    "original_text": "你好世界",
    "words": [...],
    "language": "zh"
  },
  "msg": "操作成功"
}
```

### 错误响应

```json
{
  "code": 1400,
  "data": {},
  "msg": "文件上传失败"
}
```

### 英文错误响应

```json
{
  "code": 1400,
  "data": {},
  "msg": "File upload failed"
}
```

### 印尼语错误响应

```json
{
  "code": 1400,
  "data": {},
  "msg": "Unggah file gagal"
}
```

### 印地语错误响应

```json
{
  "code": 1400,
  "data": {},
  "msg": "फ़ाइल अपलोड असफल"
}
```

## API使用示例

### 语音识别API

```bash
# 中文响应
curl -X POST "http://localhost:8080/v1/speechRecognition?lang=zh_CN" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh"

# 英文响应
curl -X POST "http://localhost:8080/v1/speechRecognition?lang=en" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh"

# 印尼语响应
curl -X POST "http://localhost:8080/v1/speechRecognition?lang=id" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh"

# 印地语响应
curl -X POST "http://localhost:8080/v1/speechRecognition?lang=hi" \
  -F "file=@audio.wav" \
  -F "sourceLanguage=zh"
```

### 响应示例

**中文响应**:
```json
{
  "code": 0,
  "data": {
    "original_text": "你好，这是一个测试",
    "words": [...],
    "language": "zh"
  },
  "msg": "操作成功"
}
```

**英文响应**:
```json
{
  "code": 0,
  "data": {
    "original_text": "你好，这是一个测试",
    "words": [...],
    "language": "zh"
  },
  "msg": "Success"
}
```

## 开发者指南

### 在API中使用新的响应系统

```go
// 成功响应
response.OkWithI18n(c, data)

// 文件错误响应
response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")

// 参数验证错误响应
response.FailWithValidationError(c, "invalid_params")

// 语音识别错误响应
response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")
```

### 添加新的错误代码

1. 在 `airAi/core/consts/error_codes.go` 中添加新的错误代码常量
2. 在 `ErrorCodeMap` 中添加对应的错误信息
3. 在 `airAi/core/i18n/messages.go` 中添加对应的多语言消息

### 添加新的语言支持

1. 在 `airAi/core/consts/lang.go` 中添加语言代码映射
2. 在 `airAi/core/i18n/messages.go` 中添加对应语言的消息映射表
3. 更新 `LanguageMessages` 映射表

## 向后兼容性

新系统完全向后兼容现有的API：

- 现有的错误代码（0, 7, 8）继续有效
- 现有的响应函数（`response.OkWithData`, `response.FailWithMessage`）继续工作
- 客户端无需修改即可继续使用现有API

## 最佳实践

1. **使用具体的错误代码**: 避免使用通用错误代码，选择最具体的错误代码
2. **提供语言参数**: 在API文档中说明支持的语言参数
3. **错误消息一致性**: 确保错误消息在不同语言中保持一致的含义
4. **日志记录**: 在服务端记录详细的错误信息用于调试
5. **客户端处理**: 客户端应该根据错误代码而不是错误消息来处理错误

## 测试

### 多语言测试

```bash
# 测试中文响应
curl -X POST "http://localhost:8080/v1/recognize?lang=zh_CN" \
  -F "file=@invalid_file.txt"

# 测试英文响应
curl -X POST "http://localhost:8080/v1/recognize?lang=en" \
  -F "file=@invalid_file.txt"
```

### 错误代码测试

验证不同错误场景返回正确的错误代码：

- 文件上传失败: 1400
- 文件打开失败: 1401
- 语音识别失败: 1500
- 参数验证失败: 1100

## 注意事项

1. **性能影响**: 多语言支持对性能影响很小，消息查找是O(1)操作
2. **内存使用**: 消息映射表在启动时加载到内存中
3. **扩展性**: 系统设计支持轻松添加新语言和错误代码
4. **维护性**: 集中管理的消息和错误代码便于维护和更新

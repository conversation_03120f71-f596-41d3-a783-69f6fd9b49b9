# 微信登录集成文档

## 概述

本文档描述了在现有API系统中集成微信OAuth 2.0登录功能的实现。该功能支持微信授权码模式登录，包含完整的用户信息获取、JWT token生成和国际化错误处理。

## 功能特性

- ✅ 微信OAuth 2.0授权码模式登录
- ✅ 自动用户创建和信息同步
- ✅ JWT token集成
- ✅ 多语言错误处理（中文、英文、印尼语、印地语）
- ✅ 安全的state参数验证
- ✅ 灵活的配置管理
- ✅ 完整的错误分类和处理

## API端点

### 1. 获取微信授权URL

**端点**: `POST /api/v1/wechat/auth`

**请求参数**:
```json
{
  "redirect_url": "https://your-app.com/callback",  // 可选，自定义回调地址
  "state": "custom_state_value"                     // 可选，自定义状态参数
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "auth_url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=xxx&redirect_uri=xxx&response_type=code&scope=snsapi_userinfo&state=xxx#wechat_redirect",
    "state": "wechat_1642567890123456789",
    "qr_code_url": ""
  },
  "msg": "操作成功"
}
```

### 2. 微信授权回调处理

**端点**: `GET /api/v1/wechat/callback`

**查询参数**:
- `code`: 微信授权码（必需）
- `state`: 状态参数（可选）
- `error`: 错误代码（用户拒绝授权时）

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "user": {
      "uuid": "550e8400-e29b-41d4-a716-************",
      "userName": "wx_张三_12345678",
      "nickName": "张三",
      "headerImg": "https://thirdwx.qlogo.cn/mmopen/xxx",
      "phone": "",
      "email": "",
      "enable": 1
    },
    "wechat_user": {
      "open_id": "oxxxxxxxxxxxxxxxxxxxxxx",
      "union_id": "oxxxxxxxxxxxxxxxxxxxxxx",
      "nickname": "张三",
      "sex": 1,
      "head_img_url": "https://thirdwx.qlogo.cn/mmopen/xxx",
      "province": "北京",
      "city": "北京",
      "country": "中国"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": 1642567890000,
    "is_new_user": true
  },
  "msg": "微信登录成功"
}
```

### 3. 直接微信登录

**端点**: `POST /api/v1/wechat/login`

**请求参数**:
```json
{
  "code": "021234567890abcdef",  // 必需，微信授权码
  "state": "custom_state"       // 可选，状态参数
}
```

**响应格式**: 与回调处理相同

### 4. 微信授权重定向

**端点**: `GET /api/v1/wechat/redirect`

**查询参数**:
- `state`: 状态参数（可选）

**功能**: 直接重定向到微信授权页面，适用于网页应用

## 配置说明

### config.yaml配置

```yaml
# 微信OAuth配置
wechat:
    # 是否启用微信登录
    enabled: true
    
    # 微信应用ID（从微信开放平台获取）
    app-id: "wx1234567890abcdef"
    
    # 微信应用密钥（从微信开放平台获取）
    app-secret: "1234567890abcdef1234567890abcdef"
    
    # 授权回调地址（需要在微信开放平台配置）
    redirect-url: "https://your-domain.com/api/v1/wechat/callback"
    
    # 微信OAuth授权URL（一般不需要修改）
    auth-url: "https://open.weixin.qq.com/connect/oauth2/authorize"
    
    # 微信获取access_token的URL（一般不需要修改）
    token-url: "https://api.weixin.qq.com/sns/oauth2/access_token"
    
    # 微信获取用户信息的URL（一般不需要修改）
    user-info-url: "https://api.weixin.qq.com/sns/userinfo"
    
    # 授权作用域（snsapi_base: 静默授权，snsapi_userinfo: 用户信息授权）
    scope: "snsapi_userinfo"
```

### 环境变量配置（推荐）

为了安全起见，建议使用环境变量配置敏感信息：

```bash
export WECHAT_APP_ID="wx1234567890abcdef"
export WECHAT_APP_SECRET="1234567890abcdef1234567890abcdef"
export WECHAT_REDIRECT_URL="https://your-domain.com/api/v1/wechat/callback"
```

## 错误处理

### 错误代码说明

| 错误代码 | 错误类型 | 描述 |
|---------|---------|------|
| 1200 | 未授权访问 | 微信登录服务未启用 |
| 1201 | Token无效 | 微信授权码无效或状态参数无效 |
| 2103 | 外部API认证失败 | 微信API调用失败 |

### 多语言错误消息

系统支持中文、英文、印尼语、印地语的错误消息：

```json
// 中文
{
  "code": 1200,
  "msg": "微信登录服务未启用"
}

// 英文
{
  "code": 1200,
  "msg": "WeChat login service is not enabled"
}

// 印尼语
{
  "code": 1200,
  "msg": "Layanan login WeChat tidak diaktifkan"
}

// 印地语
{
  "code": 1200,
  "msg": "WeChat लॉगिन सेवा सक्षम नहीं है"
}
```

## 数据库变更

### 用户表字段扩展

在 `airpods_users` 表中添加了以下字段：

```sql
ALTER TABLE airpods_users ADD COLUMN wechat_open_id VARCHAR(255) DEFAULT NULL COMMENT '微信OpenID';
ALTER TABLE airpods_users ADD COLUMN wechat_union_id VARCHAR(255) DEFAULT NULL COMMENT '微信UnionID';

-- 添加索引
CREATE INDEX idx_wechat_open_id ON airpods_users(wechat_open_id);
CREATE INDEX idx_wechat_union_id ON airpods_users(wechat_union_id);
```

## 安全考虑

1. **State参数验证**: 防止CSRF攻击
2. **Token安全**: 微信App Secret不在客户端暴露
3. **HTTPS要求**: 回调URL必须使用HTTPS
4. **用户信息保护**: 敏感信息不记录日志

## 测试指南

### 1. 单元测试

```bash
# 运行微信登录相关测试
go test ./service/airport -run TestWechatLogin
go test ./api/v1/ear -run TestWechatAuth
```

### 2. 集成测试

使用微信开发者工具或实际微信客户端进行测试：

1. 配置测试环境的微信应用
2. 设置正确的回调URL
3. 测试完整的授权流程

### 3. API测试示例

```bash
# 1. 获取授权URL
curl -X POST "http://localhost:8888/api/v1/wechat/auth" \
  -H "Content-Type: application/json" \
  -d '{"state": "test_state"}'

# 2. 模拟回调（需要真实的微信授权码）
curl "http://localhost:8888/api/v1/wechat/callback?code=021234567890abcdef&state=test_state"

# 3. 直接登录
curl -X POST "http://localhost:8888/api/v1/wechat/login" \
  -H "Content-Type: application/json" \
  -d '{"code": "021234567890abcdef", "state": "test_state"}'
```

## 部署注意事项

1. **微信开放平台配置**: 确保回调URL已在微信开放平台正确配置
2. **域名验证**: 回调域名需要通过微信验证
3. **SSL证书**: 生产环境必须使用有效的SSL证书
4. **配置管理**: 使用环境变量或安全的配置管理系统

## 故障排除

### 常见问题

1. **授权失败**: 检查App ID和App Secret是否正确
2. **回调失败**: 确认回调URL配置正确且可访问
3. **用户信息获取失败**: 检查授权作用域是否为 `snsapi_userinfo`
4. **Token生成失败**: 检查JWT配置是否正确

### 日志查看

```bash
# 查看微信登录相关日志
tail -f logs/server.log | grep -i wechat
```

## 前端集成示例

### JavaScript示例

```javascript
// 1. 获取微信授权URL
async function getWechatAuthUrl() {
    try {
        const response = await fetch('/api/v1/wechat/auth', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept-Language': 'zh_CN'
            },
            body: JSON.stringify({
                state: 'frontend_' + Date.now()
            })
        });

        const result = await response.json();
        if (result.code === 0) {
            // 跳转到微信授权页面
            window.location.href = result.data.auth_url;
        } else {
            console.error('获取授权URL失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 2. 处理微信回调（在回调页面中）
function handleWechatCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');

    if (error) {
        console.error('微信授权失败:', error);
        return;
    }

    if (code) {
        // 发送登录请求
        loginWithWechatCode(code, state);
    }
}

// 3. 使用授权码登录
async function loginWithWechatCode(code, state) {
    try {
        const response = await fetch('/api/v1/wechat/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept-Language': 'zh_CN'
            },
            body: JSON.stringify({
                code: code,
                state: state
            })
        });

        const result = await response.json();
        if (result.code === 0) {
            // 登录成功，保存token
            localStorage.setItem('token', result.data.token);
            localStorage.setItem('user', JSON.stringify(result.data.user));

            // 跳转到主页面
            window.location.href = '/dashboard';
        } else {
            console.error('登录失败:', result.msg);
        }
    } catch (error) {
        console.error('登录请求失败:', error);
    }
}
```

## 版本历史

- v1.0.0: 初始版本，支持基本的微信OAuth登录
- 支持多语言错误处理
- 集成现有的JWT认证系统
- 完整的用户信息同步机制

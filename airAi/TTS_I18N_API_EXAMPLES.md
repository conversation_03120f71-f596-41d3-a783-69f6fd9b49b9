# TTS API 国际化语音速率控制示例

## 概述
TTS API 端点 `/v1/tts` 现在支持语音速率控制和多语言错误消息。当传入无效的 `speechRate` 参数时，API 会根据 `lang` 参数返回相应语言的错误消息。

## API 端点
```
POST /v1/tts
```

## 请求参数
| 参数 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| text | string | 是 | - | 要转换为语音的文本 |
| speechType | string | 否 | "qwen" | 语音合成类型 (qwen/deepseek) |
| speechRate | float64 | 否 | 1.0 | 语音速率 (0.5-2.0) |
| lang | string | 否 | "zh_CN" | 响应语言 (zh_CN/en/id/hi) |

## 成功响应示例

### 正常语音速率请求
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a speech rate test",
    "speechType": "qwen",
    "speechRate": 1.2,
    "lang": "en"
  }'
```

**响应:**
```json
{
  "code": 0,
  "data": {
    "output": [/* 音频字节数据 */]
  },
  "msg": "Success"
}
```

## 错误响应示例

### 1. 语音速率过慢错误 (中文)
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "测试语音速率过慢",
    "speechRate": 0.3,
    "lang": "zh_CN"
  }'
```

**响应:**
```json
{
  "code": 1100,
  "data": {},
  "msg": "语音速率过慢，最小值为0.5"
}
```

### 2. 语音速率过慢错误 (英文)
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Testing speech rate too slow",
    "speechRate": 0.3,
    "lang": "en"
  }'
```

**响应:**
```json
{
  "code": 1100,
  "data": {},
  "msg": "Speech rate too slow, minimum value is 0.5"
}
```

### 3. 语音速率过快错误 (印尼语)
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Menguji kecepatan bicara terlalu cepat",
    "speechRate": 2.5,
    "lang": "id"
  }'
```

**响应:**
```json
{
  "code": 1100,
  "data": {},
  "msg": "Kecepatan bicara terlalu cepat, nilai maksimum 2.0"
}
```

### 4. 语音速率过快错误 (印地语)
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "text": "वाक् गति परीक्षण",
    "speechRate": 2.5,
    "lang": "hi"
  }'
```

**响应:**
```json
{
  "code": 1100,
  "data": {},
  "msg": "वाक् गति बहुत तेज़, अधिकतम मान 2.0 है"
}
```

## 语言参数传递方式

### 1. 查询参数
```bash
curl -X POST "http://localhost:8080/v1/tts?lang=en" \
  -H "Content-Type: application/json" \
  -d '{"text": "Test", "speechRate": 0.3}'
```

### 2. 请求头
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -H "lang: en" \
  -d '{"text": "Test", "speechRate": 0.3}'
```

### 3. Accept-Language 请求头
```bash
curl -X POST http://localhost:8080/v1/tts \
  -H "Content-Type: application/json" \
  -H "Accept-Language: en" \
  -d '{"text": "Test", "speechRate": 0.3}'
```

## 支持的语言代码
- `zh_CN` 或 `zh`: 简体中文
- `en`: 英语
- `id`: 印尼语
- `hi`: 印地语

## 错误代码说明
- `1100`: 参数验证错误 (ERROR_INVALID_PARAMS)
- 具体的语音速率错误会根据语言参数返回相应的本地化消息

## 语音速率范围
- **最小值**: 0.5 (0.5倍速，较慢)
- **最大值**: 2.0 (2倍速，较快)
- **默认值**: 1.0 (正常语速)
- **推荐范围**: 0.7-1.5 (最佳用户体验)

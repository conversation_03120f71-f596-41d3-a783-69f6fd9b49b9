package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"model/common/response"
	"net/http/httptest"

	"airAi/api/v1/ear"

	"github.com/gin-gonic/gin"
)

// APIResponse 表示API响应结构
type APIResponse struct {
	Code int                    `json:"code"`
	Data map[string]interface{} `json:"data"`
	Msg  string                 `json:"msg"`
}

func main() {
	gin.SetMode(gin.TestMode)

	fmt.Println("🌍 多语言API响应演示")
	fmt.Println("====================================================")

	// 演示文件上传错误的多语言响应
	fmt.Println("\n📁 文件上传错误响应:")
	demonstrateFileUploadError()

	// 演示参数验证错误的多语言响应
	fmt.Println("\n✅ 参数验证错误响应:")
	demonstrateValidationError()

	// 演示语言代码标准化
	fmt.Println("\n🔄 语言代码标准化:")
	demonstrateLanguageNormalization()

	// 演示Accept-Language请求头支持
	fmt.Println("\n🌐 Accept-Language请求头支持:")
	demonstrateAcceptLanguageHeader()

	// 演示认证错误的多语言响应
	fmt.Println("\n🔐 认证错误响应:")
	demonstrateAuthenticationErrors()
}

func demonstrateFileUploadError() {
	languages := []struct {
		code string
		name string
	}{
		{"zh_CN", "中文"},
		{"en", "English"},
		{"id", "Bahasa Indonesia"},
		{"hi", "हिन्दी"},
	}

	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 模拟没有文件的请求
		c.Request = httptest.NewRequest("POST", "/v1/recognize?lang="+lang.code, nil)
		c.Request.Header.Set("Content-Type", "multipart/form-data")

		api := &ear.AirportApi{}
		api.Recognize(c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("  %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}
}

func demonstrateValidationError() {
	languages := []struct {
		code string
		name string
	}{
		{"zh_CN", "中文"},
		{"en", "English"},
		{"id", "Bahasa Indonesia"},
		{"hi", "हिन्दी"},
	}

	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 创建空的表单数据
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		writer.WriteField("text", "") // 空文本触发验证错误
		writer.Close()

		c.Request = httptest.NewRequest("POST", "/v1/translate?lang="+lang.code, body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		api := &ear.AirportApi{}
		api.Translate(c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("  %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}
}

func demonstrateLanguageNormalization() {
	testCases := []struct {
		input    string
		expected string
	}{
		{"zh", "中文标准化"},
		{"zh_cn", "中文标准化"},
		{"en_US", "英文标准化"},
		{"en_us", "英文标准化"},
		{"id_ID", "印尼语标准化"},
		{"in", "印尼语传统代码"},
		{"hi_IN", "印地语标准化"},
		{"invalid", "无效语言回退"},
	}

	for _, tc := range testCases {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("POST", "/v1/recognize?lang="+tc.input, nil)
		c.Request.Header.Set("Content-Type", "multipart/form-data")

		api := &ear.AirportApi{}
		api.Recognize(c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("  %s → %s: %s\n", tc.input, tc.expected, resp.Msg)
	}
}

func demonstrateAcceptLanguageHeader() {
	languages := []struct {
		header string
		name   string
	}{
		{"zh-CN", "中文"},
		{"en", "English"},
		{"id", "Bahasa Indonesia"},
		{"hi", "हिन्दी"},
	}

	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("POST", "/v1/recognize", nil)
		c.Request.Header.Set("Content-Type", "multipart/form-data")
		c.Request.Header.Set("Accept-Language", lang.header)

		api := &ear.AirportApi{}
		api.Recognize(c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("  Accept-Language: %s → %s (%s)\n", lang.header, lang.name, resp.Msg)
	}
}

func demonstrateAuthenticationErrors() {
	languages := []struct {
		code string
		name string
	}{
		{"zh_CN", "中文"},
		{"en", "English"},
		{"id", "Bahasa Indonesia"},
		{"hi", "हिन्दी"},
	}

	fmt.Println("  未授权访问错误:")
	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?lang="+lang.code, nil)

		// 使用NoAuth函数测试
		response.NoAuth("test message", c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("    %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}

	fmt.Println("  Token无效错误:")
	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?lang="+lang.code, nil)

		// 使用FailWithAuthError函数测试
		response.FailWithAuthError(c, 1201, "token_invalid")

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("    %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}

	fmt.Println("  Token过期错误:")
	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?lang="+lang.code, nil)

		// 使用FailWithAuthError函数测试
		response.FailWithAuthError(c, 1202, "token_expired")

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("    %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}

	fmt.Println("  余额不足错误:")
	for _, lang := range languages {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("GET", "/?lang="+lang.code, nil)

		// 使用NoInsufficientBalance函数测试
		response.NoInsufficientBalance("test message", c)

		var resp APIResponse
		json.Unmarshal(w.Body.Bytes(), &resp)

		fmt.Printf("    %s (%s): [%d] %s\n", lang.name, lang.code, resp.Code, resp.Msg)
	}
}

// 辅助函数：重复字符串
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}

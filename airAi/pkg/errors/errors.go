package errors

import (
	"fmt"
	"net/http"
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	// 系统错误类型
	ErrorTypeSystem     ErrorType = "system"
	ErrorTypeValidation ErrorType = "validation"
	ErrorTypeAuth       ErrorType = "authentication"
	ErrorTypePermission ErrorType = "permission"
	ErrorTypeNotFound   ErrorType = "not_found"
	ErrorTypeConflict   ErrorType = "conflict"
	ErrorTypeRateLimit  ErrorType = "rate_limit"

	// 业务错误类型
	ErrorTypeASR         ErrorType = "asr"
	ErrorTypeTranslation ErrorType = "translation"
	ErrorTypeTTS         ErrorType = "tts"
	ErrorTypeFile        ErrorType = "file"
	ErrorTypeStream      ErrorType = "stream"
	ErrorTypeMeeting     ErrorType = "meeting"

	// 外部服务错误类型
	ErrorTypeExternal ErrorType = "external_service"
	ErrorTypeNetwork  ErrorType = "network"
	ErrorTypeTimeout  ErrorType = "timeout"
)

// APIError 标准化的API错误结构
type APIError struct {
	Code      int       `json:"code"`
	Message   string    `json:"message"`
	Type      ErrorType `json:"type"`
	Details   string    `json:"details,omitempty"`
	RequestID string    `json:"request_id,omitempty"`
	Timestamp int64     `json:"timestamp,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Type, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// HTTPStatus 返回对应的HTTP状态码
func (e *APIError) HTTPStatus() int {
	switch e.Type {
	case ErrorTypeValidation:
		return http.StatusBadRequest
	case ErrorTypeAuth:
		return http.StatusUnauthorized
	case ErrorTypePermission:
		return http.StatusForbidden
	case ErrorTypeNotFound:
		return http.StatusNotFound
	case ErrorTypeConflict:
		return http.StatusConflict
	case ErrorTypeRateLimit:
		return http.StatusTooManyRequests
	case ErrorTypeTimeout:
		return http.StatusRequestTimeout
	case ErrorTypeExternal, ErrorTypeNetwork:
		return http.StatusBadGateway
	case ErrorTypeSystem:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// NewAPIError 创建新的API错误
func NewAPIError(code int, message string, errorType ErrorType) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Type:    errorType,
	}
}

// NewAPIErrorWithDetails 创建带详细信息的API错误
func NewAPIErrorWithDetails(code int, message string, errorType ErrorType, details string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Type:    errorType,
		Details: details,
	}
}

// 预定义的常用错误

// 系统错误
func NewSystemError(message string) *APIError {
	return NewAPIError(5000, message, ErrorTypeSystem)
}

func NewInternalServerError() *APIError {
	return NewAPIError(5000, "Internal server error", ErrorTypeSystem)
}

// 验证错误
func NewValidationError(message string) *APIError {
	return NewAPIError(4000, message, ErrorTypeValidation)
}

func NewMissingParameterError(param string) *APIError {
	return NewAPIErrorWithDetails(4001, "Missing required parameter", ErrorTypeValidation, param)
}

func NewInvalidParameterError(param string) *APIError {
	return NewAPIErrorWithDetails(4002, "Invalid parameter", ErrorTypeValidation, param)
}

// 认证错误
func NewAuthError(message string) *APIError {
	return NewAPIError(4010, message, ErrorTypeAuth)
}

func NewUnauthorizedError() *APIError {
	return NewAPIError(4010, "Unauthorized", ErrorTypeAuth)
}

func NewTokenExpiredError() *APIError {
	return NewAPIError(4011, "Token expired", ErrorTypeAuth)
}

func NewInvalidTokenError() *APIError {
	return NewAPIError(4012, "Invalid token", ErrorTypeAuth)
}

// 权限错误
func NewPermissionError(message string) *APIError {
	return NewAPIError(4030, message, ErrorTypePermission)
}

func NewForbiddenError() *APIError {
	return NewAPIError(4030, "Forbidden", ErrorTypePermission)
}

// 资源错误
func NewNotFoundError(resource string) *APIError {
	return NewAPIErrorWithDetails(4040, "Resource not found", ErrorTypeNotFound, resource)
}

func NewConflictError(message string) *APIError {
	return NewAPIError(4090, message, ErrorTypeConflict)
}

// 限流错误
func NewRateLimitError() *APIError {
	return NewAPIError(4290, "Rate limit exceeded", ErrorTypeRateLimit)
}

// 文件错误
func NewFileUploadError(details string) *APIError {
	return NewAPIErrorWithDetails(1400, "File upload failed", ErrorTypeFile, details)
}

func NewFileOpenError(details string) *APIError {
	return NewAPIErrorWithDetails(1401, "File open failed", ErrorTypeFile, details)
}

func NewFileProcessError(details string) *APIError {
	return NewAPIErrorWithDetails(1402, "File processing failed", ErrorTypeFile, details)
}

// ASR错误
func NewASRError(details string) *APIError {
	return NewAPIErrorWithDetails(1500, "Speech recognition failed", ErrorTypeASR, details)
}

func NewASRConnectionError() *APIError {
	return NewAPIError(1501, "ASR service connection failed", ErrorTypeASR)
}

func NewASRTimeoutError() *APIError {
	return NewAPIError(1502, "ASR service timeout", ErrorTypeASR)
}

// 翻译错误
func NewTranslationError(details string) *APIError {
	return NewAPIErrorWithDetails(1600, "Translation failed", ErrorTypeTranslation, details)
}

func NewTranslationServiceError() *APIError {
	return NewAPIError(1601, "Translation service unavailable", ErrorTypeTranslation)
}

// TTS错误
func NewTTSError(details string) *APIError {
	return NewAPIErrorWithDetails(1700, "Text-to-speech failed", ErrorTypeTTS, details)
}

func NewTTSServiceError() *APIError {
	return NewAPIError(1701, "TTS service unavailable", ErrorTypeTTS)
}

// 流式处理错误
func NewStreamError(details string) *APIError {
	return NewAPIErrorWithDetails(1800, "Streaming failed", ErrorTypeStream, details)
}

func NewStreamConnectionError() *APIError {
	return NewAPIError(1801, "Stream connection failed", ErrorTypeStream)
}

// 会议错误
func NewMeetingError(details string) *APIError {
	return NewAPIErrorWithDetails(1900, "Meeting processing failed", ErrorTypeMeeting, details)
}

// 外部服务错误
func NewExternalServiceError(service string, details string) *APIError {
	return NewAPIErrorWithDetails(5001, fmt.Sprintf("%s service error", service), ErrorTypeExternal, details)
}

func NewNetworkError(details string) *APIError {
	return NewAPIErrorWithDetails(5002, "Network error", ErrorTypeNetwork, details)
}

func NewTimeoutError(operation string) *APIError {
	return NewAPIErrorWithDetails(5003, "Operation timeout", ErrorTypeTimeout, operation)
}

// WrapError 包装标准错误为API错误
func WrapError(err error, errorType ErrorType, message string) *APIError {
	if err == nil {
		return nil
	}

	// 如果已经是APIError，直接返回
	if apiErr, ok := err.(*APIError); ok {
		return apiErr
	}

	return NewAPIErrorWithDetails(5000, message, errorType, err.Error())
}

// IsAPIError 检查是否为API错误
func IsAPIError(err error) bool {
	_, ok := err.(*APIError)
	return ok
}

// GetAPIError 获取API错误，如果不是则返回nil
func GetAPIError(err error) *APIError {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr
	}
	return nil
}

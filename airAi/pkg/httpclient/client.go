package httpclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// BaseClient 提供统一的HTTP客户端基础功能
// 包含通用的请求处理、错误处理、日志记录和重试机制
type BaseClient struct {
	httpClient *http.Client
	baseURL    string
	timeout    time.Duration
	logger     *zap.Logger
	headers    map[string]string
}

// ClientConfig HTTP客户端配置
type ClientConfig struct {
	BaseURL    string
	Timeout    time.Duration
	Logger     *zap.Logger
	Headers    map[string]string
	MaxRetries int
}

// NewBaseClient 创建新的基础HTTP客户端
func NewBaseClient(config ClientConfig) *BaseClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.Headers == nil {
		config.Headers = make(map[string]string)
	}

	// 设置默认请求头
	if config.Headers["Content-Type"] == "" {
		config.Headers["Content-Type"] = "application/json"
	}
	if config.Headers["User-Agent"] == "" {
		config.Headers["User-Agent"] = "AirAI-Client/1.0"
	}

	return &BaseClient{
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		baseURL: config.BaseURL,
		timeout: config.Timeout,
		logger:  config.Logger,
		headers: config.Headers,
	}
}

// Get 执行GET请求
func (c *BaseClient) Get(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	return c.doRequest(ctx, "GET", path, nil, headers)
}

// Post 执行POST请求
func (c *BaseClient) Post(ctx context.Context, path string, body interface{}, headers map[string]string) (*http.Response, error) {
	return c.doRequest(ctx, "POST", path, body, headers)
}

// Put 执行PUT请求
func (c *BaseClient) Put(ctx context.Context, path string, body interface{}, headers map[string]string) (*http.Response, error) {
	return c.doRequest(ctx, "PUT", path, body, headers)
}

// Delete 执行DELETE请求
func (c *BaseClient) Delete(ctx context.Context, path string, headers map[string]string) (*http.Response, error) {
	return c.doRequest(ctx, "DELETE", path, nil, headers)
}

// PostJSON 执行POST请求并解析JSON响应
func (c *BaseClient) PostJSON(ctx context.Context, path string, body interface{}, result interface{}, headers map[string]string) error {
	resp, err := c.Post(ctx, path, body, headers)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return c.parseJSONResponse(resp, result)
}

// GetJSON 执行GET请求并解析JSON响应
func (c *BaseClient) GetJSON(ctx context.Context, path string, result interface{}, headers map[string]string) error {
	resp, err := c.Get(ctx, path, headers)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	return c.parseJSONResponse(resp, result)
}

// doRequest 执行HTTP请求的核心方法
func (c *BaseClient) doRequest(ctx context.Context, method, path string, body interface{}, headers map[string]string) (*http.Response, error) {
	// 构建完整URL
	url := c.baseURL + path

	// 准备请求体
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := c.marshalBody(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	c.setHeaders(req, headers)

	// 记录请求日志
	c.logRequest(req)

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logError("Request failed", err, map[string]interface{}{
			"method": method,
			"url":    url,
		})
		return nil, fmt.Errorf("request failed: %w", err)
	}

	// 记录响应日志
	c.logResponse(resp)

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return resp, c.handleHTTPError(resp)
	}

	return resp, nil
}

// marshalBody 序列化请求体
func (c *BaseClient) marshalBody(body interface{}) ([]byte, error) {
	switch v := body.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	case io.Reader:
		return io.ReadAll(v)
	default:
		return json.Marshal(v)
	}
}

// setHeaders 设置请求头
func (c *BaseClient) setHeaders(req *http.Request, additionalHeaders map[string]string) {
	// 设置默认请求头
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	// 设置额外的请求头
	for key, value := range additionalHeaders {
		req.Header.Set(key, value)
	}
}

// parseJSONResponse 解析JSON响应
func (c *BaseClient) parseJSONResponse(resp *http.Response, result interface{}) error {
	if result == nil {
		return nil
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if err := json.Unmarshal(body, result); err != nil {
		c.logError("Failed to parse JSON response", err, map[string]interface{}{
			"status_code": resp.StatusCode,
			"body":        string(body),
		})
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return nil
}

// handleHTTPError 处理HTTP错误
func (c *BaseClient) handleHTTPError(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("HTTP %d: failed to read error response", resp.StatusCode)
	}

	c.logError("HTTP error response", nil, map[string]interface{}{
		"status_code": resp.StatusCode,
		"status":      resp.Status,
		"body":        string(body),
	})

	return fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
}

// logRequest 记录请求日志
func (c *BaseClient) logRequest(req *http.Request) {
	if c.logger == nil {
		return
	}

	c.logger.Debug("HTTP request",
		zap.String("method", req.Method),
		zap.String("url", req.URL.String()),
		zap.Any("headers", req.Header),
	)
}

// logResponse 记录响应日志
func (c *BaseClient) logResponse(resp *http.Response) {
	if c.logger == nil {
		return
	}

	c.logger.Debug("HTTP response",
		zap.Int("status_code", resp.StatusCode),
		zap.String("status", resp.Status),
		zap.Any("headers", resp.Header),
	)
}

// logError 记录错误日志
func (c *BaseClient) logError(message string, err error, fields map[string]interface{}) {
	if c.logger == nil {
		return
	}

	zapFields := []zap.Field{zap.String("message", message)}
	if err != nil {
		zapFields = append(zapFields, zap.Error(err))
	}

	for key, value := range fields {
		zapFields = append(zapFields, zap.Any(key, value))
	}

	c.logger.Error("HTTP client error", zapFields...)
}

// SetTimeout 设置请求超时时间
func (c *BaseClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
	c.httpClient.Timeout = timeout
}

// SetHeader 设置默认请求头
func (c *BaseClient) SetHeader(key, value string) {
	c.headers[key] = value
}

// RemoveHeader 移除默认请求头
func (c *BaseClient) RemoveHeader(key string) {
	delete(c.headers, key)
}

// GetBaseURL 获取基础URL
func (c *BaseClient) GetBaseURL() string {
	return c.baseURL
}

// SetBaseURL 设置基础URL
func (c *BaseClient) SetBaseURL(baseURL string) {
	c.baseURL = baseURL
}

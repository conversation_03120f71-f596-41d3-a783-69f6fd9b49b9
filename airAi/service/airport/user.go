package airport

import (
	"airAi/common/requst"
	"airAi/global"
	"airAi/utils"
	"context"
	"errors"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"model/airpods"
)

func (s *UserService) Login(req requst.LoginRequest) (airUser *airpods.AirpodsUser, err error) {
	if req.Code != 0 {
		if !s.CheckCode(req.Account, req.Code) {
			return nil, errors.New("code error")
		}
	}
	var user airpods.AirpodsUser
	if utils.PhoneValidation(req.Account) {
		user.Phone = req.Account
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", req.Account).First(&user).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	if utils.EmailValidation(req.Account) {
		user.Email = req.Account
		err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("email =?", req.Account).First(&user).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	if user.ID > 0 {
		if req.Password != "" {
			if !utils.BcryptCheck(req.Password, user.Password) {
				return nil, errors.New("password error")
			}
		}
	} else {
		user.UUID = uuid.New()
		err = global.GVA_DB.Create(&user).Error
		if err != nil {
			return nil, err
		}
	}

	return &user, nil
}

func (s *UserService) LogOut() {

}

func (s *UserService) BindPhone(uid int64, phone string) error {
	var user airpods.AirpodsUser
	err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("uid=?", uid).First(&user).Error
	if err != nil {
		return err
	}
	global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone =?", phone).First(&user)
	if user.ID > 0 {
		return errors.New("phone exist")
	}
	if user.Phone != "" {
		return errors.New("It's already bound")
	}
	return global.GVA_DB.Model(airpods.AirpodsUser{}).Where("uid=?", uid).Update("phone", phone).Error
}

func (s *UserService) CheckCode(key string, code int64) bool {
	codeStr, err := global.GVA_REDIS.Get(context.Background(), key).Result()
	if err == redis.Nil {
		return false
	}
	if cast.ToInt64(codeStr) != code {
		return false
	}
	return true
}

func (s *UserService) ResetPassword(code int64, phone, password string) error {
	var user airpods.AirpodsUser
	if !s.CheckCode(phone, code) {
		return errors.New("invalid account or code")
	}
	psd := utils.BcryptHash(password)
	global.GVA_DB.Model(airpods.AirpodsUser{}).Where("phone = ?", phone).First(&user)
	if user.ID > 0 {
		user.Password = psd
		return global.GVA_DB.Save(&user).Error
	}
	return errors.New("invalid account")
}

func (s *UserService) UpdateUser(user airpods.AirpodsUser) error {
	return global.GVA_DB.Model(airpods.AirpodsUser{}).Where("id = ?", user.ID).Updates(&user).Error
}

func (s *UserService) GetUserInfoById(uid int64) (airpods.AirpodsUser, error) {
	var user airpods.AirpodsUser
	err := global.GVA_DB.Model(airpods.AirpodsUser{}).Where("id = ?", uid).First(&user).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return airpods.AirpodsUser{}, err
	}
	return user, nil
}

func (s *UserService) Feedback(uid uint, content string) error {
	return global.GVA_DB.Create(&airpods.Feedback{
		Uid:     uid,
		Content: content,
	}).Error
}

package airport

import (
	"errors"
	"fmt"
	"time"

	"airAi/global"
	"model/airpods"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// QuotaService 配额服务
type QuotaService struct{}

// 配额相关常量
const (
	DefaultMonthlyQuota    = 300  // 默认月度配额
	MonthlySubscriptionFee = 70.0 // 月度订阅费用（RMB）
	ExtraQuotaUnit         = 50   // 额外配额单位（50次/单位）
	ExtraQuotaPrice        = 10.0 // 额外配额价格（10RMB/单位）
)

// 使用类型常量
const (
	UsageTypeChat = 1 // 聊天
	UsageTypeASR  = 2 // 语音识别
	UsageTypeTTS  = 3 // 文字转语音
)

// 购买类型常量
const (
	PurchaseTypeExtraQuota          = 1 // 额外配额
	PurchaseTypeMonthlySubscription = 2 // 月度订阅
)

// 支付状态常量
const (
	PaymentStatusPending   = 0 // 待支付
	PaymentStatusPaid      = 1 // 已支付
	PaymentStatusCancelled = 2 // 已取消
	PaymentStatusRefunded  = 3 // 已退款
)

// GetOrCreateUserQuota 获取或创建用户配额记录
func (s *QuotaService) GetOrCreateUserQuota(userID uint) (*airpods.Quota, error) {
	var quota airpods.Quota

	// 尝试获取现有配额记录
	err := global.GVA_DB.Where("uid = ?", userID).First(&quota).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新的配额记录
			quota = airpods.Quota{
				Uid:                userID,
				MonthlyQuota:       DefaultMonthlyQuota,
				CurrentUsage:       0,
				ExtraPurchases:     0,
				LastResetDate:      time.Now(),
				SubscriptionStatus: 0, // 默认未订阅
				SubscriptionExpiry: time.Time{},
				IsActive:           true,
			}

			if err := global.GVA_DB.Create(&quota).Error; err != nil {
				global.GVA_LOG.Error("创建用户配额记录失败",
					zap.Uint("user_id", userID),
					zap.Error(err))
				return nil, fmt.Errorf("创建用户配额记录失败: %w", err)
			}

			global.GVA_LOG.Info("创建新用户配额记录",
				zap.Uint("user_id", userID),
				zap.Int("monthly_quota", DefaultMonthlyQuota))
		} else {
			global.GVA_LOG.Error("查询用户配额记录失败",
				zap.Uint("user_id", userID),
				zap.Error(err))
			return nil, fmt.Errorf("查询用户配额记录失败: %w", err)
		}
	}

	return &quota, nil
}

// CheckQuota 检查用户配额是否足够
func (s *QuotaService) CheckQuota(userID uint) (bool, *airpods.Quota, error) {
	quota, err := s.GetOrCreateUserQuota(userID)
	if err != nil {
		return false, nil, err
	}

	// 检查是否需要重置月度配额
	if err := s.checkAndResetMonthlyQuota(quota); err != nil {
		global.GVA_LOG.Error("检查和重置月度配额失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return false, quota, err
	}

	// 检查配额是否可用
	canUse := quota.CanUse()

	global.GVA_LOG.Debug("配额检查结果",
		zap.Uint("user_id", userID),
		zap.Bool("can_use", canUse),
		zap.Int("total_quota", quota.GetTotalQuota()),
		zap.Int("current_usage", quota.CurrentUsage),
		zap.Int("remaining", quota.GetRemainingQuota()))

	return canUse, quota, nil
}

// UseQuota 使用配额（增加使用量）
func (s *QuotaService) UseQuota(userID uint, usageType int, requestInfo map[string]interface{}) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取配额记录
		var quota airpods.Quota
		if err := tx.Where("uid = ?", userID).First(&quota).Error; err != nil {
			return fmt.Errorf("获取用户配额记录失败: %w", err)
		}

		// 检查配额是否足够
		if !quota.CanUse() {
			return errors.New("配额不足")
		}

		// 增加使用量
		quota.CurrentUsage++
		if err := tx.Save(&quota).Error; err != nil {
			return fmt.Errorf("更新配额使用量失败: %w", err)
		}

		// 记录使用日志
		usageLog := airpods.QuotaUsageLog{
			Uid:            userID,
			UsageType:      usageType,
			RequestPath:    getStringFromMap(requestInfo, "request_path"),
			RequestMethod:  getStringFromMap(requestInfo, "request_method"),
			ClientIP:       getStringFromMap(requestInfo, "client_ip"),
			UserAgent:      getStringFromMap(requestInfo, "user_agent"),
			UsageDate:      time.Now(),
			Success:        true,
			ConversationID: getStringFromMap(requestInfo, "conversation_id"),
			RequestSize:    getIntFromMap(requestInfo, "request_size"),
			ResponseSize:   getIntFromMap(requestInfo, "response_size"),
		}

		if err := tx.Create(&usageLog).Error; err != nil {
			global.GVA_LOG.Error("创建配额使用日志失败",
				zap.Uint("user_id", userID),
				zap.Error(err))
			// 日志记录失败不影响主流程
		}

		global.GVA_LOG.Info("配额使用成功",
			zap.Uint("user_id", userID),
			zap.Int("usage_type", usageType),
			zap.Int("current_usage", quota.CurrentUsage),
			zap.Int("remaining", quota.GetRemainingQuota()))

		return nil
	})
}

// checkAndResetMonthlyQuota 检查并重置月度配额
func (s *QuotaService) checkAndResetMonthlyQuota(quota *airpods.Quota) error {
	now := time.Now()
	lastReset := quota.LastResetDate

	// 检查是否需要重置（跨月了）
	if lastReset.Year() != now.Year() || lastReset.Month() != now.Month() {
		// 重置当前使用量
		quota.CurrentUsage = 0
		quota.LastResetDate = now

		// 如果订阅已过期，重置订阅状态
		if quota.SubscriptionExpiry.Before(now) {
			quota.SubscriptionStatus = 0
		}

		if err := global.GVA_DB.Save(quota).Error; err != nil {
			return fmt.Errorf("重置月度配额失败: %w", err)
		}

		global.GVA_LOG.Info("月度配额已重置",
			zap.Uint("user_id", quota.Uid),
			zap.Time("last_reset", lastReset),
			zap.Time("current_time", now))
	}

	return nil
}

// PurchaseExtraQuota 购买额外配额
func (s *QuotaService) PurchaseExtraQuota(userID uint, units int, clientIP string) (*airpods.PurchaseRecord, error) {
	if units <= 0 {
		return nil, errors.New("购买单位数必须大于0")
	}

	amount := float64(units) * ExtraQuotaPrice
	orderID := fmt.Sprintf("EQ_%d_%d", userID, time.Now().Unix())

	var record airpods.PurchaseRecord

	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 创建购买记录
		record = airpods.PurchaseRecord{
			Uid:           userID,
			PurchaseType:  PurchaseTypeExtraQuota,
			PurchaseUnits: units,
			Amount:        amount,
			Status:        PaymentStatusPaid, // 简化处理，直接标记为已支付
			PaymentMethod: "system",
			OrderID:       orderID,
			Description:   fmt.Sprintf("购买额外配额 %d 单位 (%d 次)", units, units*ExtraQuotaUnit),
			ClientIP:      clientIP,
		}

		if err := tx.Create(&record).Error; err != nil {
			return fmt.Errorf("创建购买记录失败: %w", err)
		}

		// 更新用户配额
		var quota airpods.Quota
		if err := tx.Where("uid = ?", userID).First(&quota).Error; err != nil {
			return fmt.Errorf("获取用户配额记录失败: %w", err)
		}

		quota.ExtraPurchases += units * ExtraQuotaUnit
		if err := tx.Save(&quota).Error; err != nil {
			return fmt.Errorf("更新用户配额失败: %w", err)
		}

		global.GVA_LOG.Info("额外配额购买成功",
			zap.Uint("user_id", userID),
			zap.Int("units", units),
			zap.Float64("amount", amount),
			zap.String("order_id", orderID))

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &record, nil
}

// SubscribeMonthly 订阅月度服务
func (s *QuotaService) SubscribeMonthly(userID uint, clientIP string) (*airpods.PurchaseRecord, error) {
	orderID := fmt.Sprintf("MS_%d_%d", userID, time.Now().Unix())

	var record airpods.PurchaseRecord

	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 创建购买记录
		record = airpods.PurchaseRecord{
			Uid:           userID,
			PurchaseType:  PurchaseTypeMonthlySubscription,
			PurchaseUnits: 1,
			Amount:        MonthlySubscriptionFee,
			Status:        PaymentStatusPaid, // 简化处理，直接标记为已支付
			PaymentMethod: "system",
			OrderID:       orderID,
			Description:   "月度订阅服务",
			ClientIP:      clientIP,
		}

		if err := tx.Create(&record).Error; err != nil {
			return fmt.Errorf("创建购买记录失败: %w", err)
		}

		// 更新用户配额
		var quota airpods.Quota
		if err := tx.Where("uid = ?", userID).First(&quota).Error; err != nil {
			return fmt.Errorf("获取用户配额记录失败: %w", err)
		}

		// 设置订阅状态和到期时间
		quota.SubscriptionStatus = 1
		now := time.Now()
		if quota.SubscriptionExpiry.Before(now) {
			// 如果当前没有有效订阅，从现在开始计算
			quota.SubscriptionExpiry = now.AddDate(0, 1, 0) // 加一个月
		} else {
			// 如果有有效订阅，延长一个月
			quota.SubscriptionExpiry = quota.SubscriptionExpiry.AddDate(0, 1, 0)
		}

		if err := tx.Save(&quota).Error; err != nil {
			return fmt.Errorf("更新用户配额失败: %w", err)
		}

		global.GVA_LOG.Info("月度订阅成功",
			zap.Uint("user_id", userID),
			zap.Float64("amount", MonthlySubscriptionFee),
			zap.String("order_id", orderID),
			zap.Time("expiry", quota.SubscriptionExpiry))

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &record, nil
}

// GetQuotaInfo 获取用户配额信息
func (s *QuotaService) GetQuotaInfo(userID uint) (*airpods.Quota, error) {
	quota, err := s.GetOrCreateUserQuota(userID)
	if err != nil {
		return nil, err
	}

	// 检查并重置月度配额
	if err := s.checkAndResetMonthlyQuota(quota); err != nil {
		return nil, err
	}

	return quota, nil
}

// GetPurchaseHistory 获取购买历史
func (s *QuotaService) GetPurchaseHistory(userID uint, limit, offset int) ([]airpods.PurchaseRecord, int64, error) {
	var records []airpods.PurchaseRecord
	var total int64

	db := global.GVA_DB.Model(&airpods.PurchaseRecord{}).Where("uid = ?", userID)

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取购买记录总数失败: %w", err)
	}

	// 获取记录
	if err := db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error; err != nil {
		return nil, 0, fmt.Errorf("获取购买记录失败: %w", err)
	}

	return records, total, nil
}

// GetUsageHistory 获取使用历史
func (s *QuotaService) GetUsageHistory(userID uint, limit, offset int) ([]airpods.QuotaUsageLog, int64, error) {
	var logs []airpods.QuotaUsageLog
	var total int64

	db := global.GVA_DB.Model(&airpods.QuotaUsageLog{}).Where("uid = ?", userID)

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取使用记录总数失败: %w", err)
	}

	// 获取记录
	if err := db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("获取使用记录失败: %w", err)
	}

	return logs, total, nil
}

// ResetMonthlyQuotaForAllUsers 重置所有用户的月度配额
func (s *QuotaService) ResetMonthlyQuotaForAllUsers() error {
	now := time.Now()

	// 批量更新所有需要重置的配额记录
	result := global.GVA_DB.Model(&airpods.Quota{}).
		Where("DATE_FORMAT(last_reset_date, '%Y-%m') != DATE_FORMAT(?, '%Y-%m')", now).
		Updates(map[string]interface{}{
			"current_usage":   0,
			"last_reset_date": now,
		})

	if result.Error != nil {
		global.GVA_LOG.Error("批量重置月度配额失败", zap.Error(result.Error))
		return fmt.Errorf("批量重置月度配额失败: %w", result.Error)
	}

	global.GVA_LOG.Info("批量重置月度配额成功",
		zap.Int64("affected_rows", result.RowsAffected),
		zap.Time("reset_time", now))

	return nil
}

// ResetExpiredSubscriptions 重置过期订阅状态
func (s *QuotaService) ResetExpiredSubscriptions() error {
	now := time.Now()

	// 批量更新过期的订阅状态
	result := global.GVA_DB.Model(&airpods.Quota{}).
		Where("subscription_status = 1 AND subscription_expiry < ?", now).
		Update("subscription_status", 0)

	if result.Error != nil {
		global.GVA_LOG.Error("批量重置过期订阅失败", zap.Error(result.Error))
		return fmt.Errorf("批量重置过期订阅失败: %w", result.Error)
	}

	global.GVA_LOG.Info("批量重置过期订阅成功",
		zap.Int64("affected_rows", result.RowsAffected),
		zap.Time("reset_time", now))

	return nil
}

// GetQuotaStatistics 获取配额统计信息
func (s *QuotaService) GetQuotaStatistics() (map[string]interface{}, error) {
	var stats struct {
		TotalUsers        int64 `json:"total_users"`
		ActiveSubscribers int64 `json:"active_subscribers"`
		TotalUsageToday   int64 `json:"total_usage_today"`
		TotalUsageMonth   int64 `json:"total_usage_month"`
	}

	// 获取总用户数
	if err := global.GVA_DB.Model(&airpods.Quota{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 获取活跃订阅用户数
	now := time.Now()
	if err := global.GVA_DB.Model(&airpods.Quota{}).
		Where("subscription_status = 1 AND subscription_expiry > ?", now).
		Count(&stats.ActiveSubscribers).Error; err != nil {
		return nil, fmt.Errorf("获取活跃订阅用户数失败: %w", err)
	}

	// 获取今日总使用量
	today := time.Now().Format("2006-01-02")
	if err := global.GVA_DB.Model(&airpods.QuotaUsageLog{}).
		Where("DATE(usage_date) = ?", today).
		Count(&stats.TotalUsageToday).Error; err != nil {
		return nil, fmt.Errorf("获取今日总使用量失败: %w", err)
	}

	// 获取本月总使用量
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	if err := global.GVA_DB.Model(&airpods.QuotaUsageLog{}).
		Where("usage_date >= ?", monthStart).
		Count(&stats.TotalUsageMonth).Error; err != nil {
		return nil, fmt.Errorf("获取本月总使用量失败: %w", err)
	}

	result := map[string]interface{}{
		"total_users":        stats.TotalUsers,
		"active_subscribers": stats.ActiveSubscribers,
		"total_usage_today":  stats.TotalUsageToday,
		"total_usage_month":  stats.TotalUsageMonth,
		"statistics_time":    now,
	}

	return result, nil
}

// 辅助函数：从map中获取字符串值
func getStringFromMap(m map[string]interface{}, key string) string {
	if v, ok := m[key]; ok {
		if s, ok := v.(string); ok {
			return s
		}
	}
	return ""
}

// 辅助函数：从map中获取整数值
func getIntFromMap(m map[string]interface{}, key string) int {
	if v, ok := m[key]; ok {
		if i, ok := v.(int); ok {
			return i
		}
	}
	return 0
}

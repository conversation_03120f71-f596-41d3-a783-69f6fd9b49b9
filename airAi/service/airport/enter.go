package airport

import (
	"sync"
	"time"
)

type ServiceGroup struct {
	UserService
}

var (
	// 全局对话上下文管理器实例
	conversationManager *ConversationManager
	// 确保只初始化一次的同步原语
	conversationManagerOnce sync.Once
)

// GetConversationManager 获取全局对话上下文管理器实例
// 使用单例模式确保全局只有一个管理器实例
func GetConversationManager() *ConversationManager {
	conversationManagerOnce.Do(func() {
		conversationManager = NewConversationManager()

		// 启动定期清理过期上下文的协程
		go func() {
			ticker := time.NewTicker(30 * time.Minute) // 每30分钟清理一次
			defer ticker.Stop()

			for range ticker.C {
				// 清理超过2小时未更新的对话上下文
				conversationManager.CleanupExpiredContexts(2 * time.Hour)
			}
		}()
	})

	return conversationManager
}

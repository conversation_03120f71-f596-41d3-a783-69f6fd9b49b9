package airport

import (
	"airAi/common/requst"
	"airAi/global"
	"airAi/other_api/wechat"
	"airAi/utils"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"model/airpods"
	"strings"
)

// WechatLoginService 微信登录服务
type WechatLoginService struct {
	wechatOAuth *wechat.WechatOAuthService
}

// NewWechatLoginService 创建微信登录服务实例
func NewWechatLoginService() *WechatLoginService {
	return &WechatLoginService{
		wechatOAuth: wechat.NewWechatOAuthService(),
	}
}

// IsEnabled 检查微信登录是否启用
func (w *WechatLoginService) IsEnabled() bool {
	return w.wechatOAuth.IsEnabled()
}

// GenerateAuthURL 生成微信授权URL
func (w *WechatLoginService) GenerateAuthURL(state string) (string, error) {
	if !w.IsEnabled() {
		return "", errors.New("微信登录服务未启用")
	}
	
	if state == "" {
		state = w.wechatOAuth.GenerateState()
	}
	
	authURL := w.wechatOAuth.GenerateAuthURL(state)
	return authURL, nil
}

// ProcessWechatCallback 处理微信授权回调
func (w *WechatLoginService) ProcessWechatCallback(req requst.WechatCallbackRequest) (*airpods.AirpodsUser, bool, error) {
	if !w.IsEnabled() {
		return nil, false, errors.New("微信登录服务未启用")
	}
	
	// 检查是否有错误（用户取消授权等）
	if req.Error != "" {
		global.GVA_LOG.Error(fmt.Sprintf("微信授权错误: %s - %s", req.Error, req.ErrorDescription))
		return nil, false, errors.New("用户取消微信授权")
	}
	
	// 验证授权码
	if req.Code == "" {
		return nil, false, errors.New("微信授权码无效")
	}
	
	// 验证state参数（防止CSRF攻击）
	if !w.wechatOAuth.ValidateState(req.State) {
		global.GVA_LOG.Error("微信授权state参数无效: " + req.State)
		return nil, false, errors.New("微信授权状态参数无效")
	}
	
	// 获取access_token
	tokenResp, err := w.wechatOAuth.GetAccessToken(req.Code)
	if err != nil {
		global.GVA_LOG.Error("获取微信access_token失败: " + err.Error())
		return nil, false, errors.New("获取微信访问令牌失败")
	}
	
	// 获取用户信息
	userInfo, err := w.wechatOAuth.GetUserInfo(tokenResp.AccessToken, tokenResp.OpenID)
	if err != nil {
		global.GVA_LOG.Error("获取微信用户信息失败: " + err.Error())
		return nil, false, errors.New("获取微信用户信息失败")
	}
	
	// 查找或创建用户
	user, isNewUser, err := w.findOrCreateUser(userInfo, tokenResp)
	if err != nil {
		global.GVA_LOG.Error("查找或创建用户失败: " + err.Error())
		return nil, false, err
	}
	
	return user, isNewUser, nil
}

// ProcessWechatLogin 处理微信登录（直接提供授权码）
func (w *WechatLoginService) ProcessWechatLogin(req requst.WechatLoginRequest) (*airpods.AirpodsUser, bool, error) {
	if !w.IsEnabled() {
		return nil, false, errors.New("微信登录服务未启用")
	}
	
	// 验证授权码
	if req.Code == "" {
		return nil, false, errors.New("微信授权码无效")
	}
	
	// 验证state参数（如果提供）
	if req.State != "" && !w.wechatOAuth.ValidateState(req.State) {
		global.GVA_LOG.Error("微信授权state参数无效: " + req.State)
		return nil, false, errors.New("微信授权状态参数无效")
	}
	
	// 获取access_token
	tokenResp, err := w.wechatOAuth.GetAccessToken(req.Code)
	if err != nil {
		global.GVA_LOG.Error("获取微信access_token失败: " + err.Error())
		return nil, false, errors.New("获取微信访问令牌失败")
	}
	
	// 获取用户信息
	userInfo, err := w.wechatOAuth.GetUserInfo(tokenResp.AccessToken, tokenResp.OpenID)
	if err != nil {
		global.GVA_LOG.Error("获取微信用户信息失败: " + err.Error())
		return nil, false, errors.New("获取微信用户信息失败")
	}
	
	// 查找或创建用户
	user, isNewUser, err := w.findOrCreateUser(userInfo, tokenResp)
	if err != nil {
		global.GVA_LOG.Error("查找或创建用户失败: " + err.Error())
		return nil, false, err
	}
	
	return user, isNewUser, nil
}

// findOrCreateUser 查找或创建用户
func (w *WechatLoginService) findOrCreateUser(userInfo *wechat.WechatUserInfo, tokenResp *wechat.WechatTokenResponse) (*airpods.AirpodsUser, bool, error) {
	var user airpods.AirpodsUser
	var isNewUser bool
	
	// 首先尝试通过OpenID查找用户
	err := global.GVA_DB.Where("wechat_open_id = ?", userInfo.OpenID).First(&user).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, false, fmt.Errorf("查询用户失败: %w", err)
	}
	
	// 如果通过OpenID没找到，且有UnionID，尝试通过UnionID查找
	if err == gorm.ErrRecordNotFound && userInfo.UnionID != "" {
		err = global.GVA_DB.Where("wechat_union_id = ?", userInfo.UnionID).First(&user).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, false, fmt.Errorf("查询用户失败: %w", err)
		}
	}
	
	if err == gorm.ErrRecordNotFound {
		// 用户不存在，创建新用户
		isNewUser = true
		user = airpods.AirpodsUser{
			UUID:          uuid.New(),
			Username:      generateUsernameFromWechat(userInfo),
			NickName:      userInfo.Nickname,
			HeaderImg:     userInfo.HeadImgURL,
			WechatOpenID:  userInfo.OpenID,
			WechatUnionID: userInfo.UnionID,
			Enable:        1, // 默认启用
			AuthorityId:   888, // 默认角色ID
		}
		
		// 如果头像URL为空，使用默认头像
		if user.HeaderImg == "" {
			user.HeaderImg = "https://qmplusimg.henrongyi.top/gva_header.jpg"
		}
		
		// 如果昵称为空，生成随机昵称
		if user.NickName == "" {
			user.NickName, _ = utils.RandomUsername(5)
		}
		
		err = global.GVA_DB.Create(&user).Error
		if err != nil {
			return nil, false, fmt.Errorf("创建用户失败: %w", err)
		}
	} else {
		// 用户已存在，更新微信信息
		isNewUser = false
		updates := map[string]interface{}{}
		
		// 更新OpenID（如果不同）
		if user.WechatOpenID != userInfo.OpenID {
			updates["wechat_open_id"] = userInfo.OpenID
		}
		
		// 更新UnionID（如果有且不同）
		if userInfo.UnionID != "" && user.WechatUnionID != userInfo.UnionID {
			updates["wechat_union_id"] = userInfo.UnionID
		}
		
		// 更新昵称（如果微信昵称不为空且不同）
		if userInfo.Nickname != "" && user.NickName != userInfo.Nickname {
			updates["nick_name"] = userInfo.Nickname
		}
		
		// 更新头像（如果微信头像不为空且不同）
		if userInfo.HeadImgURL != "" && user.HeaderImg != userInfo.HeadImgURL {
			updates["header_img"] = userInfo.HeadImgURL
		}
		
		// 如果有更新，执行更新操作
		if len(updates) > 0 {
			err = global.GVA_DB.Model(&user).Where("id = ?", user.ID).Updates(updates).Error
			if err != nil {
				global.GVA_LOG.Error("更新用户微信信息失败: " + err.Error())
				// 更新失败不影响登录，只记录日志
			} else {
				// 重新查询用户信息以获取最新数据
				global.GVA_DB.Where("id = ?", user.ID).First(&user)
			}
		}
	}
	
	return &user, isNewUser, nil
}

// generateUsernameFromWechat 从微信信息生成用户名
func generateUsernameFromWechat(userInfo *wechat.WechatUserInfo) string {
	// 使用OpenID的后8位作为用户名的一部分
	openIDSuffix := userInfo.OpenID
	if len(openIDSuffix) > 8 {
		openIDSuffix = openIDSuffix[len(openIDSuffix)-8:]
	}
	
	// 清理昵称，移除特殊字符
	cleanNickname := strings.ReplaceAll(userInfo.Nickname, " ", "")
	cleanNickname = strings.ReplaceAll(cleanNickname, "@", "")
	cleanNickname = strings.ReplaceAll(cleanNickname, "#", "")
	
	// 如果昵称太长，截取前8位
	if len(cleanNickname) > 8 {
		cleanNickname = cleanNickname[:8]
	}
	
	// 组合用户名
	if cleanNickname != "" {
		return fmt.Sprintf("wx_%s_%s", cleanNickname, openIDSuffix)
	}
	
	return fmt.Sprintf("wx_user_%s", openIDSuffix)
}

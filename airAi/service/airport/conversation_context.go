package airport

import (
	"airAi/common/requst"
	"sync"
	"time"
)

// ConversationContext 对话上下文结构体
// 用于存储和管理单个对话会话的上下文信息
type ConversationContext struct {
	ConversationID string                       `json:"conversation_id"` // 对话会话ID
	Messages       []requst.ConversationMessage `json:"messages"`        // 对话历史消息列表
	SystemPrompt   string                       `json:"system_prompt"`   // 系统提示词
	CreatedAt      time.Time                    `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time                    `json:"updated_at"`      // 最后更新时间
}

// ConversationManager 对话上下文管理器
// 提供对话上下文的存储、检索和管理功能
type ConversationManager struct {
	contexts map[string]*ConversationContext // 对话上下文存储映射
	mutex    sync.RWMutex                    // 读写锁，保证并发安全
}

// NewConversationManager 创建新的对话上下文管理器
func NewConversationManager() *ConversationManager {
	return &ConversationManager{
		contexts: make(map[string]*ConversationContext),
		mutex:    sync.RWMutex{},
	}
}

// GetOrCreateContext 获取或创建对话上下文
// 如果对话ID存在则返回现有上下文，否则创建新的上下文
func (cm *ConversationManager) GetOrCreateContext(conversationID, systemPrompt string) *ConversationContext {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 如果对话ID为空，生成新的ID
	if conversationID == "" {
		conversationID = generateConversationID()
	}

	// 检查是否已存在该对话上下文
	if context, exists := cm.contexts[conversationID]; exists {
		// 更新系统提示词（如果提供了新的）
		if systemPrompt != "" && systemPrompt != context.SystemPrompt {
			context.SystemPrompt = systemPrompt
			context.UpdatedAt = time.Now()
		}
		return context
	}

	// 创建新的对话上下文
	context := &ConversationContext{
		ConversationID: conversationID,
		Messages:       make([]requst.ConversationMessage, 0),
		SystemPrompt:   systemPrompt,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	cm.contexts[conversationID] = context
	return context
}

// AddMessage 向对话上下文添加消息
func (cm *ConversationManager) AddMessage(conversationID string, message requst.ConversationMessage) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if context, exists := cm.contexts[conversationID]; exists {
		// 设置消息时间戳
		if message.Timestamp == 0 {
			message.Timestamp = time.Now().Unix()
		}

		context.Messages = append(context.Messages, message)
		context.UpdatedAt = time.Now()
	}
}

// GetContextMessages 获取指定上下文窗口大小的消息列表
// contextWindow: 上下文窗口大小，0表示获取所有消息
func (cm *ConversationManager) GetContextMessages(conversationID string, contextWindow int) []requst.ConversationMessage {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	context, exists := cm.contexts[conversationID]
	if !exists {
		return []requst.ConversationMessage{}
	}

	messages := context.Messages

	// 如果指定了上下文窗口大小且小于总消息数，则只返回最近的消息
	if contextWindow > 0 && len(messages) > contextWindow {
		return messages[len(messages)-contextWindow:]
	}

	return messages
}

// GetSystemPrompt 获取对话的系统提示词
func (cm *ConversationManager) GetSystemPrompt(conversationID string) string {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	if context, exists := cm.contexts[conversationID]; exists {
		return context.SystemPrompt
	}
	return ""
}

// DeleteContext 删除指定的对话上下文
func (cm *ConversationManager) DeleteContext(conversationID string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	delete(cm.contexts, conversationID)
}

// CleanupExpiredContexts 清理过期的对话上下文
// maxAge: 最大存活时间，超过此时间的上下文将被删除
func (cm *ConversationManager) CleanupExpiredContexts(maxAge time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	for id, context := range cm.contexts {
		if now.Sub(context.UpdatedAt) > maxAge {
			delete(cm.contexts, id)
		}
	}
}

// GetContextCount 获取当前存储的对话上下文数量
func (cm *ConversationManager) GetContextCount() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return len(cm.contexts)
}

// generateConversationID 生成新的对话ID
func generateConversationID() string {
	// 使用时间戳和随机数生成唯一ID
	return "conv_" + time.Now().Format("20060102150405") + "_" + generateRandomString(8)
}

// generateRandomString 生成指定长度的随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}

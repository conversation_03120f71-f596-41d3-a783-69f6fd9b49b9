package airport

import (
	"time"

	"airAi/global"

	"go.uber.org/zap"
)

// QuotaSchedulerService 配额定时任务服务
type QuotaSchedulerService struct {
	quotaService *QuotaService
	stopChan     chan bool
	running      bool
}

// NewQuotaSchedulerService 创建配额定时任务服务
func NewQuotaSchedulerService() *QuotaSchedulerService {
	return &QuotaSchedulerService{
		quotaService: &QuotaService{},
		stopChan:     make(chan bool),
		running:      false,
	}
}

// Start 启动定时任务
func (s *QuotaSchedulerService) Start() {
	if s.running {
		global.GVA_LOG.Warn("配额定时任务已经在运行中")
		return
	}

	s.running = true
	global.GVA_LOG.Info("启动配额定时任务服务")

	// 启动月度重置任务
	go s.runMonthlyResetTask()

	// 启动订阅过期检查任务
	go s.runSubscriptionExpiryTask()
}

// Stop 停止定时任务
func (s *QuotaSchedulerService) Stop() {
	if !s.running {
		return
	}

	global.GVA_LOG.Info("停止配额定时任务服务")
	s.running = false
	close(s.stopChan)
}

// runMonthlyResetTask 运行月度重置任务
func (s *QuotaSchedulerService) runMonthlyResetTask() {
	// 计算下次重置时间（每月1日凌晨0点）
	now := time.Now()
	nextMonth := now.AddDate(0, 1, 0)
	nextReset := time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, now.Location())

	global.GVA_LOG.Info("月度配额重置任务已启动",
		zap.Time("next_reset_time", nextReset))

	// 等待到下次重置时间
	timer := time.NewTimer(time.Until(nextReset))
	defer timer.Stop()

	for s.running {
		select {
		case <-timer.C:
			// 执行月度重置
			s.performMonthlyReset()

			// 计算下次重置时间
			now = time.Now()
			nextMonth = now.AddDate(0, 1, 0)
			nextReset = time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, now.Location())
			timer.Reset(time.Until(nextReset))

			global.GVA_LOG.Info("下次月度配额重置时间",
				zap.Time("next_reset_time", nextReset))

		case <-s.stopChan:
			global.GVA_LOG.Info("月度配额重置任务已停止")
			return
		}
	}
}

// runSubscriptionExpiryTask 运行订阅过期检查任务
func (s *QuotaSchedulerService) runSubscriptionExpiryTask() {
	// 每小时检查一次订阅过期
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	global.GVA_LOG.Info("订阅过期检查任务已启动")

	for s.running {
		select {
		case <-ticker.C:
			// 执行订阅过期检查
			s.performSubscriptionExpiryCheck()

		case <-s.stopChan:
			global.GVA_LOG.Info("订阅过期检查任务已停止")
			return
		}
	}
}

// performMonthlyReset 执行月度重置
func (s *QuotaSchedulerService) performMonthlyReset() {
	global.GVA_LOG.Info("开始执行月度配额重置")

	// 重置所有用户的月度配额
	if err := s.quotaService.ResetMonthlyQuotaForAllUsers(); err != nil {
		global.GVA_LOG.Error("月度配额重置失败", zap.Error(err))
		return
	}

	// 重置过期订阅
	if err := s.quotaService.ResetExpiredSubscriptions(); err != nil {
		global.GVA_LOG.Error("重置过期订阅失败", zap.Error(err))
		// 不返回，继续执行其他任务
	}

	global.GVA_LOG.Info("月度配额重置完成")
}

// performSubscriptionExpiryCheck 执行订阅过期检查
func (s *QuotaSchedulerService) performSubscriptionExpiryCheck() {
	global.GVA_LOG.Debug("开始检查订阅过期状态")

	if err := s.quotaService.ResetExpiredSubscriptions(); err != nil {
		global.GVA_LOG.Error("订阅过期检查失败", zap.Error(err))
		return
	}

	global.GVA_LOG.Debug("订阅过期检查完成")
}

// IsRunning 检查定时任务是否在运行
func (s *QuotaSchedulerService) IsRunning() bool {
	return s.running
}

// GetNextResetTime 获取下次重置时间
func (s *QuotaSchedulerService) GetNextResetTime() time.Time {
	now := time.Now()
	nextMonth := now.AddDate(0, 1, 0)
	return time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, now.Location())
}

// ForceMonthlyReset 强制执行月度重置（用于测试或手动触发）
func (s *QuotaSchedulerService) ForceMonthlyReset() error {
	global.GVA_LOG.Info("手动触发月度配额重置")

	// 重置所有用户的月度配额
	if err := s.quotaService.ResetMonthlyQuotaForAllUsers(); err != nil {
		global.GVA_LOG.Error("手动月度配额重置失败", zap.Error(err))
		return err
	}

	// 重置过期订阅
	if err := s.quotaService.ResetExpiredSubscriptions(); err != nil {
		global.GVA_LOG.Error("手动重置过期订阅失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("手动月度配额重置完成")
	return nil
}

// GetQuotaStatistics 获取配额统计信息
func (s *QuotaSchedulerService) GetQuotaStatistics() (map[string]interface{}, error) {
	return s.quotaService.GetQuotaStatistics()
}

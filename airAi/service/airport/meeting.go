package airport

import (
	"airAi/global"
	"airAi/other_api"
	"airAi/other_api/qwen"
	"airAi/other_api/types"
	"airAi/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

const qwApiKey = "sk-597c7556cb88475f8df22ca6b2b89076"

func (s *UserService) Asr(sourceLanguage, targetLanguages string, audio io.Reader) (*types.TranscriptionData, error) {
	config := qwen.AliYunConfig{
		ApiKey: qwApiKey,
		Model:  qwen.GummyRealtimeV1,
		//Format:          "wav",
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}
	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return nil, err
	}
	data, err := asrCli.TranscriptionAudio(audio)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// SpeechRecognition 使用PfRealtimeV2模型进行语音识别（非流式）
func (s *UserService) SpeechRecognition(sourceLanguage string, audio io.Reader) (*types.TranscriptionData, error) {
	config := qwen.AliYunConfig{
		ApiKey:         qwApiKey,
		Model:          qwen.PfRealtimeV2,
		SourceLanguage: sourceLanguage,
	}
	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return nil, err
	}
	data, err := asrCli.TranscriptionAudio(audio)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// StreamingSpeechRecognition 使用PfRealtimeV2模型进行流式语音识别
func (s *UserService) StreamingSpeechRecognition(sourceLanguage string, audio io.Reader, resultChan chan<- qwen.StreamingTranscriptionResult) error {
	config := qwen.AliYunConfig{
		ApiKey:         qwApiKey,
		Model:          qwen.PfRealtimeV2,
		SourceLanguage: sourceLanguage,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 启动流式ASR处理
	return asrCli.StreamingTranscriptionAudio(audio, resultChan)
}

func (s *UserService) Translate(sourceLanguage, targetLanguages, text string) (string, error) {
	client := qwen.NewQwenClient()
	prompt := fmt.Sprintf(
		qwen.TranslatePrompt, text, targetLanguages)
	return client.Text(context.Background(), prompt)
}

// ComprehensiveStreamingVoiceTranslate 综合流式语音翻译 - 返回ASR、翻译和TTS结果
func (s *UserService) ComprehensiveStreamingVoiceTranslate(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	// 创建ASR配置 - 使用与Transcription方法相同的配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动增强的ASR流式转录 - 利用Transcription方法的基础设施
	go func() {
		defer close(transcriptionChan)
		err := s.enhancedStreamingTranscriptionWithTranslation(asrCli, audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动综合处理管道
	return s.processComprehensiveVoicePipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// StreamingVoiceTranslate 流式语音翻译 - 真正的实时流式处理
func (s *UserService) StreamingVoiceTranslate(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.StreamingTranslateResult) error {
	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动ASR流式转录
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionAudio(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动实时翻译管道 - 立即处理每个转录结果
	return s.processRealtimeTranscriptionPipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

func (s *UserService) StreamingVoiceTranslate1(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.Event) error {
	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.Event, 100)

	// 启动ASR处理的goroutine
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionAudio1(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			//select {
			//case transcriptionChan <- qwen.StreamingTranscriptionResult{
			//	Text:        "",
			//	Translation: "",
			//	IsPartial:   false,
			//	IsEnd:       true,
			//	Error:       err.Error(),
			//}:
			//default:
			//}
		}
	}()
	for v := range transcriptionChan {
		//if v.Error != "" {
		//	global.GVA_LOG.Error("error", zap.Any("transcriptionChan", transcriptionChan))
		//	continue
		//}
		resultChan <- v
	}

	return nil
}

// processRealtimeTranscriptionPipeline 处理实时转录管道 - 立即翻译每个转录片段
func (s *UserService) processRealtimeTranscriptionPipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.StreamingTranslateResult) error {
	client := qwen.NewQwenClient()

	// 使用 sync.WaitGroup 来协调多个翻译 goroutine
	var wg sync.WaitGroup

	// 使用 context 来处理取消和超时
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 用于去重的映射，避免重复处理相同的文本
	processedTexts := make(map[string]bool)
	var textMutex sync.Mutex

	for transcriptionResult := range transcriptionChan {
		// 处理错误
		if transcriptionResult.Error != "" {
			cancel()  // 取消所有正在进行的翻译
			wg.Wait() // 等待所有 goroutine 完成
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:      "",
				OriginalText: "",
				IsPartial:    false,
				IsEnd:        true,
				Error:        transcriptionResult.Error,
			}:
			default:
			}
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			// 等待所有翻译 goroutine 完成，确保翻译结果都被发送
			wg.Wait()

			// 现在取消上下文，因为所有翻译都已完成
			cancel()

			// 发送最终结束标记
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:      "",
				OriginalText: "",
				IsPartial:    false,
				IsEnd:        true,
			}:
			default:
			}
			break
		}

		// 处理ASR结果，避免重复处理相同文本
		if transcriptionResult.Text != "" {
			textMutex.Lock()
			// 使用文本内容作为去重键，忽略IsPartial状态
			textKey := transcriptionResult.Text
			alreadyProcessed := processedTexts[textKey]
			if !alreadyProcessed {
				processedTexts[textKey] = true
			}
			textMutex.Unlock()

			// 只处理未处理过的文本
			if !alreadyProcessed {
				// 发送ASR结果
				select {
				case resultChan <- qwen.StreamingTranslateResult{
					Content:      "",
					OriginalText: transcriptionResult.Text,
					IsPartial:    transcriptionResult.IsPartial,
					IsEnd:        false,
				}:
				case <-ctx.Done():
					return ctx.Err()
				default:
					// Channel is full, skip this result
				}

				// 立即翻译每个转录片段（无论是否为部分结果）
				wg.Add(1)
				// 为每个转录片段启动独立的翻译流
				go s.translateAndStreamImmediateWithSync(ctx, &wg, client, sourceLanguage, targetLanguages, transcriptionResult.Text, resultChan, transcriptionResult.IsPartial)
			}
		}
	}

	return nil
}

// ProcessRealtimeTranscriptionPipeline 公开的方法用于测试
func (s *UserService) ProcessRealtimeTranscriptionPipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.StreamingTranslateResult) error {
	return s.processRealtimeTranscriptionPipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// translateAndStreamImmediateWithSync 立即翻译并流式输出 - 带同步控制
func (s *UserService) translateAndStreamImmediateWithSync(ctx context.Context, wg *sync.WaitGroup, client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, resultChan chan<- qwen.StreamingTranslateResult, isPartial bool) {
	defer wg.Done()

	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(ctx, sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:      "",
				OriginalText: "",
				IsPartial:    false,
				IsEnd:        true,
				Error:        err.Error(),
			}:
			case <-ctx.Done():
				return
			default:
			}
		}
	}()

	// 立即转发每个翻译结果片段
	for translateResult := range translateChan {
		// 检查上下文是否被取消，但不要立即返回，让翻译完成
		select {
		case <-ctx.Done():
			// 上下文取消了，但我们仍然要处理已经生成的翻译结果
			// 不要return，继续处理这个结果
		default:
		}

		// 保持部分结果的标记
		if isPartial && !translateResult.IsEnd {
			translateResult.IsPartial = true
		}

		// 确保不包含VoiceResponse数据，只包含翻译内容
		translateResult.VoiceResponse = nil

		// 立即发送每个翻译片段，但不发送个别的结束标记
		if !translateResult.IsEnd {
			select {
			case resultChan <- translateResult:
				// 成功发送翻译结果
			default:
				// Channel is full, skip this result
				// 注意：不检查ctx.Done()，确保翻译结果能被发送
			}
		}
	}
}

// translateAndStreamImmediate 立即翻译并流式输出 - 不等待完整句子（保留向后兼容性）
func (s *UserService) translateAndStreamImmediate(client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, resultChan chan<- qwen.StreamingTranslateResult, isPartial bool) {
	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(context.Background(), sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 立即转发每个翻译结果片段
	for translateResult := range translateChan {
		// 保持部分结果的标记
		if isPartial && !translateResult.IsEnd {
			translateResult.IsPartial = true
		}

		// 立即发送每个翻译片段
		select {
		case resultChan <- translateResult:
		default:
			// Channel is full, skip this result
		}

		if translateResult.IsEnd {
			break
		}
	}
}

// ConcurrentStreamingVoiceTranslate 并发流式语音翻译（等待完整句子后翻译）
// 注意：此方法等待完整句子后才进行翻译，延迟较高
// 推荐使用 StreamingVoiceTranslate 获得真正的实时体验
func (s *UserService) ConcurrentStreamingVoiceTranslate(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.StreamingTranslateResult) error {

	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动ASR流式转录
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionAudio(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动翻译管道
	return s.processTranscriptionPipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// processTranscriptionPipeline 处理转录管道
func (s *UserService) processTranscriptionPipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.StreamingTranslateResult) error {
	client := qwen.NewQwenClient()
	var accumulatedText strings.Builder
	var lastTranslatedLength int

	for transcriptionResult := range transcriptionChan {
		// 处理错误
		if transcriptionResult.Error != "" {
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     transcriptionResult.Error,
			}:
			default:
			}
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			// 如果还有未翻译的文本，进行最终翻译
			finalText := accumulatedText.String()
			if len(finalText) > lastTranslatedLength {
				remainingText := finalText[lastTranslatedLength:]
				if strings.TrimSpace(remainingText) != "" {
					err := s.translateAndStream(client, sourceLanguage, targetLanguages, remainingText, resultChan, false)
					if err != nil {
						select {
						case resultChan <- qwen.StreamingTranslateResult{
							Content:   "",
							IsPartial: false,
							IsEnd:     true,
							Error:     err.Error(),
						}:
						default:
						}
						return err
					}
				}
			}

			// 发送结束标记
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}:
			default:
			}
			break
		}

		// 累积转录文本
		if transcriptionResult.Text != "" {
			accumulatedText.WriteString(transcriptionResult.Text)

			// 如果是完整句子（非部分结果），进行翻译
			if !transcriptionResult.IsPartial {
				currentText := accumulatedText.String()
				if len(currentText) > lastTranslatedLength {
					newText := currentText[lastTranslatedLength:]
					if strings.TrimSpace(newText) != "" {
						err := s.translateAndStream(client, sourceLanguage, targetLanguages, newText, resultChan, true)
						if err != nil {
							// 记录错误但继续处理
							continue
						}
						lastTranslatedLength = len(currentText)
					}
				}
			}
		}
	}

	return nil
}

// translateAndStream 翻译并流式输出
func (s *UserService) translateAndStream(client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, resultChan chan<- qwen.StreamingTranslateResult, isPartial bool) error {
	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(context.Background(), sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 转发翻译结果
	for translateResult := range translateChan {
		// 如果是部分转录的翻译结果，标记为部分结果
		if isPartial && !translateResult.IsEnd {
			translateResult.IsPartial = true
		}

		select {
		case resultChan <- translateResult:
		default:
			// Channel is full, skip this result
		}

		if translateResult.IsEnd {
			break
		}
	}

	return nil
}

// StreamingResultAggregator 流式结果聚合器
type StreamingResultAggregator struct {
	transcriptionBuffer strings.Builder
	translationBuffer   strings.Builder
	lastSentLength      int
	isComplete          bool
}

// NewStreamingResultAggregator 创建新的流式结果聚合器
func NewStreamingResultAggregator() *StreamingResultAggregator {
	return &StreamingResultAggregator{
		lastSentLength: 0,
		isComplete:     false,
	}
}

// AddTranscription 添加转录结果
func (sra *StreamingResultAggregator) AddTranscription(text string, isPartial bool) {
	if !isPartial {
		sra.transcriptionBuffer.WriteString(text)
	}
}

// AddTranslation 添加翻译结果并返回需要发送的增量内容
func (sra *StreamingResultAggregator) AddTranslation(content string, isPartial bool) (string, bool) {
	if isPartial {
		// 对于部分结果，直接返回内容
		return content, true
	}

	// 对于完整结果，添加到缓冲区
	sra.translationBuffer.WriteString(content)
	currentContent := sra.translationBuffer.String()

	// 计算增量内容
	if len(currentContent) > sra.lastSentLength {
		incrementalContent := currentContent[sra.lastSentLength:]
		sra.lastSentLength = len(currentContent)
		return incrementalContent, false
	}

	return "", false
}

// GetFullTranscription 获取完整转录文本
func (sra *StreamingResultAggregator) GetFullTranscription() string {
	return sra.transcriptionBuffer.String()
}

// GetFullTranslation 获取完整翻译文本
func (sra *StreamingResultAggregator) GetFullTranslation() string {
	return sra.translationBuffer.String()
}

// MarkComplete 标记为完成
func (sra *StreamingResultAggregator) MarkComplete() {
	sra.isComplete = true
}

// IsComplete 检查是否完成
func (sra *StreamingResultAggregator) IsComplete() bool {
	return sra.isComplete
}

// EnhancedConcurrentStreamingVoiceTranslate 增强的并发流式语音翻译（带结果聚合）
func (s *UserService) EnhancedConcurrentStreamingVoiceTranslate(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.StreamingTranslateResult) error {

	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动ASR流式转录
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionAudio(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动增强的翻译管道
	return s.processEnhancedTranscriptionPipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// processEnhancedTranscriptionPipeline 处理增强的转录管道
func (s *UserService) processEnhancedTranscriptionPipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.StreamingTranslateResult) error {
	client := qwen.NewQwenClient()
	aggregator := NewStreamingResultAggregator()

	for transcriptionResult := range transcriptionChan {
		// 处理错误
		if transcriptionResult.Error != "" {
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     transcriptionResult.Error,
			}:
			default:
			}
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			aggregator.MarkComplete()

			// 发送最终的完整结果
			finalTranslation := aggregator.GetFullTranslation()
			if finalTranslation != "" {
				select {
				case resultChan <- qwen.StreamingTranslateResult{
					Content:   finalTranslation,
					IsPartial: false,
					IsEnd:     false,
				}:
				default:
				}
			}

			// 发送结束标记
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}:
			default:
			}
			break
		}

		// 处理转录结果
		if transcriptionResult.Text != "" {
			aggregator.AddTranscription(transcriptionResult.Text, transcriptionResult.IsPartial)

			// 如果是完整句子，进行翻译
			if !transcriptionResult.IsPartial {
				err := s.translateWithAggregation(client, sourceLanguage, targetLanguages, transcriptionResult.Text, aggregator, resultChan)
				if err != nil {
					// 记录错误但继续处理
					continue
				}
			}
		}
	}

	return nil
}

// translateWithAggregation 使用聚合器进行翻译
func (s *UserService) translateWithAggregation(client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, aggregator *StreamingResultAggregator, resultChan chan<- qwen.StreamingTranslateResult) error {
	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(context.Background(), sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 处理翻译结果并聚合
	for translateResult := range translateChan {
		if translateResult.Error != "" {
			// 转发错误
			select {
			case resultChan <- translateResult:
			default:
			}
			return errors.New(translateResult.Error)
		}

		if translateResult.IsEnd {
			break
		}

		// 使用聚合器处理翻译结果
		incrementalContent, isPartial := aggregator.AddTranslation(translateResult.Content, translateResult.IsPartial)

		if incrementalContent != "" {
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   incrementalContent,
				IsPartial: isPartial,
				IsEnd:     false,
			}:
			default:
			}
		}
	}

	return nil
}

// Meeting 生成会议纪要（支持多语言）
func (s *UserService) Meeting(language, text string) (meeting other_api.MeetingNote, err error) {
	// 验证语言参数
	if err = utils.ValidateMeetingLanguage(language); err != nil {
		return meeting, fmt.Errorf("语言验证失败: %w", err)
	}

	// 获取语言的自然语言名称
	languageName, err := utils.GetMeetingLanguageName(language)
	if err != nil {
		return meeting, fmt.Errorf("获取语言名称失败: %w", err)
	}

	client := qwen.NewQwenClient()
	currentDate := time.Now().Format("2006-01-02")

	// 使用更新后的多语言提示模板
	// 参数顺序：语言名称(多次), 当前日期(多次), 会议内容
	prompt := fmt.Sprintf(
		qwen.MeetingPrompt,
		languageName, languageName, languageName, currentDate,
		languageName, languageName, languageName, languageName,
		languageName, currentDate, languageName, text)

	resp, err := client.Text(context.Background(), prompt)
	if err != nil {
		return meeting, fmt.Errorf("AI生成会议纪要失败: %w", err)
	}

	str, err := utils.ExtractAndParseJSON(resp)
	if err != nil {
		return meeting, fmt.Errorf("解析JSON失败: %w", err)
	}

	err = json.Unmarshal([]byte(str), &meeting)
	if err != nil {
		return meeting, fmt.Errorf("反序列化会议纪要失败: %w", err)
	}

	return meeting, nil
}

// processComprehensiveVoicePipeline 处理综合语音管道 - 包含ASR、翻译和TTS
func (s *UserService) processComprehensiveVoicePipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	client := qwen.NewQwenClient()

	for transcriptionResult := range transcriptionChan {
		timestamp := time.Now().Unix()

		// 处理错误
		if transcriptionResult.Error != "" {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "error",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
				Error:          transcriptionResult.Error,
			}:
			default:
			}
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "complete",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
			}:
			default:
			}
			break
		}

		// 处理转录结果
		if transcriptionResult.Text != "" {
			if transcriptionResult.IsTranslated {
				// 这是翻译结果，直接发送
				select {
				case resultChan <- qwen.ComprehensiveVoiceResult{
					TranslatedText: transcriptionResult.Text,
					TransPartial:   transcriptionResult.IsPartial,
					ProcessingType: "translation",
					Timestamp:      timestamp,
					SourceLanguage: sourceLanguage,
					TargetLanguage: targetLanguages,
					IsPartial:      transcriptionResult.IsPartial,
					IsEnd:          false,
				}:
				default:
				}

				// 如果翻译完成且不是部分结果，生成语音响应
				if !transcriptionResult.IsPartial {
					go s.processVoiceResponse(client, transcriptionResult.Text, sourceLanguage, targetLanguages, resultChan, timestamp)
				}
			} else {
				// 这是ASR结果，发送原始转录文本
				select {
				case resultChan <- qwen.ComprehensiveVoiceResult{
					OriginalText:   transcriptionResult.Text,
					ASRPartial:     transcriptionResult.IsPartial,
					ProcessingType: "asr",
					Timestamp:      timestamp,
					SourceLanguage: sourceLanguage,
					TargetLanguage: targetLanguages,
					IsPartial:      transcriptionResult.IsPartial,
					IsEnd:          false,
				}:
				default:
				}
			}
		}
	}

	return nil
}

// processComprehensiveTranslation 处理综合翻译 - 包含翻译和可选的TTS
func (s *UserService) processComprehensiveTranslation(client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, isPartial bool, resultChan chan<- qwen.ComprehensiveVoiceResult, timestamp int64) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 创建安全的通道发送函数，防止发送到已关闭的通道
	safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
		defer func() {
			if r := recover(); r != nil {
				// 捕获 "send on closed channel" panic
				// 这是正常的，表示接收方已经关闭了通道
			}
		}()

		select {
		case resultChan <- result:
			return true
		case <-ctx.Done():
			return false
		default:
			// 通道可能已满或已关闭，安全跳过
			return false
		}
	}
	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(ctx, sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	var fullTranslation strings.Builder

	// 处理翻译结果
	for translateResult := range translateChan {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return
		default:
		}

		if translateResult.Error != "" {
			safeSend(qwen.ComprehensiveVoiceResult{
				ProcessingType: "translation_error",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          false,
				Error:          translateResult.Error,
			})
			return
		}

		if translateResult.IsEnd {
			// 如果有完整的翻译文本，生成语音响应
			finalTranslation := fullTranslation.String()
			if finalTranslation != "" && !isPartial {
				// 同步生成语音响应，确保在处理完成前发送
				s.processVoiceResponse(client, finalTranslation, sourceLanguage, targetLanguages, resultChan, timestamp)
			}
			break
		}

		if translateResult.Content != "" {
			fullTranslation.WriteString(translateResult.Content)

			// 使用安全发送函数，防止发送到已关闭的通道
			safeSend(qwen.ComprehensiveVoiceResult{
				TranslatedText: translateResult.Content,
				TransPartial:   translateResult.IsPartial,
				ProcessingType: "translation",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      translateResult.IsPartial,
				IsEnd:          false,
			})
		}
	}
}

// processVoiceResponse 处理语音响应生成
func (s *UserService) processVoiceResponse(client *qwen.QwenClient, text, sourceLanguage, targetLanguages string, resultChan chan<- qwen.ComprehensiveVoiceResult, timestamp int64) {
	// 创建安全的通道发送函数，防止发送到已关闭的通道
	safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
		defer func() {
			if r := recover(); r != nil {
				// 捕获 "send on closed channel" panic
				// 这是正常的，表示接收方已经关闭了通道
			}
		}()

		select {
		case resultChan <- result:
			return true
		default:
			// 通道可能已满或已关闭，安全跳过
			return false
		}
	}

	// 生成TTS语音响应
	voiceResponse, err := client.StreamingTTS(text)
	if err != nil {
		safeSend(qwen.ComprehensiveVoiceResult{
			ProcessingType: "tts_error",
			Timestamp:      timestamp,
			SourceLanguage: sourceLanguage,
			TargetLanguage: targetLanguages,
			IsPartial:      false,
			IsEnd:          false,
			Error:          err.Error(),
		})
		return
	}

	// 如果成功生成语音响应，发送结果
	if voiceResponse != nil {
		safeSend(qwen.ComprehensiveVoiceResult{
			VoiceResponse:  voiceResponse,
			ProcessingType: "tts",
			Timestamp:      timestamp,
			SourceLanguage: sourceLanguage,
			TargetLanguage: targetLanguages,
			IsPartial:      false,
			IsEnd:          false,
		})
	}
}

// EnhancedStreamingVoiceTranslate 增强的流式语音翻译 - 返回ASR和翻译结果
func (s *UserService) EnhancedStreamingVoiceTranslate(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动ASR流式转录
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionAudio(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动增强的处理管道 - 返回ASR和翻译结果
	return s.processEnhancedVoicePipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// processEnhancedVoicePipeline 处理增强的语音管道 - 返回ASR和翻译结果
func (s *UserService) processEnhancedVoicePipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	client := qwen.NewQwenClient()

	for transcriptionResult := range transcriptionChan {
		timestamp := time.Now().Unix()

		// 处理错误
		if transcriptionResult.Error != "" {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "asr_error",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
				Error:          transcriptionResult.Error,
			}:
			default:
			}
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "complete",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
			}:
			default:
			}
			break
		}

		// 处理转录结果
		if transcriptionResult.Text != "" {
			// 1. 发送ASR结果
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				OriginalText:   transcriptionResult.Text,
				ASRPartial:     transcriptionResult.IsPartial,
				ProcessingType: "asr",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      transcriptionResult.IsPartial,
				IsEnd:          false,
			}:
			default:
			}

			// 2. 启动翻译处理（不包含TTS）
			go s.processEnhancedTranslation(client, sourceLanguage, targetLanguages, transcriptionResult.Text, transcriptionResult.IsPartial, resultChan, timestamp)
		}
	}

	return nil
}

// processEnhancedTranslation 处理增强的翻译 - 包含翻译和TTS
func (s *UserService) processEnhancedTranslation(client *qwen.QwenClient, sourceLanguage, targetLanguages, text string, isPartial bool, resultChan chan<- qwen.ComprehensiveVoiceResult, timestamp int64) {
	// 添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 创建安全的通道发送函数，防止发送到已关闭的通道
	safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
		defer func() {
			if r := recover(); r != nil {
				// 捕获 "send on closed channel" panic
				// 这是正常的，表示接收方已经关闭了通道
			}
		}()

		select {
		case resultChan <- result:
			return true
		case <-ctx.Done():
			return false
		default:
			// 通道可能已满或已关闭，安全跳过
			return false
		}
	}
	// 创建翻译结果通道
	translateChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动翻译
	go func() {
		defer close(translateChan)
		err := client.StreamingTranslate(ctx, sourceLanguage, targetLanguages, text, translateChan)
		if err != nil {
			select {
			case translateChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	var fullTranslation strings.Builder

	// 处理翻译结果
	for translateResult := range translateChan {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return
		default:
		}

		if translateResult.Error != "" {
			safeSend(qwen.ComprehensiveVoiceResult{
				ProcessingType: "translation_error",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          false,
				Error:          translateResult.Error,
			})
			return
		}

		if translateResult.IsEnd {
			// 如果有完整的翻译文本且不是部分结果，生成语音响应
			finalTranslation := fullTranslation.String()
			if finalTranslation != "" && !isPartial {
				// 生成语音响应
				s.processVoiceResponse(client, finalTranslation, sourceLanguage, targetLanguages, resultChan, timestamp)
			}
			break
		}

		if translateResult.Content != "" {
			fullTranslation.WriteString(translateResult.Content)

			// 使用安全发送函数，防止发送到已关闭的通道
			safeSend(qwen.ComprehensiveVoiceResult{
				TranslatedText: translateResult.Content,
				TransPartial:   translateResult.IsPartial,
				ProcessingType: "translation",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      translateResult.IsPartial,
				IsEnd:          false,
			})
		}
	}
}

// enhancedStreamingTranscriptionWithTranslation 增强的流式转录，利用Transcription方法的基础设施
// 这个方法结合了Transcription方法的翻译能力和流式处理的实时性
func (s *UserService) enhancedStreamingTranscriptionWithTranslation(asrCli *qwen.AsrClient, audio io.Reader, resultChan chan<- qwen.StreamingTranscriptionResult) error {
	// 使用新的增强流式转录方法，它结合了Transcription方法的翻译能力
	// 这样我们可以获得内置的翻译功能，同时保持实时性

	// 使用增强的流式转录方法，包含翻译功能
	return asrCli.StreamingTranscriptionWithTranslation(audio, resultChan)
}

// IncrementalVoiceProcessing 增量语音处理 - 实现真正的实时增量处理
// 对每个部分ASR结果立即进行翻译和TTS处理，而不是等待完整句子
func (s *UserService) IncrementalVoiceProcessing(sourceLanguage, targetLanguages string, audio io.Reader, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	// 创建ASR配置
	config := qwen.AliYunConfig{
		ApiKey:          qwApiKey,
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  sourceLanguage,
		TargetLanguages: targetLanguages,
	}

	asrCli, err := qwen.NewAsrClient(config)
	if err != nil {
		return err
	}

	// 创建转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 启动ASR流式转录
	go func() {
		defer close(transcriptionChan)
		err := asrCli.StreamingTranscriptionWithTranslation(audio, transcriptionChan)
		if err != nil {
			// 发送错误到转录通道
			select {
			case transcriptionChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 启动增量处理管道
	return s.processIncrementalVoicePipeline(sourceLanguage, targetLanguages, transcriptionChan, resultChan)
}

// processIncrementalVoicePipeline 处理增量语音管道 - 对每个部分结果立即处理
func (s *UserService) processIncrementalVoicePipeline(sourceLanguage, targetLanguages string, transcriptionChan <-chan qwen.StreamingTranscriptionResult, resultChan chan<- qwen.ComprehensiveVoiceResult) error {
	client := qwen.NewQwenClient()

	// 创建安全的通道发送函数
	safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
		defer func() {
			if r := recover(); r != nil {
				// 捕获 "send on closed channel" panic
			}
		}()

		select {
		case resultChan <- result:
			return true
		default:
			return false
		}
	}

	// 用于跟踪处理的文本段
	var segmentCounter int

	for transcriptionResult := range transcriptionChan {
		timestamp := time.Now().Unix()
		segmentCounter++

		// 处理错误
		if transcriptionResult.Error != "" {
			safeSend(qwen.ComprehensiveVoiceResult{
				ProcessingType: "error",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
				Error:          transcriptionResult.Error,
			})
			return errors.New(transcriptionResult.Error)
		}

		// 处理结束标记
		if transcriptionResult.IsEnd {
			safeSend(qwen.ComprehensiveVoiceResult{
				ProcessingType: "complete",
				Timestamp:      timestamp,
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguages,
				IsPartial:      false,
				IsEnd:          true,
			})
			break
		}

		// 增量处理转录结果
		if transcriptionResult.Text != "" {
			if transcriptionResult.IsTranslated {
				// 这是翻译结果，立即发送
				safeSend(qwen.ComprehensiveVoiceResult{
					TranslatedText: transcriptionResult.Text,
					TransPartial:   transcriptionResult.IsPartial,
					ProcessingType: "translation",
					Timestamp:      timestamp,
					SourceLanguage: sourceLanguage,
					TargetLanguage: targetLanguages,
					IsPartial:      transcriptionResult.IsPartial,
					IsEnd:          false,
				})

				// 只有在翻译完成时（非部分结果）才生成语音响应
				if !transcriptionResult.IsPartial && len(strings.TrimSpace(transcriptionResult.Text)) > 0 {
					go s.processIncrementalVoiceResponse(client, transcriptionResult.Text, sourceLanguage, targetLanguages, resultChan, timestamp, segmentCounter)
				}
			} else {
				// 这是ASR结果，发送原始转录文本
				safeSend(qwen.ComprehensiveVoiceResult{
					OriginalText:   transcriptionResult.Text,
					ASRPartial:     transcriptionResult.IsPartial,
					ProcessingType: "asr",
					Timestamp:      timestamp,
					SourceLanguage: sourceLanguage,
					TargetLanguage: targetLanguages,
					IsPartial:      transcriptionResult.IsPartial,
					IsEnd:          false,
				})
			}
		}
	}

	return nil
}

// processIncrementalVoiceResponse 处理增量语音响应生成 - 为每个文本段立即生成语音
func (s *UserService) processIncrementalVoiceResponse(client *qwen.QwenClient, text, sourceLanguage, targetLanguages string, resultChan chan<- qwen.ComprehensiveVoiceResult, timestamp int64, segmentID int) {
	// 创建安全的通道发送函数
	safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
		defer func() {
			if r := recover(); r != nil {
				// 捕获 "send on closed channel" panic
			}
		}()

		select {
		case resultChan <- result:
			return true
		default:
			return false
		}
	}

	// 生成TTS语音响应
	voiceResponse, err := client.StreamingTTS(text)
	if err != nil {
		safeSend(qwen.ComprehensiveVoiceResult{
			ProcessingType: "tts_error",
			Timestamp:      timestamp,
			SourceLanguage: sourceLanguage,
			TargetLanguage: targetLanguages,
			IsPartial:      false,
			IsEnd:          false,
			Error:          fmt.Sprintf("TTS error for segment %d: %v", segmentID, err),
		})
		return
	}

	// 如果成功生成语音响应，保存音频文件并发送结果
	if voiceResponse != nil {
		// 为增量处理添加段标识
		if voiceResponse.Metadata == nil {
			voiceResponse.Metadata = make(map[string]string)
		}
		voiceResponse.Metadata["segment_id"] = fmt.Sprintf("%d", segmentID)
		voiceResponse.Metadata["processing_type"] = "incremental"
		voiceResponse.Metadata["text_length"] = fmt.Sprintf("%d", len(text))
		voiceResponse.Metadata["translated_text"] = text

		// 保存音频文件到磁盘
		audioFilePath, audioFileErr := s.saveIncrementalAudioFile(voiceResponse, segmentID, timestamp)
		if audioFileErr != nil {
			// 记录错误但不中断处理
			if global.GVA_LOG != nil {
				global.GVA_LOG.Error("Failed to save audio file",
					zap.Int("segment_id", segmentID),
					zap.Int64("timestamp", timestamp),
					zap.Error(audioFileErr),
				)
			}
		} else {
			// 添加文件路径到元数据
			voiceResponse.Metadata["saved_file_path"] = audioFilePath
			if global.GVA_LOG != nil {
				global.GVA_LOG.Info("Audio file saved successfully",
					zap.String("file_path", audioFilePath),
					zap.Int("segment_id", segmentID),
					zap.String("text", text),
					zap.Float64("duration", voiceResponse.Duration),
				)
			}
		}

		safeSend(qwen.ComprehensiveVoiceResult{
			VoiceResponse:  voiceResponse,
			ProcessingType: "tts",
			Timestamp:      timestamp,
			SourceLanguage: sourceLanguage,
			TargetLanguage: targetLanguages,
			IsPartial:      false,
			IsEnd:          false,
		})
	}
}

// saveIncrementalAudioFile 保存增量音频文件
func (s *UserService) saveIncrementalAudioFile(voiceResponse *qwen.VoiceResponseData, segmentID int, timestamp int64) (string, error) {
	if voiceResponse == nil || len(voiceResponse.AudioData) == 0 {
		return "", fmt.Errorf("no audio data to save")
	}

	// 获取音频文件管理器
	audioManager := utils.GetDefaultAudioFileManager()

	// 保存音频文件
	filePath, err := audioManager.SaveAudioFile(
		voiceResponse.AudioData,
		segmentID,
		timestamp,
		voiceResponse.AudioFormat,
	)
	if err != nil {
		return "", fmt.Errorf("failed to save audio file: %w", err)
	}

	// 验证保存的音频文件
	audioFileInfo, err := audioManager.VerifyAudioFile(filePath, voiceResponse.Duration, voiceResponse.AudioFormat)
	if err != nil {
		if global.GVA_LOG != nil {
			global.GVA_LOG.Warn("Audio file verification failed",
				zap.String("file_path", filePath),
				zap.Error(err),
			)
		}
		// 验证失败不影响文件保存，只记录警告
	} else {
		if global.GVA_LOG != nil {
			global.GVA_LOG.Info("Audio file verification successful",
				zap.String("file_path", filePath),
				zap.Int64("file_size", audioFileInfo.FileSize),
				zap.Float64("duration", audioFileInfo.Duration),
				zap.String("format", audioFileInfo.Format),
			)
		}
	}

	return filePath, nil
}

// verifyIncrementalAudioContent 验证增量音频内容
func (s *UserService) verifyIncrementalAudioContent(filePath string, expectedText string, voiceResponse *qwen.VoiceResponseData) error {
	// 获取音频文件管理器
	audioManager := utils.GetDefaultAudioFileManager()

	// 获取文件信息
	audioFileInfo, err := audioManager.GetAudioFileInfo(filePath)
	if err != nil {
		return fmt.Errorf("failed to get audio file info: %w", err)
	}

	// 验证文件大小
	if audioFileInfo.FileSize == 0 {
		return fmt.Errorf("audio file is empty")
	}

	// 验证格式匹配
	if audioFileInfo.Format != voiceResponse.AudioFormat {
		return fmt.Errorf("audio format mismatch: expected %s, got %s", voiceResponse.AudioFormat, audioFileInfo.Format)
	}

	// 验证文件大小与音频数据大小匹配
	expectedSize := int64(len(voiceResponse.AudioData))
	if audioFileInfo.FileSize != expectedSize {
		return fmt.Errorf("file size mismatch: expected %d bytes, got %d bytes", expectedSize, audioFileInfo.FileSize)
	}

	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("Audio content verification completed",
			zap.String("file_path", filePath),
			zap.String("expected_text", expectedText),
			zap.Int64("file_size", audioFileInfo.FileSize),
			zap.String("format", audioFileInfo.Format),
			zap.Float64("duration", voiceResponse.Duration),
		)
	}

	return nil
}

// SaveIncrementalAudioFile 公开的音频文件保存方法（用于测试）
func (s *UserService) SaveIncrementalAudioFile(voiceResponse *qwen.VoiceResponseData, segmentID int, timestamp int64) (string, error) {
	return s.saveIncrementalAudioFile(voiceResponse, segmentID, timestamp)
}

package airport

import (
	"airAi/global"
	"gorm.io/gorm"
	"model/airpods"
)

var (
	count = 50
	price = 10
)

func (s *UserService) MakeAddSendRequest(userID uint) error {
	db := global.GVA_DB
	// 使用事务确保操作的原子性
	return db.Transaction(func(tx *gorm.DB) error {
		var quota airpods.Quota
		if err := tx.Where("uid = ?", userID).First(&quota).Error; err != nil {
			return err
		}
		quota.CurrentUsage++
		return tx.Save(&quota).Error
	})
}

func (s *UserService) Deposit(userID uint, units int) error {
	db := global.GVA_DB
	// 使用事务确保数据一致性
	return db.Transaction(func(tx *gorm.DB) error {
		// 更新配额
		var quota airpods.Quota
		if err := tx.Where("uid = ?", userID).First(&quota).Error; err != nil {
			return err
		}

		quota.MonthlyQuota += units * count
		if err := tx.Save(&quota).Error; err != nil {
			return err
		}

		// 记录购买
		amount := units * price // 每单位50次10元
		record := airpods.PurchaseRecord{
			Uid:           userID,
			PurchaseUnits: units,
			Amount:        float64(amount),
		}
		return tx.Create(&record).Error
	})
}

func (s *UserService) CanMakeSendRequest(userID uint) (bool, error) {
	var quota airpods.Quota
	if err := global.GVA_DB.Where("uid = ?", userID).First(&quota).Error; err != nil {
		return false, err
	}

	totalAvailable := quota.MonthlyQuota
	return quota.CurrentUsage < totalAvailable, nil
}

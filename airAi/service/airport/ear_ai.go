package airport

import (
	"airAi/common/requst"
	"airAi/global"
	"airAi/other_api/deepseek"
	"airAi/other_api/qwen"
	"airAi/other_api/xunfei"
	"airAi/utils"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"time"

	"go.uber.org/zap"
)

type UserService struct {
}

func (s *UserService) Chat() {

}

var (
	api   = "/v2/tts"
	rtApi = "/v2/iat"
)

func (s *UserService) RealTime(audio io.Reader) (str string, err error) {
	host := global.GVA_CONFIG.XunfeiConfig.WS + rtApi
	cli := xunfei.NewClient(xunfei.Config{
		Host:      host,
		AppID:     global.GVA_CONFIG.XunfeiConfig.AppID,
		APIKey:    global.GVA_CONFIG.XunfeiConfig.APIKey,
		APISecret: global.GVA_CONFIG.XunfeiConfig.APISecret,
		FrameSize: 0,
	})
	_, err = cli.ConnectWebSocket()
	if err != nil {
		return
	}
	rsp, err := cli.Recognize(context.Background(), audio, "")
	if err != nil {
		return
	}
	if len(rsp) <= 0 {
		err = errors.New("no dataset")
		return
	}
	return rsp[0].Text, nil
}

func (s *UserService) TextToSpeech(text string) (audio []byte, err error) {
	host := global.GVA_CONFIG.XunfeiConfig.WS + api
	client := xunfei.NewClient(xunfei.Config{
		Host:      host,
		AppID:     global.GVA_CONFIG.XunfeiConfig.AppID,
		APIKey:    global.GVA_CONFIG.XunfeiConfig.APIKey,
		APISecret: global.GVA_CONFIG.XunfeiConfig.APISecret,
		FrameSize: 0,
	})
	return client.TextToSpeech(text)

}

func (s *UserService) TToSpeech(text string) (audio []byte, err error) {
	// 使用默认语音速率1.0调用新方法
	return s.TToSpeechWithRate(text, 1.0)
}

// TToSpeechWithRate 带语音速率控制的文字转语音
func (s *UserService) TToSpeechWithRate(text string, speechRate float64) (audio []byte, err error) {
	global.GVA_LOG.Info("准备生成语音",
		zap.String("text", text),
		zap.Float64("speechRate", speechRate),
	)

	// 生成唯一文件名
	str := utils.MD5V([]byte(text))
	outputFile := fmt.Sprintf("./%s.mp3", str)

	// 确保函数结束时删除临时文件
	defer func() {
		if _, err := os.Stat(outputFile); err == nil {
			os.Remove(outputFile)
		}
	}()

	// 生成语音文件，传入语音速率参数
	err = qwen.NewSambertWithRate("sambert-zhinan-v1", text, outputFile, speechRate)
	if err != nil {
		return nil, fmt.Errorf("failed to generate speech: %w", err)
	}

	// 读取音频文件内容
	audio, err = os.ReadFile(outputFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read audio file: %w", err)
	}
	global.GVA_LOG.Info("语音生成完成",
		zap.Float64("speechRate", speechRate),
		zap.Int("audioSize", len(audio)),
	)
	return audio, nil
}

func (s *UserService) TextChat(speechType, text string) (str string, err error) {
	global.GVA_LOG.Info("ai TextChat start")
	defer func() {
		global.GVA_LOG.Info("Ai TextChat end ")
	}()
	switch speechType {
	case "deepseek":
		cli := deepseek.NewClient()
		rsp, err := cli.ChatMessage(text)
		if err != nil {
			return "", err
		}
		if len(rsp.Choices) == 0 {
			return "", errors.New("no choices")
		}
		return rsp.Choices[0].Message.Content, err
	case "qwen":
		cli := qwen.NewQwenClient()
		return cli.Text(context.Background(), text)
	default:
		cli := qwen.NewQwenClient()
		return cli.Text(context.Background(), text)
	}

	return "", err

}

// StreamingTextChatWithContext 带对话上下文的流式文本聊天服务方法
// 支持对话历史、会话管理和上下文窗口控制
// 参数：
//   - speechType: AI模型类型，支持 "deepseek" 和 "qwen"
//   - text: 用户输入的文本内容
//   - conversationID: 对话会话ID，用于维护对话上下文
//   - messages: 对话历史消息列表
//   - contextWindow: 上下文窗口大小，限制发送给AI的历史消息数量
//   - systemPrompt: 系统提示词，用于设置AI的行为和角色
//   - resultChan: 用于接收流式响应结果的通道
//
// 返回：
//   - conversationID: 实际使用的对话ID（如果输入为空则生成新ID）
//   - error: 如果发生错误则返回错误信息
func (s *UserService) StreamingTextChatWithContext(speechType, text, conversationID string, messages []requst.ConversationMessage, contextWindow int, systemPrompt string, resultChan chan<- interface{}) (string, error) {
	global.GVA_LOG.Info("ai StreamingTextChatWithContext start")
	defer func() {
		global.GVA_LOG.Info("ai StreamingTextChatWithContext end")
	}()

	// 获取对话上下文管理器
	contextManager := GetConversationManager()

	// 获取或创建对话上下文
	convContext := contextManager.GetOrCreateContext(conversationID, systemPrompt)
	actualConversationID := convContext.ConversationID

	// 如果提供了历史消息，更新上下文
	if len(messages) > 0 {
		for _, msg := range messages {
			contextManager.AddMessage(actualConversationID, msg)
		}
	}

	// 添加当前用户消息到上下文
	userMessage := requst.ConversationMessage{
		Role:      "user",
		Content:   text,
		Timestamp: time.Now().Unix(),
	}
	contextManager.AddMessage(actualConversationID, userMessage)

	// 设置默认上下文窗口大小
	if contextWindow <= 0 {
		contextWindow = 10 // 默认保留最近10条消息
	}

	// 获取上下文消息（包括系统提示词和历史消息）
	contextMessages := contextManager.GetContextMessages(actualConversationID, contextWindow)

	ctx := context.Background()

	switch speechType {
	case "deepseek":
		return actualConversationID, s.streamingDeepSeekWithContext(ctx, contextMessages, systemPrompt, resultChan)
	case "qwen":
		return actualConversationID, s.streamingQwenWithContext(ctx, contextMessages, systemPrompt, resultChan)
	default:
		// 默认使用qwen
		return actualConversationID, s.streamingQwenWithContext(ctx, contextMessages, systemPrompt, resultChan)
	}
}

// StreamingTextChat 流式文本聊天服务方法
// 根据指定的AI模型类型（deepseek/qwen）进行流式文本对话
// 参数：
//   - speechType: AI模型类型，支持 "deepseek" 和 "qwen"
//   - text: 用户输入的文本内容
//   - resultChan: 用于接收流式响应结果的通道
//
// 返回：
//   - error: 如果发生错误则返回错误信息
func (s *UserService) StreamingTextChat(speechType, text string, resultChan chan<- interface{}) error {
	global.GVA_LOG.Info("ai StreamingTextChat start")
	defer func() {
		global.GVA_LOG.Info("ai StreamingTextChat end")
	}()

	ctx := context.Background()

	switch speechType {
	case "deepseek":
		// 使用DeepSeek进行流式聊天
		cli := deepseek.NewClient()

		// 创建DeepSeek专用的结果通道
		deepseekChan := make(chan deepseek.StreamingChatResult, 10)

		// 启动DeepSeek流式聊天
		go func() {
			defer close(deepseekChan)
			err := cli.StreamingTextChat(ctx, text, deepseekChan)
			if err != nil {
				// 发送错误到通道
				select {
				case deepseekChan <- deepseek.StreamingChatResult{
					Content:   "",
					IsPartial: false,
					IsEnd:     true,
					Error:     err.Error(),
				}:
				default:
				}
			}
		}()

		// 转发DeepSeek结果到通用结果通道
		for result := range deepseekChan {
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// 通道已满，跳过此结果
			}

			// 如果是结束标记，退出循环
			if result.IsEnd {
				break
			}
		}

	case "qwen":
		// 使用Qwen进行流式聊天
		cli := qwen.NewQwenClient()

		// 创建Qwen专用的结果通道
		qwenChan := make(chan qwen.StreamingChatResult, 10)

		// 启动Qwen流式聊天
		go func() {
			defer close(qwenChan)
			err := cli.StreamingTextChat(ctx, text, qwenChan)
			if err != nil {
				// 发送错误到通道
				select {
				case qwenChan <- qwen.StreamingChatResult{
					Content:   "",
					IsPartial: false,
					IsEnd:     true,
					Error:     err.Error(),
				}:
				default:
				}
			}
		}()

		// 转发Qwen结果到通用结果通道
		for result := range qwenChan {
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// 通道已满，跳过此结果
			}

			// 如果是结束标记，退出循环
			if result.IsEnd {
				break
			}
		}

	default:
		// 默认使用Qwen
		cli := qwen.NewQwenClient()

		// 创建Qwen专用的结果通道
		qwenChan := make(chan qwen.StreamingChatResult, 10)

		// 启动Qwen流式聊天
		go func() {
			defer close(qwenChan)
			err := cli.StreamingTextChat(ctx, text, qwenChan)
			if err != nil {
				// 发送错误到通道
				select {
				case qwenChan <- qwen.StreamingChatResult{
					Content:   "",
					IsPartial: false,
					IsEnd:     true,
					Error:     err.Error(),
				}:
				default:
				}
			}
		}()

		// 转发Qwen结果到通用结果通道
		for result := range qwenChan {
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// 通道已满，跳过此结果
			}

			// 如果是结束标记，退出循环
			if result.IsEnd {
				break
			}
		}
	}

	return nil
}

// streamingDeepSeekWithContext 使用DeepSeek进行带上下文的流式聊天
func (s *UserService) streamingDeepSeekWithContext(ctx context.Context, contextMessages []requst.ConversationMessage, systemPrompt string, resultChan chan<- interface{}) error {
	cli := deepseek.NewClient()

	// 创建DeepSeek专用的结果通道
	deepseekChan := make(chan deepseek.StreamingChatResult, 10)

	// 启动DeepSeek流式聊天
	go func() {
		defer close(deepseekChan)
		err := cli.StreamingTextChatWithContext(ctx, contextMessages, systemPrompt, deepseekChan)
		if err != nil {
			// 发送错误到通道
			select {
			case deepseekChan <- deepseek.StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 转发DeepSeek结果到通用结果通道
	for result := range deepseekChan {
		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}

	return nil
}

// streamingQwenWithContext 使用Qwen进行带上下文的流式聊天
func (s *UserService) streamingQwenWithContext(ctx context.Context, contextMessages []requst.ConversationMessage, systemPrompt string, resultChan chan<- interface{}) error {
	cli := qwen.NewQwenClient()

	// 创建Qwen专用的结果通道
	qwenChan := make(chan qwen.StreamingChatResult, 10)

	// 启动Qwen流式聊天
	go func() {
		defer close(qwenChan)
		err := cli.StreamingTextChatWithContext(ctx, contextMessages, systemPrompt, qwenChan)
		if err != nil {
			// 发送错误到通道
			select {
			case qwenChan <- qwen.StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 转发Qwen结果到通用结果通道
	for result := range qwenChan {
		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}

	return nil
}

package config

// QwenConfig Qwen AI服务配置
type QwenConfig struct {
	// API密钥
	APIKey string `mapstructure:"api-key" json:"api-key" yaml:"api-key"`

	// 基础URL
	BaseURL string `mapstructure:"base-url" json:"base-url" yaml:"base-url"`

	// 默认模型配置
	DefaultModel string `mapstructure:"default-model" json:"default-model" yaml:"default-model"`

	// ASR配置
	ASR QwenASRConfig `mapstructure:"asr" json:"asr" yaml:"asr"`

	// 翻译配置
	Translation QwenTranslationConfig `mapstructure:"translation" json:"translation" yaml:"translation"`

	// TTS配置
	TTS QwenTTSConfig `mapstructure:"tts" json:"tts" yaml:"tts"`
}

// QwenASRConfig ASR语音识别配置
type QwenASRConfig struct {
	// 默认模型
	DefaultModel string `mapstructure:"default-model" json:"default-model" yaml:"default-model"`

	// 支持翻译的模型
	TranslationModel string `mapstructure:"translation-model" json:"translation-model" yaml:"translation-model"`

	// 默认音频格式
	DefaultFormat string `mapstructure:"default-format" json:"default-format" yaml:"default-format"`

	// 默认源语言
	DefaultSourceLanguage string `mapstructure:"default-source-language" json:"default-source-language" yaml:"default-source-language"`

	// 默认目标语言
	DefaultTargetLanguages []string `mapstructure:"default-target-languages" json:"default-target-languages" yaml:"default-target-languages"`

	// 连接超时时间（秒）
	ConnectionTimeout int `mapstructure:"connection-timeout" json:"connection-timeout" yaml:"connection-timeout"`

	// 最大重连次数
	MaxReconnectAttempts int `mapstructure:"max-reconnect-attempts" json:"max-reconnect-attempts" yaml:"max-reconnect-attempts"`
}

// QwenTranslationConfig 翻译配置
type QwenTranslationConfig struct {
	// 默认模型
	DefaultModel string `mapstructure:"default-model" json:"default-model" yaml:"default-model"`

	// 默认源语言
	DefaultSourceLanguage string `mapstructure:"default-source-language" json:"default-source-language" yaml:"default-source-language"`

	// 默认目标语言
	DefaultTargetLanguage string `mapstructure:"default-target-language" json:"default-target-language" yaml:"default-target-language"`

	// 请求超时时间（秒）
	RequestTimeout int `mapstructure:"request-timeout" json:"request-timeout" yaml:"request-timeout"`
}

// QwenTTSConfig TTS语音合成配置
type QwenTTSConfig struct {
	// 默认模型
	DefaultModel string `mapstructure:"default-model" json:"default-model" yaml:"default-model"`

	// 默认语音
	DefaultVoice string `mapstructure:"default-voice" json:"default-voice" yaml:"default-voice"`

	// 默认语音速率
	DefaultSpeechRate float64 `mapstructure:"default-speech-rate" json:"default-speech-rate" yaml:"default-speech-rate"`

	// 默认音频格式
	DefaultFormat string `mapstructure:"default-format" json:"default-format" yaml:"default-format"`

	// 请求超时时间（秒）
	RequestTimeout int `mapstructure:"request-timeout" json:"request-timeout" yaml:"request-timeout"`
}

package config

// WechatConfig 微信OAuth配置
type WechatConfig struct {
	// 微信应用ID
	AppID string `mapstructure:"app-id" json:"app-id" yaml:"app-id"`

	// 微信应用密钥
	AppSecret string `mapstructure:"app-secret" json:"app-secret" yaml:"app-secret"`

	// 授权回调地址
	RedirectURL string `mapstructure:"redirect-url" json:"redirect-url" yaml:"redirect-url"`

	// 微信OAuth授权URL
	AuthURL string `mapstructure:"auth-url" json:"auth-url" yaml:"auth-url"`

	// 微信获取access_token的URL
	TokenURL string `mapstructure:"token-url" json:"token-url" yaml:"token-url"`

	// 微信获取用户信息的URL
	UserInfoURL string `mapstructure:"user-info-url" json:"user-info-url" yaml:"user-info-url"`

	// 授权作用域
	Scope string `mapstructure:"scope" json:"scope" yaml:"scope"`

	// 是否启用微信登录
	Enabled bool `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
}

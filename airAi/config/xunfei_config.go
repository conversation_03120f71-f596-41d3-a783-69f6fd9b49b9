package config

type XunfeiConfig struct {
	AppID     string `mapstructure:"appid" json:"appid" yaml:"appid"`
	HTTP      string `mapstructure:"http"  json:"http" yaml:"http"`
	WS        string `mapstructure:"ws"  json:"ws" yaml:"ws"`
	APISecret string `mapstructure:"api-secret"  json:"apiSecret" yaml:"api-secret"`
	APIKey    string `mapstructure:"api-key" json:"apiKey" yaml:"api-key"`
	//RealTime RealTime `mapstructure:"real-time" json:"real-time" yaml:"real-time"`
	//Tts      Tts      `mapstructure:"tts" json:"tts" yaml:"tts"`
}

type RealTime struct {
	HTTP      string `mapstructure:"http"  json:"http" yaml:"http"`
	WS        string `mapstructure:"ws"  json:"ws" yaml:"ws"`
	APISecret string `mapstructure:"api-secret"  json:"apiSecret" yaml:"api-secret"`
	APIKey    string `mapstructure:"api-key" json:"apiKey" yaml:"api-key"`
}

type Tts struct {
	HTTP      string `mapstructure:"http"  json:"http" yaml:"http"`
	WS        string `mapstructure:"ws"  json:"ws" yaml:"ws"`
	APISecret string `mapstructure:"api-secret"  json:"apiSecret" yaml:"api-secret"`
	APIKey    string `mapstructure:"api-key" json:"apiKey" yaml:"api-key"`
}

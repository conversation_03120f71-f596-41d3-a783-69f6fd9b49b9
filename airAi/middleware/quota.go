package middleware

import (
	"strconv"
	"time"

	"airAi/common/response"
	"airAi/core/i18n"
	"airAi/global"
	"airAi/service"
	"airAi/service/airport"
	"airAi/utils"
	"model/airpods"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var quotaService = service.ServiceGroupApp.AirportServiceGroup.QuotaService

// QuotaAuth 配额验证中间件
func QuotaAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userID := utils.GetUserID(c)
		if userID == 0 {
			// 如果没有用户ID，说明用户未登录，让JWT中间件处理
			c.Next()
			return
		}

		// 获取语言设置
		lang := i18n.GetLangFromContext(c)

		// 检查配额
		canUse, quota, err := quotaService.CheckQuota(userID)
		if err != nil {
			global.GVA_LOG.Error("配额检查失败",
				zap.Uint("user_id", userID),
				zap.Error(err),
				zap.String("path", c.Request.URL.Path))

			errorMessage := i18n.T(lang, "quota.check_failed", map[string]interface{}{
				"Error": err.Error(),
			})
			response.FailWithMessage(errorMessage, c)
			c.Abort()
			return
		}

		if !canUse {
			global.GVA_LOG.Warn("用户配额不足",
				zap.Uint("user_id", userID),
				zap.Int("total_quota", quota.GetTotalQuota()),
				zap.Int("current_usage", quota.CurrentUsage),
				zap.Int("remaining", quota.GetRemainingQuota()),
				zap.String("path", c.Request.URL.Path))

			// 构建配额不足的错误响应
			errorData := map[string]interface{}{
				"total_quota":              quota.GetTotalQuota(),
				"current_usage":            quota.CurrentUsage,
				"remaining":                quota.GetRemainingQuota(),
				"subscription_active":      quota.IsSubscriptionActive(),
				"extra_quota_price":        airport.ExtraQuotaPrice,
				"extra_quota_unit":         airport.ExtraQuotaUnit,
				"monthly_subscription_fee": airport.MonthlySubscriptionFee,
			}

			errorMessage := i18n.TSimple(lang, "quota.insufficient")
			response.FailWithQuotaExceeded(errorMessage, errorData, c)
			c.Abort()
			return
		}

		// 将配额信息存储到上下文中，供后续使用
		c.Set("quota_info", quota)
		c.Set("user_quota_checked", true)

		global.GVA_LOG.Debug("配额验证通过",
			zap.Uint("user_id", userID),
			zap.Int("remaining", quota.GetRemainingQuota()),
			zap.String("path", c.Request.URL.Path))

		c.Next()
	}
}

// QuotaUsage 配额使用中间件（在请求处理完成后扣减配额）
func QuotaUsage(usageType int) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行请求处理
		c.Next()

		// 检查是否已经验证过配额
		if checked, exists := c.Get("user_quota_checked"); !exists || !checked.(bool) {
			// 如果没有验证过配额，说明可能是公开接口或者验证失败，不扣减配额
			return
		}

		// 获取用户ID
		userID := utils.GetUserID(c)
		if userID == 0 {
			return
		}

		// 只有在请求成功时才扣减配额
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			// 构建请求信息
			requestInfo := map[string]interface{}{
				"request_path":   c.Request.URL.Path,
				"request_method": c.Request.Method,
				"client_ip":      c.ClientIP(),
				"user_agent":     c.Request.UserAgent(),
				"request_size":   c.Request.ContentLength,
				"response_size":  c.Writer.Size(),
			}

			// 如果有对话ID，添加到请求信息中
			if conversationID, exists := c.Get("conversation_id"); exists {
				requestInfo["conversation_id"] = conversationID
			}

			// 扣减配额
			if err := quotaService.UseQuota(userID, usageType, requestInfo); err != nil {
				global.GVA_LOG.Error("配额扣减失败",
					zap.Uint("user_id", userID),
					zap.Int("usage_type", usageType),
					zap.Error(err),
					zap.String("path", c.Request.URL.Path))
				// 注意：配额扣减失败不影响响应，但要记录错误
			} else {
				global.GVA_LOG.Debug("配额扣减成功",
					zap.Uint("user_id", userID),
					zap.Int("usage_type", usageType),
					zap.String("path", c.Request.URL.Path))
			}
		}
	}
}

// QuotaChatAuth 聊天配额验证中间件（包含验证和使用）
func QuotaChatAuth() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 先进行配额验证
		QuotaAuth()(c)
		if c.IsAborted() {
			return
		}

		// 然后设置配额使用
		QuotaUsage(airport.UsageTypeChat)(c)
	})
}

// QuotaASRAuth 语音识别配额验证中间件
func QuotaASRAuth() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		QuotaAuth()(c)
		if c.IsAborted() {
			return
		}
		QuotaUsage(airport.UsageTypeASR)(c)
	})
}

// QuotaTTSAuth 文字转语音配额验证中间件
func QuotaTTSAuth() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		QuotaAuth()(c)
		if c.IsAborted() {
			return
		}
		QuotaUsage(airport.UsageTypeTTS)(c)
	})
}

// AddQuotaInfoToResponse 在响应中添加配额信息
func AddQuotaInfoToResponse(c *gin.Context, data map[string]interface{}) {
	if quotaInfo, exists := c.Get("quota_info"); exists {
		if quota, ok := quotaInfo.(*airpods.Quota); ok {
			// 重新获取最新的配额信息（因为可能已经扣减了）
			userID := utils.GetUserID(c)
			if userID > 0 {
				if latestQuota, err := quotaService.GetQuotaInfo(userID); err == nil {
					quota = latestQuota
				}
			}

			data["quota_info"] = map[string]interface{}{
				"total_quota":         quota.GetTotalQuota(),
				"current_usage":       quota.CurrentUsage,
				"remaining":           quota.GetRemainingQuota(),
				"subscription_active": quota.IsSubscriptionActive(),
				"subscription_expiry": quota.SubscriptionExpiry,
				"last_reset_date":     quota.LastResetDate,
			}
		}
	}
}

// GetQuotaInfoHeader 设置配额信息到响应头
func GetQuotaInfoHeader(c *gin.Context) {
	if quotaInfo, exists := c.Get("quota_info"); exists {
		if quota, ok := quotaInfo.(*airpods.Quota); ok {
			c.Header("X-Quota-Total", strconv.Itoa(quota.GetTotalQuota()))
			c.Header("X-Quota-Used", strconv.Itoa(quota.CurrentUsage))
			c.Header("X-Quota-Remaining", strconv.Itoa(quota.GetRemainingQuota()))
			c.Header("X-Quota-Reset-Date", quota.LastResetDate.Format(time.RFC3339))
			if quota.IsSubscriptionActive() {
				c.Header("X-Subscription-Expiry", quota.SubscriptionExpiry.Format(time.RFC3339))
			}
		}
	}
}

package middleware

import (
	"airAi/service"
	"airAi/utils"
	"github.com/gin-gonic/gin"
	"model/common/response"
)

var userService = service.ServiceGroupApp.AirportServiceGroup.UserService

func VipAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := utils.GetUserID(c)
		ok, err := userService.CanMakeSendRequest(uid)
		if err != nil {
			response.NoAuth(err.Error(), c)
			c.Abort()
			return
		}
		if !ok {
			response.NoInsufficientBalance("No Insufficient Balance", c)
			c.Abort()
			return
		}
	}
}

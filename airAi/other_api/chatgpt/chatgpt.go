package chatgpt

import (
	"airAi/global"
	"airAi/other_api"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

const (
	apiURL = "https://api.openai.com/v1/chat/completions"
)

func GenerateContent(base64Image string) (animal other_api.Animal, err error) {
	// 构造 API 请求数据
	requestBody := OpenAIRequest{
		Model: "gpt-4o",
		Messages: []Message{
			{
				Role: "user",
				Content: []Content{
					{Type: "text", Text: `Please identify this bird and provide the following information in JSON format: 
{
  "name": "Common Name",
  "scientific_name": "Scientific Name",
  "habitat": "Habitat",
  "feeding_habits": "Feeding Habits",
  "description": "Brief Description"
}
Ensure the key for the common name is strictly 'name' and NOT 'bird_name'.`},
					{Type: "image_url", ImageUrl: ImageUrl{
						Url: fmt.Sprintf("data:image/jpeg;base64,%s", base64Image),
					}},
				},
			},
		},
	}
	// 发送 API 请求
	resp, err := sendOpenAIRequest(requestBody)
	if err != nil {
		fmt.Println("请求失败:", err)
		return
	}
	// 解析响应
	fmt.Println("识别结果:", resp)
	jsonData := strings.TrimSpace(resp) // 去掉首尾空白字符
	jsonData = strings.TrimPrefix(jsonData, "```json")
	jsonData = strings.TrimSuffix(jsonData, "```")
	jsonData = strings.TrimSpace(jsonData) // 去掉去掉标记后的首尾空白字符

	// 声明一个 map 变量
	var result other_api.Animal

	// 将 JSON 数据解析到 map 中
	err = json.Unmarshal([]byte(jsonData), &result)
	if err != nil {
		return
	}
	return result, nil

}

// 发送 API 请求
func sendOpenAIRequest(requestBody OpenAIRequest) (string, error) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+global.GVA_CONFIG.Chatgpt.Key)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	fmt.Println(string(body))

	var response OpenAIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}

	if len(response.Choices) > 0 {
		return response.Choices[0].Message.Content, nil
	}

	return "未识别到鸟类信息", nil
}

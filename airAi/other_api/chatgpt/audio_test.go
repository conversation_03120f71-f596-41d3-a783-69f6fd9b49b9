package chatgpt

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	openai "github.com/sashabaranov/go-openai"
)

var key = "********************************************************************************************************************************************************************"

func TestAudio(t *testing.T) {
	client := openai.NewClient(key)
	// 打开音频文件
	audioFile, err := os.Open("audio.mp3")
	if err != nil {
		fmt.Printf("Error opening audio file: %v\n", err)
		return
	}
	defer audioFile.Close()

	// 创建转录请求
	req := openai.AudioRequest{
		Model:    openai.Whisper1,
		FilePath: "audio.mp3",
		Reader:   audioFile,
		Format:   openai.AudioResponseFormatJSON,
	}

	// 发送请求
	resp, err := client.CreateTranscription(context.Background(), req)
	if err != nil {
		fmt.Printf("Transcription error: %v\n", err)
		return
	}

	fmt.Printf("Transcription: %s\n", resp.Text)
}

func TestTTsToAudio(t *testing.T) {
	client := openai.NewClient(key)
	text := "你好，这是一个文字转语音的测试。今天天气真好，适合学习新知识！"

	// 创建TTS请求
	req := openai.CreateSpeechRequest{
		Model:          openai.TTSModel1,
		Input:          text,
		Voice:          openai.VoiceAlloy,              // 可选: alloy, echo, fable, onyx, nova, shimmer
		ResponseFormat: openai.SpeechResponseFormatMp3, // 可选: mp3, opus, aac, flac
		Speed:          1.0,                            // 语速 (0.25-4.0)
	}

	// 发送请求并获取音频流
	resp, err := client.CreateSpeech(context.Background(), req)
	if err != nil {
		fmt.Printf("TTS error: %v\n", err)
		return
	}
	defer resp.Close()
	// 创建输出文件
	outputFile := fmt.Sprintf("speech_%d.mp3", time.Now().Unix())
	file, err := os.Create(outputFile)
	if err != nil {
		fmt.Printf("File creation error: %v\n", err)
		return
	}
	defer file.Close()

	// 将音频写入文件
	_, err = io.Copy(file, resp)
	if err != nil {
		fmt.Printf("Audio save error: %v\n", err)
		return
	}

	fmt.Printf("语音文件已生成: %s\n", outputFile)
}

func TestGenerateImage(t *testing.T) {
	client := openai.NewClient(key)
	prompt := "一只穿着宇航服的柴犬在月球上散步，数字油画风格"

	// 创建图片生成请求
	req := openai.ImageRequest{
		Prompt:         prompt,
		Size:           openai.CreateImageSize1024x1024,     // 可选: 256x256, 512x512, 1024x1024
		ResponseFormat: openai.CreateImageResponseFormatURL, // 可选: URL 或 B64_JSON
		N:              1,                                   // 生成图片数量
	}

	// 发送请求
	resp, err := client.CreateImage(context.Background(), req)
	if err != nil {
		fmt.Printf("Image generation error: %v\n", err)
		return
	}

	// 处理返回的图片URL
	for _, img := range resp.Data {
		fmt.Printf("Generated image URL: %s\n", img.URL)

		// 下载图片到本地
		if err := downloadImage(img.URL, fmt.Sprintf("image_%d.png", time.Now().Unix())); err != nil {
			fmt.Printf("Failed to download image: %v\n", err)
		}
	}
}

// 下载图片到本地
func downloadImage(url, filename string) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = io.Copy(file, resp.Body)
	return err
}

package xunfei

// 定义内部结构体以匹配JSON中的嵌套结构
type Cw struct {
	Sc int    `json:"sc"`
	W  string `json:"w"`
}

type Ws struct {
	Bg int  `json:"bg"`
	Cw []Cw `json:"cw"`
}

func (w *Ws) String() string {
	var wss string
	for _, v := range w.Cw {
		wss += v.W
	}
	return wss
}

type XfResult struct {
	Ed     int  `json:"ed"`
	Ws     []Ws `json:"ws"`
	Sn     int  `json:"sn"`
	Ls     bool `json:"ls"`
	Bg     int  `json:"bg"`
	Status int  `json:"status"`
}
type Data struct {
	Result XfResult `json:"result"`
}

type XfResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Sid     string `json:"sid"`
	Data    Data   `json:"data"`
}

type TTSRequest struct {
	Common struct {
		AppID string `json:"app_id"`
	} `json:"common"`
	Business struct {
		Aue    string `json:"aue"`    // 音频编码格式，可选值：raw, lame
		Sfl    int    `json:"sfl"`    // 流式返回，0或1
		Auf    string `json:"auf"`    // 音频采样率
		Vcn    string `json:"vcn"`    // 发音人
		Speed  int    `json:"speed"`  // 语速 [0,100]
		Volume int    `json:"volume"` // 音量 [0,100]
		Pitch  int    `json:"pitch"`  // 音高 [0,100]
		Bgs    int    `json:"bgs"`    // 背景音 [0,1]
	} `json:"business"`
	Data struct {
		Text     string `json:"text"`     // 待合成文本
		Encoding string `json:"encoding"` // 文本编码
		Status   int    `json:"status"`   // 文本状态
	} `json:"data"`
}

type TTSResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Sid     string `json:"sid"`
	Data    struct {
		Audio  string `json:"audio"`
		Status int    `json:"status"`
		Ced    string `json:"ced"`
	} `json:"data"`
}

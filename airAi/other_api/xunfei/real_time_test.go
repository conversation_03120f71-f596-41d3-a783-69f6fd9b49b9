package xunfei

import (
	"context"
	"fmt"
	"testing"
)

func TestNewClient(t *testing.T) {

	for i := 0; i < 2; i++ {
		//client := NewClient(Config{
		//	AppID:     "bedd9bed",
		//	APIKey:    "d5f6aff8660cf2f52054bf7df4681dbc",
		//	APISecret: "YmQ0ZmMyM2NjYWM5NWY3NDk3ZjA4Mzcz",
		//	FrameSize: 0,
		//})
		client := NewClient(Config{
			AppID:     "3c8f9bf3",
			APIKey:    "eebde6e169b02d131d64ed8a0942c9c9",
			APISecret: "YWI5MzI4NTcyN2NiZjA1MTVjMTg5YzQz",
			FrameSize: 0,
		})
		_, err := client.ConnectWebSocket()
		if err != nil {
			t.Error(err)
		}
		//go client.ReceiveResponse()
		results, err := client.RecognizeFile(context.Background(), "test.pcm", fmt.Sprintf("uid:%d", i))
		if err != nil {
			t.Error(err)
		}
		t.Log(results)
	}

	//client.receiveResults(client.sessions["123"])
}

package ali_sms

import (
	"fmt"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

const accessKeyId = "LTAI5tBVkhhHKyEzVeAaXxtS"
const accessKeySecret = "******************************"
const signName = "深圳腾捷实业有限公司"
const templateCode = "SMS_485155034"

// 发送短信
func SendSms(phoneNumber string, code int64) (bool, error) {
	templateParam := fmt.Sprintf(`{"code":"%v"}`, code)
	// 创建客户端
	client, err := dysmsapi.NewClientWithAccessKey(
		"cn-hangzhou",   // 地域ID
		accessKeyId,     // 你的AccessKey ID
		accessKeySecret) // 你的AccessKey Secret
	if err != nil {
		return false, fmt.Errorf("创建短信客户端失败: %v", err)
	}

	// 创建请求
	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"              // 使用HTTPS协议
	request.PhoneNumbers = phoneNumber    // 手机号
	request.SignName = signName           // 短信签名
	request.TemplateCode = templateCode   // 短信模板ID
	request.TemplateParam = templateParam // 模板参数，JSON格式

	// 发送请求
	response, err := client.SendSms(request)
	if err != nil {
		return false, fmt.Errorf("发送短信失败: %v", err)
	}

	// 检查响应
	if response.Code != "OK" {
		return false, fmt.Errorf("短信发送失败，错误码: %s, 错误信息: %s",
			response.Code, response.Message)
	}

	return true, nil
}

package ali_sms

import (
	"fmt"
	"testing"
)

func TestSendSms(t *testing.T) {
	// 示例：发送验证码
	phone := "18046725529" // 替换为实际手机号
	//signName := "深圳腾捷实业有限公司"        // 替换为在阿里云申请的签名
	//templateCode := "SMS_485155034" // 替换为你的模板CODE
	//templateCode := "SMS_317125975"      // 替换为你的模板CODE
	//templateParam := `{"code":"123456"}` // 模板参数，JSON格式

	success, err := SendSms(phone, 123456)
	if err != nil {
		fmt.Println("发送失败:", err)
		return
	}

	if success {
		fmt.Println("短信发送成功!")
	}
}

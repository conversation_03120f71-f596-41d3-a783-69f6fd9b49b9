package gemini

import (
	"airAi/global"
	"airAi/other_api"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"strings"
)

const (
	apiURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
)

func GenerateContent(base64Image string) (animal other_api.Animal, err error) {
	// 将图片转换为 Base64
	//base64Image := base64.StdEncoding.EncodeToString(imageData)

	// 构造请求体
	requestBody := map[string]interface{}{
		"contents": []map[string]interface{}{
			{
				"parts": []map[string]interface{}{
					{
						"inlineData": map[string]string{
							"mimeType": "image/jpeg",
							"data":     base64Image,
						},
					},
					{
						"text": `Please identify this bird and provide the following information in JSON format: 
{
  "name": "Common Name",
  "scientific_name": "Scientific Name",
  "habitat": "Habitat",
  "feeding_habits": "Feeding Habits",
  "description": "Brief Description"
}
Make sure to use **"name"** as the key for the common name. Do NOT use "bird_name" or any other_api key..`,
					},
				},
			},
		},
	}

	// 发送请求
	client := resty.New()
	resp, err := client.R().
		SetQueryParam("key", global.GVA_CONFIG.Gemini.Key).
		SetHeader("Content-Type", "application/json").
		SetBody(requestBody).
		Post(apiURL)

	if err != nil {
		return
	}

	// 提取 text 字段
	var data map[string]interface{}
	err = json.Unmarshal(resp.Body(), &data)
	if err != nil {
		fmt.Println("Error parsing JSON:", err)
		return
	}
	fmt.Println(data)
	// 安全提取 text 字段
	text, err := extractText(data)
	if err != nil {
		fmt.Println("Error extracting text:", err)
		return
	}

	fmt.Println("resp = ", string(resp.Body()))
	jsonData := strings.TrimSpace(text) // 去掉首尾空白字符
	jsonData = strings.TrimPrefix(jsonData, "```json")
	jsonData = strings.TrimSuffix(jsonData, "```")
	jsonData = strings.TrimSpace(jsonData) // 去掉去掉标记后的首尾空白字符

	// 声明一个 map 变量
	var result other_api.Animal

	// 将 JSON 数据解析到 map 中
	err = json.Unmarshal([]byte(jsonData), &result)
	if err != nil {
		return
	}
	return result, nil

}

// 安全提取 text 字段的函数
func extractText(data map[string]interface{}) (string, error) {
	// 检查 candidates 字段是否存在且是切片类型
	candidates, ok := data["candidates"]
	if !ok {
		return "", fmt.Errorf("field 'candidates' not found")
	}

	candidateList, ok := candidates.([]interface{})
	if !ok || len(candidateList) == 0 {
		return "", fmt.Errorf("field 'candidates' is not a valid array or is empty")
	}

	// 检查第一个 candidate 是否存在且是 map 类型
	firstCandidate, ok := candidateList[0].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("first candidate is not a valid map")
	}

	// 检查 content 字段是否存在且是 map 类型
	content, ok := firstCandidate["content"]
	if !ok {
		return "", fmt.Errorf("field 'content' not found in candidate")
	}
	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("field 'content' is not a valid map")
	}

	// 检查 parts 字段是否存在且是切片类型
	parts, ok := contentMap["parts"]
	if !ok {
		return "", fmt.Errorf("field 'parts' not found in content")
	}

	partsList, ok := parts.([]interface{})
	if !ok || len(partsList) == 0 {
		return "", fmt.Errorf("field 'parts' is not a valid array or is empty")
	}

	// 检查第一个 part 是否存在且是 map 类型
	firstPart, ok := partsList[0].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("first part is not a valid map")
	}

	// 检查 text 字段是否存在且是字符串类型
	text, ok := firstPart["text"]
	if !ok {
		return "", fmt.Errorf("field 'text' not found in part")
	}
	textStr, ok := text.(string)
	if !ok {
		return "", fmt.Errorf("field 'text' is not a valid string")
	}

	return textStr, nil
}

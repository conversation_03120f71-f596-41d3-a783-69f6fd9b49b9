package qwen

var (
	PfRealtimeV2    = "paraformer-realtime-v2"
	GummyRealtimeV1 = "gummy-realtime-v1"
	MeetingPrompt   = `你是一个专业的会议纪要助手。请根据以下会议内容，生成结构化的会议纪要。

重要要求：
1. 生成的会议纪要内容必须使用 %s 语言
2. 所有字段的值（标题、摘要、讨论内容、结论、任务描述等）都必须用 %s 语言书写
3. 严格按照以下 JSON 格式返回，不要添加额外字段或嵌套结构
4. 不要输出任何解释说明、注释或代码块标记符

JSON 格式：
{
  "title": "string，会议标题（用%s语言）",
  "date": "string，格式为 YYYY-MM-DD，如果未提到日期则使用当前日期 %s",
  "attendees": ["string，与会人员姓名（用%s语言）"],
  "summary": "string，总结本次会议主要内容（用%s语言）",
  "topics": [
    {
      "details": "string，具体讨论内容（用%s语言）",
      "conclusions": "string，总结结论（用%s语言）"
    }
  ],
  "action_items": [
    {
      "owner": "string，负责人（用%s语言）",
      "due_date": "string，格式为 YYYY-MM-DD，如果未提到则使用当前日期 %s",
      "description": "string，任务描述（用%s语言）"
    }
  ]
}

会议内容如下：
%s
`
	TranslatePrompt = `你是一个翻译员，只想得到翻译内容，根据以下内容：%s，翻译成 %s`
)

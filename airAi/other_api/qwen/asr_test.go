package qwen

import (
	"testing"
)

func TestAsr(t *testing.T) {
	//global.GVA_LOG = core.Zap()
	//asrCli := NewAsrClient("sk-597c7556cb88475f8df22ca6b2b89076", PfRealtimeV2)
	config := AliYunConfig{
		ApiKey: "sk-597c7556cb88475f8df22ca6b2b89076",
		Model:  PfRealtimeV2,
		//Format:          "wav",
		SourceLanguage:  "zh",
		TargetLanguages: "en",
	}
	asrCli, err := NewAsrClient(config)
	if err != nil {
		t.Error(err)
	}
	tr, err := asrCli.Transcription("./hello_world.wav")
	//tr, err := asrCli.Transcription("./test.pcm")
	//tr, err := asrCli.Transcription("./2.wav")
	//tr, err := asrCli.Transcription("./1.m4a")
	if err != nil {
		t.Error(err)
	}
	t.Log(tr)
}

package qwen

//func TestNewWebSocketCli(t *testing.T) {
//	cli, err := NewWebSocketCli("sk-b9ea5cc9be9d4585b46cf35a91e985b9")
//	if err != nil {
//		t.<PERSON>al(err)
//	}
//	_, _ = cli.StartResultReceiver("test.mp3")
//	str, err := cli.RunTask(LongWan)
//	if err != nil {
//		t.Fatal(err)
//	}
//	fmt.Println(str)
//	err = cli.ContinueTask(str, "床前明月光")
//	if err != nil {
//		t.Fatal(err)
//	}
//
//	select {}
//}

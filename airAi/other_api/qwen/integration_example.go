package qwen

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// StreamingVoiceHandler 流式语音翻译处理器
type StreamingVoiceHandler struct {
	serviceManager *ServiceManager
	upgrader       websocket.Upgrader
	activeClients  map[string]*ClientSession
	clientsMux     sync.RWMutex
}

// ClientSession 客户端会话
type ClientSession struct {
	ID           string
	Conn         *websocket.Conn
	Service      StreamingVoiceService
	AudioManager *ConcurrentAudioManager
	Context      context.Context
	Cancel       context.CancelFunc
	LastActivity time.Time
}

// NewStreamingVoiceHandler 创建流式语音翻译处理器
func NewStreamingVoiceHandler() *StreamingVoiceHandler {
	return &StreamingVoiceHandler{
		serviceManager: NewServiceManager(),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 在生产环境中应该进行适当的来源检查
			},
		},
		activeClients: make(map[string]*ClientSession),
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *StreamingVoiceHandler) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	// 生成客户端ID
	clientID := fmt.Sprintf("client_%d", time.Now().UnixNano())

	// 创建客户端会话
	ctx, cancel := context.WithCancel(context.Background())
	session := &ClientSession{
		ID:           clientID,
		Conn:         conn,
		Context:      ctx,
		Cancel:       cancel,
		LastActivity: time.Now(),
	}

	// 注册客户端
	h.registerClient(clientID, session)
	defer h.unregisterClient(clientID)

	// 处理客户端消息
	h.handleClientMessages(session)
}

// registerClient 注册客户端
func (h *StreamingVoiceHandler) registerClient(clientID string, session *ClientSession) {
	h.clientsMux.Lock()
	defer h.clientsMux.Unlock()
	h.activeClients[clientID] = session
	log.Printf("Client %s connected", clientID)
}

// unregisterClient 注销客户端
func (h *StreamingVoiceHandler) unregisterClient(clientID string) {
	h.clientsMux.Lock()
	defer h.clientsMux.Unlock()

	if session, exists := h.activeClients[clientID]; exists {
		// 停止服务
		if session.Service != nil {
			session.Service.Stop()
		}

		// 停止音频管理器
		if session.AudioManager != nil {
			session.AudioManager.StopAllStreamers()
		}

		// 取消上下文
		session.Cancel()

		delete(h.activeClients, clientID)
		log.Printf("Client %s disconnected", clientID)
	}
}

// handleClientMessages 处理客户端消息
func (h *StreamingVoiceHandler) handleClientMessages(session *ClientSession) {
	for {
		select {
		case <-session.Context.Done():
			return
		default:
		}

		// 设置读取超时
		session.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))

		messageType, message, err := session.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		session.LastActivity = time.Now()

		switch messageType {
		case websocket.TextMessage:
			h.handleTextMessage(session, message)
		case websocket.BinaryMessage:
			h.handleAudioMessage(session, message)
		}
	}
}

// handleTextMessage 处理文本消息
func (h *StreamingVoiceHandler) handleTextMessage(session *ClientSession, message []byte) {
	var request map[string]interface{}
	if err := json.Unmarshal(message, &request); err != nil {
		h.sendError(session, "Invalid JSON message")
		return
	}

	action, ok := request["action"].(string)
	if !ok {
		h.sendError(session, "Missing action field")
		return
	}

	switch action {
	case "start_service":
		h.handleStartService(session, request)
	case "stop_service":
		h.handleStopService(session)
	case "get_status":
		h.handleGetStatus(session)
	default:
		h.sendError(session, fmt.Sprintf("Unknown action: %s", action))
	}
}

// handleStartService 处理启动服务请求
func (h *StreamingVoiceHandler) handleStartService(session *ClientSession, request map[string]interface{}) {
	// 解析配置参数
	apiKey, _ := request["api_key"].(string)
	sourceLanguage, _ := request["source_language"].(string)
	targetLanguages, _ := request["target_languages"].([]interface{})

	if apiKey == "" || sourceLanguage == "" {
		h.sendError(session, "Missing required parameters: api_key, source_language")
		return
	}

	// 转换目标语言
	var targetLangs []string
	for _, lang := range targetLanguages {
		if langStr, ok := lang.(string); ok {
			targetLangs = append(targetLangs, langStr)
		}
	}

	// 创建服务配置
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLangs...).
		Build()
	if err != nil {
		h.sendError(session, fmt.Sprintf("Invalid configuration: %v", err))
		return
	}

	// 创建服务
	service, err := h.serviceManager.CreateAndRegisterService(session.ID, config)
	if err != nil {
		h.sendError(session, fmt.Sprintf("Failed to create service: %v", err))
		return
	}

	session.Service = service

	// 启动服务
	if err := service.Start(session.Context); err != nil {
		h.sendError(session, fmt.Sprintf("Failed to start service: %v", err))
		return
	}

	// 创建音频管理器
	serviceImpl := service.(*StreamingVoiceServiceImpl)
	session.AudioManager = NewConcurrentAudioManager(serviceImpl, 5)

	// 启动结果监听
	go h.listenForResults(session)

	// 发送成功响应
	h.sendResponse(session, map[string]interface{}{
		"action": "service_started",
		"status": "success",
	})
}

// handleStopService 处理停止服务请求
func (h *StreamingVoiceHandler) handleStopService(session *ClientSession) {
	if session.Service != nil {
		session.Service.Stop()
		session.Service = nil
	}

	if session.AudioManager != nil {
		session.AudioManager.StopAllStreamers()
		session.AudioManager = nil
	}

	h.serviceManager.RemoveService(session.ID)

	h.sendResponse(session, map[string]interface{}{
		"action": "service_stopped",
		"status": "success",
	})
}

// handleGetStatus 处理获取状态请求
func (h *StreamingVoiceHandler) handleGetStatus(session *ClientSession) {
	status := map[string]interface{}{
		"action":        "status",
		"connected":     session.Service != nil && session.Service.IsConnected(),
		"client_id":     session.ID,
		"last_activity": session.LastActivity,
	}

	if session.Service != nil {
		status["connection_info"] = session.Service.GetConnectionInfo()
	}

	if session.AudioManager != nil {
		status["audio_stats"] = session.AudioManager.GetAllStats()
	}

	h.sendResponse(session, status)
}

// handleAudioMessage 处理音频消息
func (h *StreamingVoiceHandler) handleAudioMessage(session *ClientSession, audioData []byte) {
	if session.Service == nil {
		h.sendError(session, "Service not started")
		return
	}

	// 发送音频数据到服务
	if err := session.Service.SendAudio(audioData); err != nil {
		h.sendError(session, fmt.Sprintf("Failed to send audio: %v", err))
	}
}

// listenForResults 监听服务结果
func (h *StreamingVoiceHandler) listenForResults(session *ClientSession) {
	// 监听ASR结果
	go func() {
		for asrResult := range session.Service.GetASRResults() {
			h.sendResponse(session, map[string]interface{}{
				"type":         "asr_result",
				"text":         asrResult.Text,
				"is_partial":   asrResult.IsPartial,
				"is_final":     asrResult.IsFinal,
				"begin_time":   asrResult.BeginTime,
				"end_time":     asrResult.EndTime,
				"timestamp":    asrResult.Timestamp,
				"sequence_num": asrResult.SequenceNum,
			})
		}
	}()

	// 监听翻译结果
	go func() {
		for translationResult := range session.Service.GetTranslationResults() {
			h.sendResponse(session, map[string]interface{}{
				"type":            "translation_result",
				"original_text":   translationResult.OriginalText,
				"translated_text": translationResult.TranslatedText,
				"source_language": translationResult.SourceLanguage,
				"target_language": translationResult.TargetLanguage,
				"is_partial":      translationResult.IsPartial,
				"is_final":        translationResult.IsFinal,
				"timestamp":       translationResult.Timestamp,
				"sequence_num":    translationResult.SequenceNum,
			})
		}
	}()

	// 监听错误
	go func() {
		for err := range session.Service.GetErrors() {
			h.sendError(session, fmt.Sprintf("Service error: %v", err))
		}
	}()
}

// sendResponse 发送响应
func (h *StreamingVoiceHandler) sendResponse(session *ClientSession, data interface{}) {
	message, err := json.Marshal(data)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		return
	}

	session.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := session.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
		log.Printf("Failed to send message: %v", err)
	}
}

// sendError 发送错误消息
func (h *StreamingVoiceHandler) sendError(session *ClientSession, errorMsg string) {
	h.sendResponse(session, map[string]interface{}{
		"type":    "error",
		"message": errorMsg,
	})
}

// SetupRoutes 设置路由
func SetupStreamingRoutes(router *gin.Engine) {
	handler := NewStreamingVoiceHandler()

	// WebSocket端点
	router.GET("/ws/streaming-voice", handler.HandleWebSocket)

	// HTTP API端点
	api := router.Group("/api/streaming-voice")
	{
		api.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":    "healthy",
				"timestamp": time.Now(),
			})
		})

		api.GET("/presets", func(c *gin.Context) {
			presets := map[string]interface{}{
				"default":      "Default configuration",
				"high_quality": "High quality configuration",
				"low_latency":  "Low latency configuration",
				"mobile":       "Mobile device configuration",
				"server":       "Server configuration",
			}
			c.JSON(http.StatusOK, presets)
		})
	}
}

// ExampleIntegration 集成示例
func ExampleIntegration() {
	// 创建Gin路由器
	router := gin.Default()

	// 设置流式语音翻译路由
	SetupStreamingRoutes(router)

	// 启动服务器
	log.Println("Starting streaming voice translation server on :8080")
	if err := router.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

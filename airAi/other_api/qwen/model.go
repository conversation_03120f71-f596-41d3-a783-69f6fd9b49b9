package qwen

import "github.com/openai/openai-go"

// 配置结构体
type Config struct {
	APIKey  string
	BaseURL string
	Model   string
}
type QwenClient struct {
	Client openai.Client
}

// StreamingChatResult 流式聊天结果结构体
// 用于表示AI文本聊天的流式响应结果
type StreamingChatResult struct {
	Content   string `json:"content"`         // AI生成的文本内容片段
	IsPartial bool   `json:"is_partial"`      // 是否为部分结果：true=部分内容，false=完整结果
	IsEnd     bool   `json:"is_end"`          // 是否为结束标记：true=响应结束，false=继续响应
	Error     string `json:"error,omitempty"` // 错误信息（如果有）
}

//type Payload struct {
//	TaskGroup  string     `json:"task_group"` // require 固定字符串："audio"。
//	Task       string     `json:"task"`       // require 固定字符串："tts"。
//	Function   string     `json:"function"`   // require 固定字符串："SpeechSynthesizer"。
//	Model      string     `json:"model"`      // require 模型名称："cosyvoice-v1"。 //建议参考音色列表 当前支持的模型名：cosyvoice-v1，cosyvoice-v2。
//	Input      Input      `json:"input"`      // require 需要合成的文本片段。
//	Parameters Params     `json:"parameters"`
//	Resources  []Resource `json:"resources"`
//}

//type Params struct {
//	TextType string `json:"text_type"` // require  固定字符串：“PlainText”。
//	//Voice      string `json:"voice"`       // require  发音人。
//	Voice      VoiceUser `json:"voice"`       // require  发音人。
//	Format     string    `json:"format"`      // require  音频编码格式，支持"pcm"、"wav"和"mp3"。
//	SampleRate int       `json:"sample_rate"` // require  音频采样率，支持下述采样率: 8000, 16000, 22050, 24000, 44100, 48000。
//	Volume     int       `json:"volume"`      // optional 音量，取值范围：0～100。默认值：50。
//	Rate       int       `json:"rate"`        // optional 合成音频的语速，取值范围：0.5~2。默认值：1.0。
//	Pitch      int       `json:"pitch"`       // optional 合成音频的语调，取值范围：0.5~2。 默认值：1.0。
//}

//type Resource struct {
//	ResourceID   string `json:"resource_id"`
//	ResourceType string `json:"resource_type"`
//}
//
//type Input struct {
//	Text string `json:"text"`
//}

//type Event struct {
//	Header  Header  `json:"header"`
//	Payload Payload `json:"payload"`
//}

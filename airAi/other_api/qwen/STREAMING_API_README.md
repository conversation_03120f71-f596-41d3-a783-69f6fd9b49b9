# Streaming Voice Translation REST API

A comprehensive REST API for real-time audio file processing with streaming ASR (Automatic Speech Recognition) and translation results. This API accepts audio file uploads and returns streaming transcription and translation results in real-time using Server-Sent Events (SSE).

## Features

- **File Upload Processing**: Accept multipart audio file uploads with immediate processing
- **Real-time Streaming**: Stream ASR and translation results as they become available using SSE
- **Multiple Audio Formats**: Support for WAV, MP3, PCM, AAC, M4A, FLAC, and OGG formats
- **Concurrent Processing**: Handle multiple file uploads simultaneously
- **Session Management**: Track and manage processing sessions with detailed status information
- **Configurable Parameters**: Flexible configuration for different use cases and quality requirements

## API Endpoints

### Audio Processing

#### POST `/api/streaming-voice/upload`

Upload an audio file for real-time processing with streaming results.

**Request Parameters:**
- `audio_file` (file, required): Audio file to process
- `api_key` (string, required): DashScope API key
- `source_language` (string, required): Source language code (e.g., "zh", "en")
- `target_languages` (array, required): Target language codes for translation
- `model` (string, optional): ASR model to use (default: "paraformer-realtime-v1")
- `sample_rate` (int, optional): Audio sample rate (default: 16000)
- `chunk_size` (int, optional): Processing chunk size (default: 1024)
- `enable_vad` (bool, optional): Enable Voice Activity Detection (default: false)

**Response:** Server-Sent Events stream with real-time results

**Example Request:**
```bash
curl -X POST "http://localhost:8080/api/streaming-voice/upload" \
  -H "Accept: text/event-stream" \
  -F "audio_file=@audio.wav" \
  -F "api_key=your-api-key" \
  -F "source_language=zh" \
  -F "target_languages=en" \
  -F "target_languages=ja"
```

**Example SSE Response:**
```
event: data
data: {"type":"status","session_id":"uuid","timestamp":"2024-01-01T00:00:00Z","data":{"status":"started","message":"Audio processing started"}}

event: asr
data: {"type":"asr_result","session_id":"uuid","timestamp":"2024-01-01T00:00:00Z","sequence_num":1,"data":{"original_text":"你好","is_partial":true,"is_final":false}}

event: translation
data: {"type":"translation_result","session_id":"uuid","timestamp":"2024-01-01T00:00:00Z","sequence_num":2,"data":{"original_text":"你好","translated_text":"Hello","source_language":"zh","target_language":"en","is_partial":false,"is_final":true}}

event: end
data: 
```

### Session Management

#### GET `/api/streaming-voice/sessions`

List all active processing sessions.

**Response:**
```json
{
  "active_sessions": [
    {
      "session_id": "uuid",
      "status": "processing",
      "file_name": "audio.wav",
      "file_size": 1024000,
      "processed_bytes": 512000,
      "progress": 50.0,
      "source_language": "zh",
      "target_languages": ["en", "ja"],
      "result_count": 15,
      "start_time": "2024-01-01T00:00:00Z",
      "last_activity": "2024-01-01T00:01:00Z",
      "duration": "1m0s"
    }
  ],
  "total_count": 1
}
```

#### GET `/api/streaming-voice/sessions/:session_id`

Get detailed status of a specific session.

**Response:**
```json
{
  "session_id": "uuid",
  "status": {
    "status": "processing",
    "processed_bytes": 512000,
    "total_bytes": 1024000,
    "progress": 50.0,
    "result_count": 15,
    "duration": "1m0s",
    "last_activity": "2024-01-01T00:01:00Z"
  },
  "session": {
    "id": "uuid",
    "file_name": "audio.wav",
    "source_language": "zh",
    "target_languages": ["en", "ja"],
    "start_time": "2024-01-01T00:00:00Z"
  }
}
```

#### DELETE `/api/streaming-voice/sessions/:session_id`

Stop a specific processing session.

**Response:**
```json
{
  "message": "Session stopped successfully",
  "session_id": "uuid"
}
```

### Information Endpoints

#### GET `/api/streaming-voice/health`

Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "service": "streaming-voice-api",
  "version": "1.0.0"
}
```

#### GET `/api/streaming-voice/formats`

Get supported audio formats and languages.

**Response:**
```json
{
  "supported_formats": [".wav", ".mp3", ".pcm", ".aac", ".m4a", ".flac", ".ogg"],
  "supported_languages": {
    "zh": "Chinese",
    "en": "English",
    "ja": "Japanese",
    "ko": "Korean",
    "es": "Spanish",
    "fr": "French",
    "de": "German"
  },
  "default_config": {
    "sample_rate": 16000,
    "chunk_size": 1024,
    "model": "paraformer-realtime-v1"
  }
}
```

#### GET `/api/streaming-voice/presets`

Get configuration presets for different use cases.

**Response:**
```json
{
  "default": {
    "description": "Default configuration for general use",
    "sample_rate": 16000,
    "chunk_size": 1024,
    "enable_vad": false
  },
  "high_quality": {
    "description": "High quality configuration",
    "sample_rate": 44100,
    "chunk_size": 2048,
    "enable_vad": false
  },
  "low_latency": {
    "description": "Low latency configuration",
    "sample_rate": 16000,
    "chunk_size": 512,
    "enable_vad": true
  },
  "mobile": {
    "description": "Mobile device optimized configuration",
    "sample_rate": 16000,
    "chunk_size": 512,
    "enable_vad": true
  }
}
```

## Response Format

### SSE Event Types

1. **status**: Processing status updates
2. **asr**: ASR transcription results
3. **translation**: Translation results
4. **progress**: Processing progress updates
5. **error**: Error messages
6. **end**: Processing completion signal

### Response Structure

All SSE events follow this structure:
```json
{
  "type": "event_type",
  "session_id": "uuid",
  "timestamp": "2024-01-01T00:00:00Z",
  "sequence_num": 1,
  "data": {
    // Event-specific data
  }
}
```

### ASR Result Data
```json
{
  "original_text": "recognized text",
  "is_partial": true,
  "is_final": false,
  "confidence": 0.95,
  "begin_time": 1000,
  "end_time": 2000,
  "sentence_id": 1
}
```

### Translation Result Data
```json
{
  "original_text": "source text",
  "translated_text": "translated text",
  "source_language": "zh",
  "target_language": "en",
  "is_partial": false,
  "is_final": true,
  "confidence": 0.98,
  "begin_time": 1000,
  "end_time": 2000,
  "sentence_id": 1
}
```

## Client Implementation Examples

### JavaScript/Browser
```javascript
const formData = new FormData();
formData.append('audio_file', audioFile);
formData.append('api_key', 'your-api-key');
formData.append('source_language', 'zh');
formData.append('target_languages', 'en');

fetch('/api/streaming-voice/upload', {
  method: 'POST',
  body: formData,
  headers: {
    'Accept': 'text/event-stream'
  }
}).then(response => {
  const reader = response.body.getReader();
  
  function readStream() {
    reader.read().then(({ done, value }) => {
      if (done) return;
      
      const text = new TextDecoder().decode(value);
      const lines = text.split('\n');
      
      lines.forEach(line => {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.substring(6));
          handleStreamingResult(data);
        }
      });
      
      readStream();
    });
  }
  
  readStream();
});

function handleStreamingResult(data) {
  switch(data.type) {
    case 'asr_result':
      console.log('ASR:', data.data.original_text);
      break;
    case 'translation_result':
      console.log('Translation:', data.data.translated_text);
      break;
    case 'status':
      console.log('Status:', data.data.status);
      break;
  }
}
```

### Python
```python
import requests
import json

def upload_and_stream(audio_file_path, api_key, source_lang, target_langs):
    with open(audio_file_path, 'rb') as f:
        files = {'audio_file': f}
        data = {
            'api_key': api_key,
            'source_language': source_lang,
            'target_languages': target_langs
        }
        
        response = requests.post(
            'http://localhost:8080/api/streaming-voice/upload',
            files=files,
            data=data,
            headers={'Accept': 'text/event-stream'},
            stream=True
        )
        
        for line in response.iter_lines():
            if line.startswith(b'data: '):
                try:
                    data = json.loads(line[6:])
                    handle_result(data)
                except json.JSONDecodeError:
                    continue

def handle_result(data):
    if data['type'] == 'asr_result':
        print(f"ASR: {data['data']['original_text']}")
    elif data['type'] == 'translation_result':
        print(f"Translation: {data['data']['translated_text']}")
```

## Error Handling

The API uses standard HTTP status codes and provides detailed error information:

- `400 Bad Request`: Invalid parameters or unsupported format
- `413 Payload Too Large`: File size exceeds limit (100MB)
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server processing error

Error responses follow this format:
```json
{
  "error": "Error description",
  "code": "ERROR_CODE",
  "details": "Additional details",
  "trace_id": "uuid"
}
```

## Rate Limiting

- Maximum 100 requests per minute per IP address
- Maximum file size: 100MB
- Maximum concurrent sessions: Configurable (default: unlimited)

## Integration

### With Existing Gin Router
```go
import "your-project/airAi/other_api/qwen"

func main() {
    router := gin.Default()
    
    // Add your existing routes
    router.GET("/", homeHandler)
    
    // Integrate streaming voice API
    qwen.SetupStreamingAudioRoutes(router)
    
    router.Run(":8080")
}
```

### Standalone Server
```go
import "your-project/airAi/other_api/qwen"

func main() {
    qwen.StartStreamingAPIServer("8080")
}
```

## Performance Considerations

- **Chunk Size**: Smaller chunks reduce latency but increase overhead
- **Sample Rate**: Higher rates improve quality but increase processing time
- **Concurrent Sessions**: Monitor server resources with multiple uploads
- **Network**: SSE requires persistent connections; consider connection limits

## Security

- Always validate API keys before processing
- Implement proper authentication and authorization
- Consider file type validation beyond extension checking
- Monitor for abuse and implement appropriate rate limiting
- Use HTTPS in production environments

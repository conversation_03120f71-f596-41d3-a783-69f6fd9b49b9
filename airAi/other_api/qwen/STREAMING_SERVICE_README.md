# Streaming Voice Translation Service

A production-ready streaming voice translation service built on top of Alibaba Cloud DashScope WebSocket API. This service provides real-time ASR (Automatic Speech Recognition) and translation capabilities with concurrent audio processing and structured result streaming.

## Features

- **Real-time Streaming**: Immediate processing and streaming of partial ASR and translation results
- **Concurrent Audio Processing**: Support for multiple audio streams with configurable processing
- **Connection Management**: Automatic reconnection, health monitoring, and graceful shutdown
- **Configurable Parameters**: Flexible configuration for different use cases (mobile, server, low-latency, etc.)
- **Clean API Design**: Well-structured interfaces with proper separation of concerns
- **Production Ready**: Comprehensive error handling, logging, and monitoring capabilities

## Quick Start

### Basic Usage

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "your-project/airAi/other_api/qwen"
)

func main() {
    // Create configuration
    config, err := qwen.NewConfigBuilder().
        WithApiKey("your-dashscope-api-key").
        WithSourceLanguage("zh").
        WithTargetLanguages("en", "ja").
        Build()
    if err != nil {
        log.Fatal("Failed to build config:", err)
    }

    // Create service
    service := qwen.NewStreamingVoiceService(config)

    // Start service
    ctx := context.Background()
    if err := service.Start(ctx); err != nil {
        log.Fatal("Failed to start service:", err)
    }
    defer service.Stop()

    // Listen for results
    go func() {
        for asrResult := range service.GetASRResults() {
            fmt.Printf("ASR: %s (partial: %t)\n", asrResult.Text, asrResult.IsPartial)
        }
    }()

    go func() {
        for translationResult := range service.GetTranslationResults() {
            fmt.Printf("Translation [%s]: %s (partial: %t)\n", 
                translationResult.TargetLanguage, 
                translationResult.TranslatedText, 
                translationResult.IsPartial)
        }
    }()

    // Send audio data
    audioData := []byte("your audio data here")
    if err := service.SendAudio(audioData); err != nil {
        log.Printf("Failed to send audio: %v", err)
    }

    // Wait for processing
    time.Sleep(5 * time.Second)
}
```

### Using Factory Methods

```go
// Create service with defaults
factory := qwen.NewServiceFactory()
service, err := factory.CreateServiceWithDefaults("your-api-key", "zh", "en")

// Create ASR-only service
asrService, err := factory.CreateASROnlyService("your-api-key", "zh")

// Create high-performance service
hpService, err := factory.CreateHighPerformanceService("your-api-key", "zh", "en", "ja")

// Create low-latency service
llService, err := factory.CreateLowLatencyService("your-api-key", "zh", "en")
```

### Using Configuration Presets

```go
presets := qwen.NewConfigPresets()

// Mobile device configuration
mobileConfig := presets.MobileConfig("your-api-key", "zh", "en")

// Server configuration
serverConfig := presets.ServerConfig("your-api-key", "zh", "en", "ja", "ko")

// Low latency configuration
lowLatencyConfig := presets.LowLatencyConfig("your-api-key", "zh", "en")

// Create service with preset
factory := qwen.NewServiceFactory()
service, err := factory.CreateService(mobileConfig)
```

## Configuration Options

### StreamingServiceConfig

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `ApiKey` | string | DashScope API key | Required |
| `Model` | string | ASR model to use | "paraformer-realtime-v1" |
| `SourceLanguage` | string | Source language code | Required |
| `TargetLanguages` | []string | Target language codes | Required for translation |
| `SampleRate` | int | Audio sample rate | 16000 |
| `AudioFormat` | string | Audio format | "pcm" |
| `MaxReconnectAttempts` | int | Maximum reconnection attempts | 3 |
| `ReconnectInterval` | time.Duration | Interval between reconnections | 5s |
| `ConnectionTimeout` | time.Duration | Connection timeout | 30s |
| `AudioBufferSize` | int | Audio buffer size | 1024 |
| `ResultChannelSize` | int | Result channel buffer size | 100 |
| `EnableTranscription` | bool | Enable ASR transcription | true |
| `EnableTranslation` | bool | Enable translation | true |
| `EnableVAD` | bool | Enable Voice Activity Detection | false |

### Supported Languages

The service supports the following language codes:

- `zh` - Chinese
- `en` - English  
- `ja` - Japanese
- `ko` - Korean
- `es` - Spanish
- `fr` - French
- `de` - German
- `it` - Italian
- `pt` - Portuguese
- `ru` - Russian
- `ar` - Arabic
- And many more...

## Advanced Usage

### Service Manager

```go
manager := qwen.NewServiceManager()

// Create and register multiple services
config1, _ := qwen.NewConfigBuilder().
    WithApiKey("your-api-key").
    WithSourceLanguage("zh").
    WithTargetLanguages("en").
    Build()

service1, err := manager.CreateAndRegisterService("zh-to-en", config1)

// Get service by name
service, exists := manager.GetService("zh-to-en")

// List all services
services := manager.ListServices()

// Stop all services
manager.StopAllServices()
```

### Concurrent Audio Processing

```go
// Create audio processor
processor := qwen.NewDefaultAudioProcessor(16000, 1, "pcm")

// Create audio streamer
streamer := qwen.NewAudioStreamer(service, processor, 1024)

// Start streaming
ctx := context.Background()
streamer.Start(ctx)

// Stream audio chunks
audioChunk := make([]byte, 1024)
err := streamer.StreamAudioChunk(audioChunk)

// Get statistics
stats := streamer.GetStats()
fmt.Printf("Total chunks: %d, Total bytes: %d\n", stats.TotalChunks, stats.TotalBytes)
```

### WebSocket Integration

```go
// Setup Gin router with streaming endpoints
router := gin.Default()
qwen.SetupStreamingRoutes(router)

// Start server
router.Run(":8080")
```

Client-side WebSocket usage:

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/streaming-voice');

// Start service
ws.send(JSON.stringify({
    action: 'start_service',
    api_key: 'your-api-key',
    source_language: 'zh',
    target_languages: ['en', 'ja']
}));

// Send audio data
ws.send(audioBuffer);

// Listen for results
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'asr_result') {
        console.log('ASR:', data.text);
    } else if (data.type === 'translation_result') {
        console.log('Translation:', data.translated_text);
    }
};
```

## Error Handling

```go
// Listen for errors
go func() {
    for err := range service.GetErrors() {
        log.Printf("Service error: %v", err)
        // Implement error recovery logic here
    }
}()

// Check connection status
if service.IsConnected() {
    fmt.Println("Service is connected")
} else {
    fmt.Println("Service is disconnected")
}

// Get connection information
info := service.GetConnectionInfo()
fmt.Printf("Connection state: %s\n", info.ConnectionState)
fmt.Printf("Reconnect count: %d\n", info.ReconnectCount)
```

## Testing

Run the test suite:

```bash
go test ./airAi/other_api/qwen -v
```

Run benchmarks:

```bash
go test ./airAi/other_api/qwen -bench=.
```

## Configuration Validation

```go
validator := qwen.NewConfigValidator()

// Validate configuration
err := validator.ValidateConfig(config)
if err != nil {
    log.Printf("Config validation failed: %v", err)
}

// Validate language code
err = validator.ValidateLanguage("zh")
if err != nil {
    log.Printf("Invalid language: %v", err)
}
```

## Best Practices

1. **Connection Management**: Always call `Stop()` to properly clean up resources
2. **Error Handling**: Monitor the error channel for connection issues and implement retry logic
3. **Buffer Sizing**: Adjust buffer sizes based on your latency and throughput requirements
4. **Language Validation**: Validate language codes before creating services
5. **Resource Cleanup**: Use context cancellation for graceful shutdown
6. **Monitoring**: Monitor connection statistics and audio streaming metrics

## Performance Considerations

- **Low Latency**: Use smaller buffer sizes and the low-latency preset
- **High Throughput**: Use larger buffer sizes and the high-performance preset  
- **Mobile**: Enable VAD and use appropriate buffer sizes for mobile networks
- **Server**: Use server preset with larger buffers for handling multiple clients

## License

This streaming service is part of the airAi project and follows the same licensing terms.

package qwen

import (
	"airAi/global"
	"airAi/other_api/storage"
	"airAi/other_api/types"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// safeLog 安全的日志记录函数，避免nil pointer异常
func safeLogDebug(msg string, fields ...zap.Field) {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Debug(msg, fields...)
	}
}

func safeLogInfo(msg string, fields ...zap.Field) {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Info(msg, fields...)
	}
}

func safeLogWarn(msg string, fields ...zap.Field) {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Warn(msg, fields...)
	}
}

type AsrClient struct {
	config AliYunConfig
}

type AliYunConfig struct {
	ApiKey          string
	Model           string
	Format          string //pcm、wav、opus、speex、aac、amr
	SourceLanguage  string
	TargetLanguages string
}

func NewAsrClient(config AliYunConfig) (*AsrClient, error) {
	if config.ApiKey == "" {
		return nil, errors.New("invalid api key")
	}
	return &AsrClient{
		config: config,
	}, nil
}

const (
	wsURL = "wss://dashscope.aliyuncs.com/api-ws/v1/inference/" // WebSocket服务器地址
)

var dialer = websocket.DefaultDialer

func (c *AsrClient) Transcription(audioFile string) (*types.TranscriptionData, error) {
	processedAudioFile := audioFile
	// 连接WebSocket服务
	conn, err := c.connectWebSocket(c.config.ApiKey)
	if err != nil {
		global.GVA_LOG.Error("连接WebSocket失败", zap.Error(err), zap.String("audio file", audioFile))
		return nil, err
	}
	defer c.closeConnection(conn)

	// 启动一个goroutine来接收结果
	taskStarted := make(chan bool)
	taskDone := make(chan bool)

	words := make([]types.Word, 0)
	texts := make(map[string]string)
	//text := ""
	c.startResultReceiver(conn, &words, texts, taskStarted, taskDone)

	// 发送run-task指令
	taskID, err := c.sendRunTaskCmd(conn)
	if err != nil {
		global.GVA_LOG.Error("发送run-task指令失败", zap.Error(err), zap.String("audio file", audioFile))
	}

	// 等待task-started事件
	c.waitForTaskStarted(taskStarted)

	// 发送待识别音频文件流
	if err := c.sendAudioData(conn, processedAudioFile); err != nil {
		global.GVA_LOG.Error("发送音频数据失败", zap.Error(err))
	}

	// 发送finish-task指令
	if err := c.sendFinishTaskCmd(conn, taskID); err != nil {
		global.GVA_LOG.Error("发送finish-task指令失败", zap.Error(err), zap.String("audio file", audioFile))
	}

	// 等待任务完成或失败
	<-taskDone

	if len(words) == 0 {
		//global.GVA_LOG.Info("识别结果为空", zap.String("audio file", audioFile))
		//fmt.Println("识别结果为空", zap.String("audio file", audioFile))
	}
	//global.GVA_LOG.Debug("识别结果", zap.Any("words", words), zap.Any("text", texts), zap.String("audio file", audioFile))

	transcriptionData := &types.TranscriptionData{
		Texts: texts,
		Words: words,
	}

	return transcriptionData, nil
}
func (c *AsrClient) TranscriptionAudio(audio io.Reader) (*types.TranscriptionData, error) {
	// 连接WebSocket服务
	conn, err := c.connectWebSocket(c.config.ApiKey)
	if err != nil {
		//global.GVA_LOG.Error("连接WebSocket失败", zap.Error(err))
		return nil, err
	}
	defer c.closeConnection(conn)

	// 启动一个goroutine来接收结果
	taskStarted := make(chan bool)
	taskDone := make(chan bool)

	words := make([]types.Word, 0)
	texts := make(map[string]string)
	//text := ""
	c.startResultReceiver(conn, &words, texts, taskStarted, taskDone)

	// 发送run-task指令
	taskID, err := c.sendRunTaskCmd(conn)
	if err != nil {
		//global.GVA_LOG.Error("发送run-task指令失败", zap.Error(err))
	}

	// 等待task-started事件
	c.waitForTaskStarted(taskStarted)

	// 发送待识别音频文件流
	if err := c.sendAudio(conn, audio); err != nil {
		//global.GVA_LOG.Error("发送音频数据失败", zap.Error(err))
	}

	// 发送finish-task指令
	if err := c.sendFinishTaskCmd(conn, taskID); err != nil {
		//global.GVA_LOG.Error("发送finish-task指令失败", zap.Error(err))
	}

	// 等待任务完成或失败
	<-taskDone

	if len(words) == 0 {
		//global.GVA_LOG.Info("识别结果为空")
	}
	//global.GVA_LOG.Debug("识别结果", zap.Any("words", words), zap.Any("text", texts))
	fmt.Println("识别结果", zap.Any("words", words), zap.Any("text", texts))
	transcriptionData := &types.TranscriptionData{
		Texts: texts,
		Words: words,
	}

	return transcriptionData, nil
}

// StreamingTranscriptionResult 流式转录结果 - 增强版，支持翻译
type StreamingTranscriptionResult struct {
	Text         string `json:"text"`
	Translation  string `json:"translation"`
	IsPartial    bool   `json:"is_partial"`
	IsEnd        bool   `json:"is_end"`
	RSentenceEnd bool   `json:"r_sentence_end"`
	TSentenceEnd bool   `json:"t_sentence_end"`
	IsTranslated bool   `json:"is_translated"`      // 标记是否为翻译结果
	Language     string `json:"language,omitempty"` // 语言标识
	Error        string `json:"error,omitempty"`
}

// StreamingTranscriptionAudio 流式语音转录
func (c *AsrClient) StreamingTranscriptionAudio(audio io.Reader, resultChan chan<- StreamingTranscriptionResult) error {

	// 连接WebSocket服务
	conn, err := c.connectWebSocket(c.config.ApiKey)
	if err != nil {
		return err
	}
	defer c.closeConnection(conn)

	// 启动一个goroutine来接收结果
	taskStarted := make(chan bool)
	taskDone := make(chan bool)

	// 启动流式结果接收器
	c.startStreamingResultReceiver(conn, resultChan, taskStarted, taskDone)

	// 发送run-task指令
	taskID, err := c.sendRunTaskCmd(conn)
	if err != nil {
		return err
	}

	// 等待task-started事件
	c.waitForTaskStarted(taskStarted)

	// 发送待识别音频文件流
	if err := c.sendAudio(conn, audio); err != nil {
		return err
	}

	// 发送finish-task指令
	if err := c.sendFinishTaskCmd(conn, taskID); err != nil {
		return err
	}

	// 等待任务完成或失败
	<-taskDone

	return nil
}
func (c *AsrClient) StreamingTranscriptionAudio1(audio io.Reader, resultChan chan<- Event) error {

	// 连接WebSocket服务
	conn, err := c.connectWebSocket(c.config.ApiKey)
	if err != nil {
		return err
	}
	defer c.closeConnection(conn)

	// 启动一个goroutine来接收结果
	taskStarted := make(chan bool)
	taskDone := make(chan bool)

	// 启动流式结果接收器
	c.startStreamingResultReceiver1(conn, resultChan, taskStarted, taskDone)

	// 发送run-task指令
	taskID, err := c.sendRunTaskCmd(conn)
	if err != nil {
		return err
	}

	// 等待task-started事件
	c.waitForTaskStarted(taskStarted)

	// 发送待识别音频文件流
	if err := c.sendAudio(conn, audio); err != nil {
		return err
	}

	// 发送finish-task指令
	if err := c.sendFinishTaskCmd(conn, taskID); err != nil {
		return err
	}

	// 等待任务完成或失败
	<-taskDone

	return nil
}

// StreamingTranscriptionWithTranslation 流式语音转录和翻译 - 结合Transcription方法的翻译能力
// 这个方法利用Transcription方法的基础设施，但提供流式结果
func (c *AsrClient) StreamingTranscriptionWithTranslation(audio io.Reader, resultChan chan<- StreamingTranscriptionResult) error {
	// 连接WebSocket服务 - 使用与Transcription相同的连接方式
	conn, err := c.connectWebSocket(c.config.ApiKey)
	if err != nil {
		return err
	}
	defer c.closeConnection(conn)

	// 启动一个goroutine来接收结果
	taskStarted := make(chan bool)
	taskDone := make(chan bool)

	// 启动增强的流式结果接收器 - 包含翻译功能
	c.startEnhancedStreamingResultReceiver(conn, resultChan, taskStarted, taskDone)

	// 发送run-task指令 - 确保启用翻译功能
	taskID, err := c.sendRunTaskCmd(conn)
	if err != nil {
		return err
	}

	// 等待task-started事件
	c.waitForTaskStarted(taskStarted)

	// 发送待识别音频文件流
	if err := c.sendAudio(conn, audio); err != nil {
		return err
	}

	// 发送finish-task指令
	if err := c.sendFinishTaskCmd(conn, taskID); err != nil {
		return err
	}

	// 等待任务完成或失败
	<-taskDone

	return nil
}

// startEnhancedStreamingResultReceiver 启动增强的流式结果接收器 - 包含翻译功能
// 这个方法结合了Transcription方法的翻译处理逻辑和流式处理的实时性
func (c *AsrClient) startEnhancedStreamingResultReceiver(conn *websocket.Conn, resultChan chan<- StreamingTranscriptionResult, taskStarted chan<- bool, taskDone chan<- bool) {
	go func() {
		defer close(taskDone)

		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 发送错误结果
				select {
				case resultChan <- StreamingTranscriptionResult{
					Text:      "",
					IsPartial: false,
					IsEnd:     true,
					Error:     err.Error(),
				}:
				default:
				}
				return
			}

			currentEvent := Event{}
			err = json.Unmarshal(message, &currentEvent)
			if err != nil {
				continue
			}

			// 增强的结果处理 - 包含完整的null safety检查
			c.processStreamingASRResults(&currentEvent, resultChan)

			// 处理事件状态
			if c.handleStreamingEvent(conn, &currentEvent, taskStarted, taskDone, resultChan) {
				return
			}
		}
	}()
}

// startStreamingResultReceiver 启动流式结果接收器
func (c *AsrClient) startStreamingResultReceiver(conn *websocket.Conn, resultChan chan<- StreamingTranscriptionResult, taskStarted chan<- bool, taskDone chan<- bool) {
	go func() {
		defer close(taskDone)

		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 发送错误结果
				select {
				case resultChan <- StreamingTranscriptionResult{
					Text:      "",
					IsPartial: false,
					IsEnd:     true,
					Error:     err.Error(),
				}:
				default:
				}
				return
			}

			currentEvent := Event{}
			err = json.Unmarshal(message, &currentEvent)
			if err != nil {
				continue
			}

			// 增强的结果处理 - 包含完整的null safety检查（仅ASR，不包含翻译）
			c.processBasicStreamingASRResults(&currentEvent, resultChan)

			// 处理事件状态
			if c.handleStreamingEvent(conn, &currentEvent, taskStarted, taskDone, resultChan) {
				return
			}
		}
	}()
}

func (c *AsrClient) startStreamingResultReceiver1(conn *websocket.Conn, resultChan chan<- Event, taskStarted chan<- bool, taskDone chan<- bool) {
	go func() {
		defer close(taskDone)

		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 发送错误结果
				//select {
				//case resultChan <- StreamingTranscriptionResult{
				//	Text:        "",
				//	Translation: "",
				//	IsPartial:   false,
				//	IsEnd:       true,
				//	Error:       err.Error(),
				//}:
				//default:
				//}
				return
			}

			currentEvent := Event{}
			err = json.Unmarshal(message, &currentEvent)
			if err != nil {
				continue
			}

			// 增强的结果处理 - 包含完整的null safety检查（仅ASR，不包含翻译）
			c.processBasicStreamingASRResults1(&currentEvent, resultChan)
			// 处理事件状态
			if c.handleStreamingEvent1(conn, &currentEvent, taskStarted, taskDone, resultChan) {
				return
			}
		}
	}()
}

// handleStreamingEvent 处理流式事件
func (c *AsrClient) handleStreamingEvent(conn *websocket.Conn, event *Event, taskStarted chan<- bool, taskDone chan<- bool, resultChan chan<- StreamingTranscriptionResult) bool {
	switch event.Header.Event {
	case "task-started":
		select {
		case taskStarted <- true:
		default:
		}
		return false
	case "task-finished":
		// 发送结束标记
		select {
		case resultChan <- StreamingTranscriptionResult{
			Text:      "",
			IsPartial: false,
			IsEnd:     true,
		}:
		default:
		}

		select {
		case taskDone <- true:
		default:
		}
		return true
	case "task-failed":
		// 发送错误结果
		errorMsg := "Task failed"
		if event.Header.ErrorMessage != "" {
			errorMsg = event.Header.ErrorMessage
		}

		select {
		case resultChan <- StreamingTranscriptionResult{
			Text:      "",
			IsPartial: false,
			IsEnd:     true,
			Error:     errorMsg,
		}:
		default:
		}

		select {
		case taskDone <- true:
		default:
		}
		return true
	}
	return false
}

func (c *AsrClient) handleStreamingEvent1(conn *websocket.Conn, event *Event, taskStarted chan<- bool, taskDone chan<- bool, resultChan chan<- Event) bool {
	switch event.Header.Event {
	case "task-started":
		select {
		case taskStarted <- true:
		default:
		}
		return false
	case "task-finished":
		endEvent := event
		endEvent.IsEnd = true
		resultChan <- *endEvent
		// 发送结束标记
		//select {
		//case resultChan <- Event{
		//	IsEnd: true,
		//}:
		//default:
		//}

		select {
		case taskDone <- true:
		default:
		}
		return true
	case "task-failed":
		// 发送错误结果
		//select {
		//case resultChan <- Event{
		//	IsEnd: true,
		//}:
		//default:
		//}
		endEvent := event
		endEvent.IsEnd = true
		resultChan <- *endEvent

		select {
		case taskDone <- true:
		default:
		}
		return true
	}
	return false
}

// 定义结构体来表示JSON数据
type AsrHeader struct {
	Action       string                 `json:"action"`
	TaskID       string                 `json:"task_id"`
	Streaming    string                 `json:"streaming"`
	Event        string                 `json:"event"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	Attributes   map[string]interface{} `json:"attributes"`
}

type Output struct {
	Sentence struct {
		BeginTime int64  `json:"begin_time"`
		EndTime   *int64 `json:"end_time"`
		Text      string `json:"text"`
		Words     []struct {
			BeginTime   int64  `json:"begin_time"`
			EndTime     *int64 `json:"end_time"`
			Text        string `json:"text"`
			Punctuation string `json:"punctuation"`
		} `json:"words"`
	} `json:"sentence"`
	Usage         interface{}   `json:"usage"`
	Translations  []Translation `json:"translations"`
	Transcription Transcription `json:"transcription"`
}
type Translation struct {
	SentenceID   int    `json:"sentence_id"`
	BeginTime    int64  `json:"begin_time"`
	EndTime      int64  `json:"end_time"`
	Text         string `json:"text"`
	Lang         string `json:"lang"`
	PreEndFailed bool   `json:"pre_end_failed"`
	Words        []Word `json:"words"`
	SentenceEnd  bool   `json:"sentence_end"`
}

type Transcription struct {
	SentenceID  int    `json:"sentence_id"`
	BeginTime   int64  `json:"begin_time"`
	EndTime     int64  `json:"end_time"`
	Text        string `json:"text"`
	Words       []Word `json:"words"`
	SentenceEnd bool   `json:"sentence_end"`
}

type Word struct {
	BeginTime   int64  `json:"begin_time"`
	EndTime     int64  `json:"end_time"`
	Text        string `json:"text"`
	Punctuation string `json:"punctuation"`
	Fixed       bool   `json:"fixed"`
	SpeakerID   *int   `json:"speaker_id"`
}

type Payload struct {
	TaskGroup  string      `json:"task_group"`
	Task       string      `json:"task"`
	Function   string      `json:"function"`
	Model      string      `json:"model"`
	Parameters interface{} `json:"parameters"`
	Resources  []Resource  `json:"resources"`
	Input      Input       `json:"input"`
	Output     *Output     `json:"output,omitempty"`
}

type VoiceParams struct {
	Format                   string   `json:"format"`
	SampleRate               int      `json:"sample_rate"`
	VocabularyID             string   `json:"vocabulary_id"`
	DisfluencyRemovalEnabled bool     `json:"disfluency_removal_enabled"`
	LanguageHints            []string `json:"language_hints"`
}

type AsrParams struct {
	Format                     string   `json:"format"`
	SampleRate                 int      `json:"sample_rate"`
	VocabularyID               string   `json:"vocabulary_id"`
	SourceLanguage             string   `json:"source_language"`
	TranslationTargetLanguages []string `json:"translation_target_languages"`
	TranscriptionEnabled       bool     `json:"transcription_enabled"`
	TranslationEnabled         bool     `json:"translation_enabled"`
	Rate                       float64  `json:"rate"`
}

type Resource struct {
	ResourceID   string `json:"resource_id"`
	ResourceType string `json:"resource_type"`
}

type Input struct {
}

type Event struct {
	IsEnd   bool      `json:"isEnd"`
	Header  AsrHeader `json:"header"`
	Payload Payload   `json:"payload"`
}

// 把音频处理成单声道、16k采样率
func (c *AsrClient) processAudio(filePath string) (string, error) {
	//dest := strings.ReplaceAll(filePath, filepath.Ext(filePath), "_mono_16K.mp3")
	dest := strings.ReplaceAll(filePath, filepath.Ext(filePath), "output.mp3")
	cmdArgs := []string{"-i", filePath, "-ac", "1", "-ar", "16000", "-b:a", "192k", dest}
	cmd := exec.Command(storage.FfmpegPath, cmdArgs...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		global.GVA_LOG.Error("处理音频失败", zap.Error(err), zap.String("audio file", filePath), zap.String("output", string(output)))
		return "", err
	}
	return dest, nil
}

// 连接WebSocket服务
func (c *AsrClient) connectWebSocket(apiKey string) (*websocket.Conn, error) {
	header := make(http.Header)
	header.Add("X-DashScope-DataInspection", "enable")
	header.Add("Authorization", fmt.Sprintf("bearer %s", apiKey))
	conn, _, err := dialer.Dial(wsURL, header)
	return conn, err
}

// 启动一个goroutine异步接收WebSocket消息
func (c *AsrClient) startResultReceiver(conn *websocket.Conn, words *[]types.Word, texts map[string]string, taskStarted chan<- bool, taskDone chan<- bool) {
	go func() {
		for {
			_, message, err := conn.ReadMessage()
			if err != nil {
				//global.GVA_LOG.Error("解析服务器消息失败：", zap.Error(err))
				continue
			}
			currentEvent := Event{}
			err = json.Unmarshal(message, &currentEvent)
			if err != nil {
				//global.GVA_LOG.Error("解析服务器消息失败：", zap.Error(err))
				continue
			}
			if currentEvent.Payload.Output != nil {
				if currentEvent.Payload.Output.Transcription.SentenceEnd {
					// 本句结束，添加当前的words和text
					texts["recognized"] += currentEvent.Payload.Output.Transcription.Text
				}

				currentNum := 0
				if len(*words) > 0 {
					currentNum = (*words)[len(*words)-1].Num + 1
				}
				for _, word := range currentEvent.Payload.Output.Sentence.Words {
					*words = append(*words, types.Word{
						Num:   currentNum,
						Text:  strings.TrimSpace(word.Text), // 阿里云这边的word后面会有空格
						Start: float64(word.BeginTime) / 1000,
						End:   float64(*word.EndTime) / 1000,
					})
					currentNum++
				}

				//if currentEvent.Payload.Output.Transcription.Text != "" {
				//	texts["translated"] = currentEvent.Payload.Output.Transcription.
				//}
				for _, translation := range currentEvent.Payload.Output.Translations {
					if translation.SentenceEnd {
						texts["translated"] += translation.Text
					}

					//fmt.Printf("	翻译结果 - Sentence ID：%d, Text：%s\n", translation.SentenceID, translation.Text)
					//for _, word := range translation.Words {
					//	fmt.Printf("	  Word - Begin Time：%d, End Time：%d, Text：%s\n", word.BeginTime, word.EndTime, word.Text)
					//}
				}
				//fmt.Println("currentEvent.Payload.Output.Transcription.Text =", currentEvent.Payload.Output.Transcription.Text)
				// 本句结束，添加当前的words和text
				//if currentEvent.Payload.Output.Transcription.Text != "" {
				//
				//	texts["recognized"] = currentEvent.Payload.Output.Transcription.Text
				//}

				//currentNum := 0
				//if len(*words) > 0 {
				//	currentNum = (*words)[len(*words)-1].Num + 1
				//}

				//transcription := currentEvent.Payload.Output.Transcription
				//fmt.Printf("	识别结果 - Sentence ID：%d, Text：%s\n", transcription.SentenceID, transcription.Text)
				//for _, word := range transcription.Words {
				//	fmt.Printf("	  Word - Begin Time：%d, End Time：%d, Text：%s\n", word.BeginTime, word.EndTime, word.Text)
				//	*words = append(*words, types.Word{
				//		Num:   currentNum,
				//		Text:  strings.TrimSpace(word.Text), // 阿里云这边的word后面会有空格
				//		Start: float64(word.BeginTime) / 1000,
				//		End:   float64(word.EndTime) / 1000,
				//	})
				//	currentNum++
				//}
			}

			if c.handleEvent(conn, &currentEvent, taskStarted, taskDone) {
				return
			}
		}
	}()
}

// 发送run-task指令
func (c *AsrClient) sendRunTaskCmd(conn *websocket.Conn) (string, error) {
	runTaskCmd, taskID, err := c.generateRunTaskCmd()
	if err != nil {
		return "", err
	}
	err = conn.WriteMessage(websocket.TextMessage, []byte(runTaskCmd))
	return taskID, err
}

// 生成run-task指令
func (c *AsrClient) generateRunTaskCmd() (string, string, error) {
	var parames interface{}
	switch c.config.Model {
	case PfRealtimeV2:
		parames = VoiceParams{
			//Format:        c.Format, //"wav",mp3 等
			SampleRate:    16000,
			LanguageHints: []string{c.config.SourceLanguage},
		}
	case GummyRealtimeV1:
		parames = AsrParams{
			//Format:                     "wav",
			Rate:                       1.5,
			SampleRate:                 16000,
			TranscriptionEnabled:       true,
			TranslationEnabled:         true,
			TranslationTargetLanguages: []string{c.config.TargetLanguages},
		}

	}
	taskID := uuid.New().String()
	runTaskCmd := Event{
		Header: AsrHeader{
			Action:    "run-task",
			TaskID:    taskID,
			Streaming: "duplex",
		},
		Payload: Payload{
			TaskGroup:  "audio",
			Task:       "asr",
			Function:   "recognition",
			Model:      c.config.Model,
			Parameters: parames,
			Input:      Input{},
		},
	}
	runTaskCmdJSON, err := json.Marshal(runTaskCmd)
	return string(runTaskCmdJSON), taskID, err
}

// 等待task-started事件
func (c *AsrClient) waitForTaskStarted(taskStarted chan bool) {
	select {
	case <-taskStarted:
		//global.GVA_LOG.Info("阿里云语音识别任务开启成功")
	case <-time.After(10 * time.Second):
		//global.GVA_LOG.Error("等待task-started超时，任务开启失败")
	}
}

// 发送音频数据
func (c *AsrClient) sendAudioData(conn *websocket.Conn, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	buf := make([]byte, 1024) // 100ms的音频大约1024字节
	for {
		n, err := file.Read(buf)
		if n == 0 {
			break
		}
		if err != nil && err != io.EOF {
			return err
		}
		err = conn.WriteMessage(websocket.BinaryMessage, buf[:n])
		if err != nil {
			return err
		}
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

func (c *AsrClient) sendAudio(conn *websocket.Conn, audio io.Reader) error {
	buf := make([]byte, 1024) // 100ms的音频大约1024字节
	for {
		n, err := audio.Read(buf)
		if n == 0 {
			break
		}
		if err != nil && err != io.EOF {
			return err
		}
		err = conn.WriteMessage(websocket.BinaryMessage, buf[:n])
		if err != nil {
			return err
		}
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

// 发送finish-task指令
func (c *AsrClient) sendFinishTaskCmd(conn *websocket.Conn, taskID string) error {
	finishTaskCmd, err := c.generateFinishTaskCmd(taskID)
	if err != nil {
		return err
	}
	err = conn.WriteMessage(websocket.TextMessage, []byte(finishTaskCmd))
	return err
}

// 生成finish-task指令
func (c *AsrClient) generateFinishTaskCmd(taskID string) (string, error) {
	finishTaskCmd := Event{
		Header: AsrHeader{
			Action:    "finish-task",
			TaskID:    taskID,
			Streaming: "duplex",
		},
		Payload: Payload{
			Input: Input{},
		},
	}
	finishTaskCmdJSON, err := json.Marshal(finishTaskCmd)
	return string(finishTaskCmdJSON), err
}

// 处理事件
func (c *AsrClient) handleEvent(conn *websocket.Conn, event *Event, taskStarted chan<- bool, taskDone chan<- bool) bool {
	switch event.Header.Event {
	case "task-started":
		//global.GVA_LOG.Info("收到task-started事件", zap.String("taskID", event.Header.TaskID))
		taskStarted <- true
	case "result-generated":

		//fmt.Printf("收到result-generated事件 taskID:%v,timestamp:%v\n", event.Header.TaskID, time.Now())

		// 增强的结果处理 - 包含完整的null safety检查和格式化输出
		//c.processEnhancedASRResults(event)
	case "task-finished":
		//global.GVA_LOG.Info("收到task-finished事件，任务完成", zap.String("taskID", event.Header.TaskID))
		taskDone <- true
		return true
	case "task-failed":
		//global.GVA_LOG.Info("收到task-failed事件", zap.String("taskID", event.Header.TaskID))
		c.handleTaskFailed(event, conn)
		taskDone <- true
		return true
	default:
		//global.GVA_LOG.Info("未知事件：", zap.String("event", event.Header.Event))
	}
	return false
}

// processStreamingASRResults 处理流式ASR结果 - 专门用于流式处理上下文
// 包含完整的null safety检查和去重逻辑
func (c *AsrClient) processStreamingASRResults(event *Event, resultChan chan<- StreamingTranscriptionResult) {
	// 添加序列号用于跟踪结果顺序
	sequenceNumber := time.Now().UnixNano()
	timestamp := time.Now().Format("15:04:05.000")

	// 1. 首先进行null safety检查
	if event == nil {
		global.GVA_LOG.Warn("流式ASR结果处理: event为nil")
		return
	}

	if event.Payload.Output == nil {
		global.GVA_LOG.Debug("流式ASR结果处理: Payload.Output为nil",
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))
		return
	}

	output := event.Payload.Output

	// 2. 处理语音识别结果 - 优先使用句子级别结果，避免重复
	if output.Sentence.Text != "" {
		// 安全检查EndTime指针
		isPartial := output.Sentence.EndTime == nil

		global.GVA_LOG.Debug("流式ASR - 句子级别结果",
			zap.String("text", output.Sentence.Text),
			zap.Bool("isPartial", isPartial),
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))

		result := StreamingTranscriptionResult{
			Text:      output.Sentence.Text,
			IsPartial: isPartial,
			IsEnd:     false,
		}

		select {
		case resultChan <- result:
			global.GVA_LOG.Debug("流式ASR结果已发送",
				zap.String("text", output.Sentence.Text),
				zap.Bool("isPartial", isPartial))
		default:
			global.GVA_LOG.Warn("流式ASR结果发送失败: 通道已满",
				zap.String("text", output.Sentence.Text))
		}
	} else if output.Transcription.Text != "" {
		// 只有在没有句子级别结果时才使用完整转录结果
		isPartial := !output.Transcription.SentenceEnd

		global.GVA_LOG.Debug("流式ASR - 完整转录结果",
			zap.String("text", output.Transcription.Text),
			zap.Bool("isPartial", isPartial),
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))

		result := StreamingTranscriptionResult{
			Text:      output.Transcription.Text,
			IsPartial: isPartial,
			IsEnd:     false,
		}

		select {
		case resultChan <- result:
			global.GVA_LOG.Debug("流式转录结果已发送",
				zap.String("text", output.Transcription.Text),
				zap.Bool("isPartial", isPartial))
		default:
			global.GVA_LOG.Warn("流式转录结果发送失败: 通道已满",
				zap.String("text", output.Transcription.Text))
		}
	}

	// 3. 处理翻译结果 - 包含null safety检查
	if len(output.Translations) > 0 {
		global.GVA_LOG.Debug("流式ASR - 处理翻译结果",
			zap.Int("translationCount", len(output.Translations)),
			zap.String("timestamp", timestamp))

		for i, translation := range output.Translations {
			if translation.Text != "" {
				global.GVA_LOG.Debug("流式翻译结果",
					zap.Int("index", i),
					zap.String("text", translation.Text),
					zap.String("language", translation.Lang),
					zap.Bool("sentenceEnd", translation.SentenceEnd))

				result := StreamingTranscriptionResult{
					Text:         translation.Text,
					IsPartial:    !translation.SentenceEnd,
					IsEnd:        false,
					IsTranslated: true, // 标记为翻译结果
					Language:     translation.Lang,
				}

				select {
				case resultChan <- result:
					global.GVA_LOG.Debug("流式翻译结果已发送",
						zap.String("text", translation.Text),
						zap.String("language", translation.Lang))
				default:
					global.GVA_LOG.Warn("流式翻译结果发送失败: 通道已满",
						zap.String("text", translation.Text),
						zap.String("language", translation.Lang))
				}
			} else {
				global.GVA_LOG.Debug("跳过空翻译结果",
					zap.Int("index", i),
					zap.String("language", translation.Lang))
			}
		}
	}

	// 4. 错误处理和数据完整性检查
	if output.Sentence.Text == "" && output.Transcription.Text == "" && len(output.Translations) == 0 {
		global.GVA_LOG.Debug("流式ASR处理: 本次事件无有效结果",
			zap.String("timestamp", timestamp),
			zap.String("taskID", event.Header.TaskID))
	}
}

// 处理任务失败事件
func (c *AsrClient) handleTaskFailed(event *Event, conn *websocket.Conn) {
	//global.GVA_LOG.Error("任务失败：", zap.String("error", event.Header.ErrorMessage))
}

// processBasicStreamingASRResults 处理基础流式ASR结果 - 仅处理语音识别，不包含翻译
// 专门用于基础流式处理上下文，包含完整的null safety检查
func (c *AsrClient) processBasicStreamingASRResults(event *Event, resultChan chan<- StreamingTranscriptionResult) {
	// 添加序列号用于跟踪结果顺序
	sequenceNumber := time.Now().UnixNano()
	timestamp := time.Now().Format("15:04:05.000")

	// 1. 首先进行null safety检查
	if event == nil {
		safeLogWarn("基础流式ASR结果处理: event为nil")
		return
	}

	if event.Payload.Output == nil {
		safeLogDebug("基础流式ASR结果处理: Payload.Output为nil",
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))
		return
	}

	output := event.Payload.Output

	// 2. 处理语音识别结果 - 优先使用句子级别结果，避免重复
	if output.Sentence.Text != "" {
		// 安全检查EndTime指针
		isPartial := output.Sentence.EndTime == nil

		safeLogDebug("基础流式ASR - 句子级别结果",
			zap.String("text", output.Sentence.Text),
			zap.Bool("isPartial", isPartial),
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))

		result := StreamingTranscriptionResult{
			Text:      output.Sentence.Text,
			IsPartial: isPartial,
			IsEnd:     false,
		}

		select {
		case resultChan <- result:
			safeLogDebug("基础流式ASR结果已发送",
				zap.String("text", output.Sentence.Text),
				zap.Bool("isPartial", isPartial))
		default:
			safeLogWarn("基础流式ASR结果发送失败: 通道已满",
				zap.String("text", output.Sentence.Text))
		}
	} else if output.Transcription.Text != "" {
		// 只有在没有句子级别结果时才使用完整转录结果
		// 安全检查SentenceEnd字段
		isPartial := !output.Transcription.SentenceEnd

		safeLogDebug("基础流式ASR - 完整转录结果",
			zap.String("text", output.Transcription.Text),
			zap.Bool("isPartial", isPartial),
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))

		result := StreamingTranscriptionResult{
			Text:      output.Transcription.Text,
			IsPartial: isPartial,
			IsEnd:     false,
		}

		select {
		case resultChan <- result:
			safeLogDebug("基础流式转录结果已发送",
				zap.String("text", output.Transcription.Text),
				zap.Bool("isPartial", isPartial))
		default:
			safeLogWarn("基础流式转录结果发送失败: 通道已满",
				zap.String("text", output.Transcription.Text))
		}
	} else {
		// 3. 错误处理和数据完整性检查
		safeLogDebug("基础流式ASR处理: 本次事件无语音识别结果",
			zap.String("timestamp", timestamp),
			zap.String("taskID", event.Header.TaskID),
			zap.Int64("sequence", sequenceNumber))
	}
}
func (c *AsrClient) processBasicStreamingASRResults1(event *Event, resultChan chan<- Event) {
	// 添加序列号用于跟踪结果顺序
	sequenceNumber := time.Now().UnixNano()
	timestamp := time.Now().Format("15:04:05.000")

	// 1. 首先进行null safety检查
	if event == nil {
		safeLogWarn("基础流式ASR结果处理: event为nil")
		return
	}

	if event.Payload.Output == nil {
		safeLogDebug("基础流式ASR结果处理: Payload.Output为nil",
			zap.String("timestamp", timestamp),
			zap.Int64("sequence", sequenceNumber))
		return
	}

	//transcription := output.Transcription
	//fmt.Printf("	识别结果 - Sentence ID：%d, Text：%s\n", transcription.SentenceID, transcription.Text)
	resultChan <- *event
}

// 关闭连接
func (c *AsrClient) closeConnection(conn *websocket.Conn) {
	if conn != nil {
		conn.Close()
	}
}

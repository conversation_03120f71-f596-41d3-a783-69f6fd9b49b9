package qwen

import (
	"airAi/common/requst"
	"context"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

// 初始化 OpenAI 客户端
// 使用集中化配置管理API密钥和基础URL
func NewQwenClient() *QwenClient {
	apiKey := GetAPIKey()
	baseURL := GetBaseURL()

	return &QwenClient{
		Client: openai.NewClient(
			option.WithAPIKey(apiKey),
			option.WithBaseURL(baseURL),
		),
	}
}

func (q *QwenClient) Text(ctx context.Context, prompt string) (string, error) {
	resp, err := q.Client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Model: "qwen-plus",
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.UserMessage(prompt),
		},
	})
	if err != nil {
		return "", err
	}
	return resp.Choices[0].Message.Content, nil
}

// StreamingTextChat 流式文本聊天方法
// 用于实现AI文本对话的流式响应，逐步返回AI生成的内容
func (q *QwenClient) StreamingTextChat(ctx context.Context, prompt string, resultChan chan<- StreamingChatResult) error {
	// 创建流式聊天请求
	stream := q.Client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
		Model: "qwen-plus",
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.UserMessage(prompt),
		},
	})

	// 处理流式响应
	for stream.Next() {
		chunk := stream.Current()

		// 检查是否有内容返回
		if len(chunk.Choices) > 0 && chunk.Choices[0].Delta.Content != "" {
			result := StreamingChatResult{
				Content:   chunk.Choices[0].Delta.Content,
				IsPartial: true,
				IsEnd:     false,
			}

			// 发送部分结果到通道
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// 通道已满或已关闭，跳过此块
			}
		}

		// 检查是否为最后一个块
		if len(chunk.Choices) > 0 && chunk.Choices[0].FinishReason != "" {
			result := StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}

			// 发送结束标记
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
			break
		}
	}

	// 处理流式响应中的错误
	if err := stream.Err(); err != nil && err != io.EOF {
		result := StreamingChatResult{
			Content:   "",
			IsPartial: false,
			IsEnd:     true,
			Error:     err.Error(),
		}

		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		return err
	}

	return nil
}

// StreamingTextChatWithContext 带对话上下文的流式文本聊天方法
// 用于实现AI文本对话的流式响应，支持完整的对话历史
func (q *QwenClient) StreamingTextChatWithContext(ctx context.Context, contextMessages []requst.ConversationMessage, systemPrompt string, resultChan chan<- StreamingChatResult) error {
	// 构建消息列表
	messages := make([]openai.ChatCompletionMessageParamUnion, 0)

	// 添加系统提示词（如果提供）
	if systemPrompt != "" {
		messages = append(messages, openai.SystemMessage(systemPrompt))
	}

	// 添加对话历史消息
	for _, msg := range contextMessages {
		switch msg.Role {
		case "user":
			messages = append(messages, openai.UserMessage(msg.Content))
		case "assistant":
			messages = append(messages, openai.AssistantMessage(msg.Content))
		case "system":
			messages = append(messages, openai.SystemMessage(msg.Content))
		}
	}

	// 创建流式聊天请求
	stream := q.Client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
		Model:    "qwen-plus",
		Messages: messages,
	})

	// 处理流式响应
	for stream.Next() {
		chunk := stream.Current()

		// 检查是否有内容返回
		if len(chunk.Choices) > 0 && chunk.Choices[0].Delta.Content != "" {
			result := StreamingChatResult{
				Content:   chunk.Choices[0].Delta.Content,
				IsPartial: true,
				IsEnd:     false,
			}

			// 发送部分结果到通道
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// 通道已满或已关闭，跳过此块
			}
		}

		// 检查是否为最后一个块
		if len(chunk.Choices) > 0 && chunk.Choices[0].FinishReason != "" {
			result := StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}

			// 发送结束标记到通道
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
			break
		}
	}

	// 处理流式响应中的错误
	if err := stream.Err(); err != nil && err != io.EOF {
		result := StreamingChatResult{
			Content:   "",
			IsPartial: false,
			IsEnd:     true,
			Error:     err.Error(),
		}

		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		return err
	}

	return nil
}

// StreamingTranslateResult 流式翻译结果结构 - 增强版
type StreamingTranslateResult struct {
	Content       string             `json:"content"`                  // 翻译后的文本内容
	OriginalText  string             `json:"original_text,omitempty"`  // 原始ASR转录文本
	VoiceResponse *VoiceResponseData `json:"voice_response,omitempty"` // 语音响应数据
	IsPartial     bool               `json:"is_partial"`               // 是否为部分结果
	IsEnd         bool               `json:"is_end"`                   // 是否为结束标记
	RSentenceEnd  bool               `json:"r_sentence_end"`           // 当前识别文本是否构成完整的句子
	TSentenceEnd  bool               `json:"t_sentence_end"`           // 当前翻译文本是否构成完整的句子
	Error         string             `json:"error,omitempty"`          // 错误信息
}

// VoiceResponseData 语音响应数据结构
type VoiceResponseData struct {
	AudioData   []byte            `json:"audio_data,omitempty"`   // 音频数据 (base64编码)
	AudioFormat string            `json:"audio_format,omitempty"` // 音频格式 (mp3, wav, etc.)
	Duration    float64           `json:"duration,omitempty"`     // 音频时长(秒)
	SampleRate  int               `json:"sample_rate,omitempty"`  // 采样率
	Metadata    map[string]string `json:"metadata,omitempty"`     // 额外元数据
}

// ComprehensiveVoiceResult 综合语音处理结果结构
type ComprehensiveVoiceResult struct {
	// ASR (Speech Recognition) Results
	OriginalText string `json:"original_text,omitempty"` // 原始转录文本
	ASRPartial   bool   `json:"asr_partial,omitempty"`   // ASR结果是否为部分结果

	// Translation Results
	TranslatedText string `json:"translated_text,omitempty"` // 翻译后文本
	TransPartial   bool   `json:"trans_partial,omitempty"`   // 翻译结果是否为部分结果

	// Voice Response Data
	VoiceResponse *VoiceResponseData `json:"voice_response,omitempty"` // 语音响应数据

	// Processing Metadata
	ProcessingType string `json:"processing_type,omitempty"` // 处理类型: "asr", "translation", "tts", "complete"
	Timestamp      int64  `json:"timestamp,omitempty"`       // 时间戳
	SourceLanguage string `json:"source_language,omitempty"` // 源语言
	TargetLanguage string `json:"target_language,omitempty"` // 目标语言

	// Control Fields
	IsPartial bool   `json:"is_partial"`      // 整体结果是否为部分结果
	IsEnd     bool   `json:"is_end"`          // 是否为结束标记
	Error     string `json:"error,omitempty"` // 错误信息
}

// StreamingTranslate 流式翻译方法
func (q *QwenClient) StreamingTranslate(ctx context.Context, sourceLanguage, targetLanguage, text string, resultChan chan<- StreamingTranslateResult) error {
	prompt := fmt.Sprintf(TranslatePrompt, text, targetLanguage)

	stream := q.Client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
		Model: "qwen-plus",
		Messages: []openai.ChatCompletionMessageParamUnion{
			openai.UserMessage(prompt),
		},
	})

	for stream.Next() {
		chunk := stream.Current()

		if len(chunk.Choices) > 0 && chunk.Choices[0].Delta.Content != "" {
			result := StreamingTranslateResult{
				Content:   chunk.Choices[0].Delta.Content,
				IsPartial: true,
				IsEnd:     false,
			}

			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
				// Channel is full or closed, skip this chunk
			}
		}

		// Check if this is the last chunk
		if len(chunk.Choices) > 0 && chunk.Choices[0].FinishReason != "" {
			result := StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}

			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
			break
		}
	}

	if err := stream.Err(); err != nil && err != io.EOF {
		result := StreamingTranslateResult{
			Content:   "",
			IsPartial: false,
			IsEnd:     true,
			Error:     err.Error(),
		}

		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		return err
	}

	return nil
}

// StreamingTTS 流式文本转语音方法
func (q *QwenClient) StreamingTTS(text string) (*VoiceResponseData, error) {
	if text == "" {
		return nil, nil
	}

	// 使用Sambert TTS服务生成语音
	// 生成临时文件名
	tempFile := fmt.Sprintf("/tmp/tts_%d.mp3", time.Now().UnixNano())

	// 调用Sambert TTS服务
	err := NewSambert("sambert-zhinan-v1", text, tempFile)
	if err != nil {
		// 如果TTS失败，返回模拟数据以保持服务可用性
		return &VoiceResponseData{
			AudioFormat: "mp3",
			SampleRate:  16000,
			Duration:    float64(len(text)) * 0.1,
			Metadata: map[string]string{
				"tts_model": "sambert-zhinan-v1",
				"voice":     "default",
				"status":    "simulated", // 标记为模拟数据
			},
		}, nil
	}

	// 读取生成的音频文件
	audioData, err := os.ReadFile(tempFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read TTS audio file: %w", err)
	}

	// 清理临时文件
	defer func() {
		if _, err := os.Stat(tempFile); err == nil {
			os.Remove(tempFile)
		}
	}()

	// 计算音频时长（简单估算）
	duration := float64(len(text)) * 0.15 // 大约每个字符0.15秒

	voiceResponse := &VoiceResponseData{
		AudioData:   audioData,
		AudioFormat: "mp3",
		SampleRate:  16000,
		Duration:    duration,
		Metadata: map[string]string{
			"tts_model": "sambert-zhinan-v1",
			"voice":     "default",
			"status":    "generated",
		},
	}

	return voiceResponse, nil
}

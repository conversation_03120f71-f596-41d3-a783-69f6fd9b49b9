package qwen

import (
	"context"
	"fmt"
	"testing"
)

// TestConfigBuilder 测试配置构建器
func TestConfigBuilder(t *testing.T) {
	t.<PERSON>("ValidConfig", func(t *testing.T) {
		config, err := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en", "ja").
			WithAudioConfig(16000, "pcm").
			Build()

		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if config.ApiKey != "test-api-key" {
			t.<PERSON>rf("Expected ApiKey 'test-api-key', got '%s'", config.ApiKey)
		}

		if config.SourceLanguage != "zh" {
			t.Errorf("Expected SourceLanguage 'zh', got '%s'", config.SourceLanguage)
		}

		if len(config.TargetLanguages) != 2 {
			t.<PERSON>("Expected 2 target languages, got %d", len(config.TargetLanguages))
		}
	})

	t.<PERSON>("MissingApiKey", func(t *testing.T) {
		_, err := NewConfigBuilder().
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		if err == nil {
			t.Fatal("Expected error for missing API key")
		}
	})

	t.Run("MissingSourceLanguage", func(t *testing.T) {
		_, err := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithTargetLanguages("en").
			Build()

		if err == nil {
			t.Fatal("Expected error for missing source language")
		}
	})
}

// TestServiceFactory 测试服务工厂
func TestServiceFactory(t *testing.T) {
	factory := NewServiceFactory()

	t.Run("CreateServiceWithDefaults", func(t *testing.T) {
		service, err := factory.CreateServiceWithDefaults("test-api-key", "zh", "en")
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if service == nil {
			t.Fatal("Expected service to be created")
		}
	})

	t.Run("CreateASROnlyService", func(t *testing.T) {
		service, err := factory.CreateASROnlyService("test-api-key", "zh")
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if service == nil {
			t.Fatal("Expected service to be created")
		}
	})

	t.Run("CreateTranslationService", func(t *testing.T) {
		service, err := factory.CreateTranslationService("test-api-key", "zh", "en", "ja")
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if service == nil {
			t.Fatal("Expected service to be created")
		}
	})
}

// TestConfigValidator 测试配置验证器
func TestConfigValidator(t *testing.T) {
	validator := NewConfigValidator()

	t.Run("ValidConfig", func(t *testing.T) {
		config, _ := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		err := validator.ValidateConfig(config)
		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
	})

	t.Run("NilConfig", func(t *testing.T) {
		err := validator.ValidateConfig(nil)
		if err == nil {
			t.Error("Expected error for nil config")
		}
	})

	t.Run("ValidLanguage", func(t *testing.T) {
		err := validator.ValidateLanguage("zh")
		if err != nil {
			t.Errorf("Expected no error for valid language, got %v", err)
		}
	})

	t.Run("InvalidLanguage", func(t *testing.T) {
		err := validator.ValidateLanguage("invalid")
		if err == nil {
			t.Error("Expected error for invalid language")
		}
	})
}

// TestConfigPresets 测试配置预设
func TestConfigPresets(t *testing.T) {
	presets := NewConfigPresets()

	t.Run("DefaultConfig", func(t *testing.T) {
		config := presets.DefaultConfig("test-api-key", "zh", "en")
		if config == nil {
			t.Fatal("Expected config to be created")
		}

		if config.ApiKey != "test-api-key" {
			t.Errorf("Expected ApiKey 'test-api-key', got '%s'", config.ApiKey)
		}
	})

	t.Run("HighQualityConfig", func(t *testing.T) {
		config := presets.HighQualityConfig("test-api-key", "zh", "en")
		if config == nil {
			t.Fatal("Expected config to be created")
		}

		if config.SampleRate != 44100 {
			t.Errorf("Expected SampleRate 44100, got %d", config.SampleRate)
		}
	})

	t.Run("LowLatencyConfig", func(t *testing.T) {
		config := presets.LowLatencyConfig("test-api-key", "zh", "en")
		if config == nil {
			t.Fatal("Expected config to be created")
		}

		if config.AudioBufferSize != 256 {
			t.Errorf("Expected AudioBufferSize 256, got %d", config.AudioBufferSize)
		}
	})

	t.Run("MobileConfig", func(t *testing.T) {
		config := presets.MobileConfig("test-api-key", "zh", "en")
		if config == nil {
			t.Fatal("Expected config to be created")
		}

		if !config.EnableVAD {
			t.Error("Expected VAD to be enabled for mobile config")
		}
	})
}

// TestServiceManager 测试服务管理器
func TestServiceManager(t *testing.T) {
	manager := NewServiceManager()

	t.Run("CreateAndRegisterService", func(t *testing.T) {
		config, _ := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		service, err := manager.CreateAndRegisterService("test-service", config)
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		if service == nil {
			t.Fatal("Expected service to be created")
		}

		// 测试重复注册
		_, err = manager.CreateAndRegisterService("test-service", config)
		if err == nil {
			t.Error("Expected error for duplicate service name")
		}
	})

	t.Run("GetService", func(t *testing.T) {
		config, _ := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		manager.CreateAndRegisterService("get-test", config)

		service, exists := manager.GetService("get-test")
		if !exists {
			t.Error("Expected service to exist")
		}

		if service == nil {
			t.Error("Expected service to be returned")
		}

		_, exists = manager.GetService("non-existent")
		if exists {
			t.Error("Expected service not to exist")
		}
	})

	t.Run("ListServices", func(t *testing.T) {
		config, _ := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		manager.CreateAndRegisterService("list-test-1", config)
		manager.CreateAndRegisterService("list-test-2", config)

		services := manager.ListServices()
		if len(services) < 2 {
			t.Errorf("Expected at least 2 services, got %d", len(services))
		}
	})

	t.Run("RemoveService", func(t *testing.T) {
		config, _ := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en").
			Build()

		manager.CreateAndRegisterService("remove-test", config)

		err := manager.RemoveService("remove-test")
		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}

		_, exists := manager.GetService("remove-test")
		if exists {
			t.Error("Expected service to be removed")
		}

		// 测试移除不存在的服务
		err = manager.RemoveService("non-existent")
		if err == nil {
			t.Error("Expected error for non-existent service")
		}
	})
}

// TestDefaultAudioProcessor 测试默认音频处理器
func TestDefaultAudioProcessor(t *testing.T) {
	processor := NewDefaultAudioProcessor(16000, 1, "pcm")

	t.Run("ProcessChunk", func(t *testing.T) {
		input := []byte{1, 2, 3, 4, 5}
		output, err := processor.ProcessChunk(input)

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}

		if len(output) != len(input) {
			t.Errorf("Expected output length %d, got %d", len(input), len(output))
		}

		for i, b := range output {
			if b != input[i] {
				t.Errorf("Expected output[%d] = %d, got %d", i, input[i], b)
			}
		}
	})

	t.Run("GetProperties", func(t *testing.T) {
		if processor.GetSampleRate() != 16000 {
			t.Errorf("Expected sample rate 16000, got %d", processor.GetSampleRate())
		}

		if processor.GetChannels() != 1 {
			t.Errorf("Expected channels 1, got %d", processor.GetChannels())
		}
	})

	t.Run("Reset", func(t *testing.T) {
		err := processor.Reset()
		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
	})
}

// MockStreamingService 模拟流式服务用于测试
type MockStreamingService struct {
	isConnected bool
	asrResults  chan ASRResult
	errors      chan error
}

func NewMockStreamingService() *MockStreamingService {
	return &MockStreamingService{
		asrResults: make(chan ASRResult, 10),
		errors:     make(chan error, 10),
	}
}

func (m *MockStreamingService) Start(ctx context.Context) error {
	m.isConnected = true
	return nil
}

func (m *MockStreamingService) Stop() error {
	m.isConnected = false
	close(m.asrResults)
	close(m.errors)
	return nil
}

func (m *MockStreamingService) SendAudio(audioData []byte) error {
	if !m.isConnected {
		return fmt.Errorf("service not connected")
	}
	return nil
}

func (m *MockStreamingService) GetASRResults() <-chan ASRResult {
	return m.asrResults
}

func (m *MockStreamingService) GetTranslationResults() <-chan TranslationResult {
	return make(chan TranslationResult)
}

func (m *MockStreamingService) GetErrors() <-chan error {
	return m.errors
}

func (m *MockStreamingService) IsConnected() bool {
	return m.isConnected
}

func (m *MockStreamingService) GetConnectionInfo() ConnectionInfo {
	return ConnectionInfo{IsConnected: m.isConnected}
}

// BenchmarkConfigBuilder 配置构建器性能测试
func BenchmarkConfigBuilder(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := NewConfigBuilder().
			WithApiKey("test-api-key").
			WithSourceLanguage("zh").
			WithTargetLanguages("en", "ja").
			Build()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkServiceCreation 服务创建性能测试
func BenchmarkServiceCreation(b *testing.B) {
	factory := NewServiceFactory()

	for i := 0; i < b.N; i++ {
		service, err := factory.CreateServiceWithDefaults("test-api-key", "zh", "en")
		if err != nil {
			b.Fatal(err)
		}
		_ = service
	}
}

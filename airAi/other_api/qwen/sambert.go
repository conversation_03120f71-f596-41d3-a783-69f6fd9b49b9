package qwen

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

const (
// outputFile = "output.mp3" // 输出文件路径
)

func NewSambert(model, text, outputFile string) error {
	// 使用默认语音速率1.5调用新方法
	return NewSambertWithRate(model, text, outputFile, 1.5)
}

// NewSambertWithRate 带语音速率控制的Sambert TTS
func NewSambertWithRate(model, text, outputFile string, speechRate float64) error {
	// 检查并清空输出文件
	if err := clearOutputFile(outputFile); err != nil {
		fmt.Println("清空输出文件失败：", err)
		return err
	}
	// 连接WebSocket服务
	conn, err := connectWebSocket(GetAPIKey())
	if err != nil {
		fmt.Println("连接WebSocket失败：", err)
		return err
	}
	defer closeConnection(conn)

	// 创建一个通道用于接收任务完成的通知
	done := make(chan struct{})

	// 启动异步接收消息的goroutine
	go receiveMessage(conn, done, outputFile)

	// 发送run-task指令，传入语音速率参数
	if err := sendRunTaskMsgWithRate(conn, model, text, speechRate); err != nil {
		fmt.Println("发送run-task指令失败：", err)
		return err
	}

	// 等待任务完成或超时
	select {
	case <-done:
		fmt.Println("任务结束")
	case <-time.After(5 * time.Minute):
		fmt.Println("任务超时")
	}

	return nil
}

// 定义消息结构体
type Message struct {
	Header  Header         `json:"header"`
	Payload SambertPayload `json:"payload"`
}

// 定义头部信息
type Header struct {
	Action       string                 `json:"action,omitempty"`
	TaskID       string                 `json:"task_id"`
	Streaming    string                 `json:"streaming,omitempty"`
	Event        string                 `json:"event,omitempty"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	Attributes   map[string]interface{} `json:"attributes"`
}

// 定义负载信息
type SambertPayload struct {
	Model      string            `json:"model,omitempty"`
	TaskGroup  string            `json:"task_group,omitempty"`
	Task       string            `json:"task,omitempty"`
	Function   string            `json:"function,omitempty"`
	Input      SambertInput      `json:"input,omitempty"`
	Parameters SambertParameters `json:"parameters,omitempty"`
	Output     SambertOutput     `json:"output,omitempty"`
	Usage      Usage             `json:"usage,omitempty"`
}

// 定义输入信息
type SambertInput struct {
	Text string `json:"text"`
}

// 定义参数信息
type SambertParameters struct {
	TextType                string  `json:"text_type"`
	Format                  string  `json:"format"`
	SampleRate              int     `json:"sample_rate"`
	Volume                  int     `json:"volume"`
	Rate                    float64 `json:"rate"`
	Pitch                   float64 `json:"pitch"`
	WordTimestampEnabled    bool    `json:"word_timestamp_enabled"`
	PhonemeTimestampEnabled bool    `json:"phoneme_timestamp_enabled"`
}

// 定义输出信息
type SambertOutput struct {
	Sentence Sentence `json:"sentence"`
}

// 定义句子信息
type Sentence struct {
	BeginTime int           `json:"begin_time"`
	EndTime   int           `json:"end_time"`
	Words     []SambertWord `json:"words"`
}

// 定义单词信息
type SambertWord struct {
	Text      string    `json:"text"`
	BeginTime int       `json:"begin_time"`
	EndTime   int       `json:"end_time"`
	Phonemes  []Phoneme `json:"phonemes"`
}

// 定义音素信息
type Phoneme struct {
	BeginTime int    `json:"begin_time"`
	EndTime   int    `json:"end_time"`
	Text      string `json:"text"`
	Tone      int    `json:"tone"`
}

// 定义使用信息
type Usage struct {
	Characters int `json:"characters"`
}

func receiveMessage(conn *websocket.Conn, done chan struct{}, outputFile string) {
	for {
		msgType, message, err := conn.ReadMessage()
		if err != nil {
			fmt.Println("解析服务器消息失败：", err)
			close(done)
			break
		}

		if msgType == websocket.BinaryMessage {
			// 处理二进制音频流
			if err := writeBinaryDataToFile(message, outputFile); err != nil {
				fmt.Println("写入二进制数据失败：", err)
				close(done)
				break
			}
			//fmt.Println("音频片段已写入本地文件")
		} else {
			fmt.Println("message = ", string(message))
			// 处理文本消息
			var msg Message
			if err := json.Unmarshal(message, &msg); err != nil {
				fmt.Println("解析事件失败：", err)
				continue
			}
			if handleMessage(conn, msg, done) {
				break
			}
		}
	}
}

func handleMessage(conn *websocket.Conn, msg Message, done chan struct{}) bool {
	switch msg.Header.Event {
	case "task-started":
		fmt.Println("任务已启动")

	case "result-generated":
	// 如需获取附加消息，可在此处添加相应代码

	case "task-finished":
		fmt.Println("任务已完成")
		close(done)
		return true

	case "task-failed":
		if msg.Header.ErrorMessage != "" {
			fmt.Printf("任务失败：%s\n", msg.Header.ErrorMessage)
		} else {
			fmt.Println("未知原因导致任务失败")
		}
		close(done)
		return true

	default:
		fmt.Printf("预料之外的事件：%v\n", msg)
		close(done)
	}

	return false
}

// sendRunTaskMsgWithRate 发送带语音速率控制的run-task消息
func sendRunTaskMsgWithRate(conn *websocket.Conn, model, text string, speechRate float64) error {
	runTaskMsg, err := generateRunTaskMsgWithRate(model, text, speechRate)
	if err != nil {
		return err
	}
	if err := conn.WriteMessage(websocket.TextMessage, []byte(runTaskMsg)); err != nil {
		return err
	}
	return nil
}

// generateRunTaskMsgWithRate 生成带语音速率控制的run-task消息
func generateRunTaskMsgWithRate(model, text string, speechRate float64) (string, error) {
	runTaskMessage := Message{
		Header: Header{
			Action:    "run-task",
			TaskID:    uuid.New().String(),
			Streaming: "out",
		},
		Payload: SambertPayload{
			Model:     model,
			TaskGroup: "audio",
			Task:      "tts",
			Function:  "SpeechSynthesizer",
			Input: SambertInput{
				Text: text,
			},
			Parameters: SambertParameters{
				TextType:                "PlainText",
				Format:                  "mp3",
				SampleRate:              16000,
				Volume:                  50,
				Rate:                    speechRate, // 使用传入的语音速率参数
				Pitch:                   1.0,
				WordTimestampEnabled:    true,
				PhonemeTimestampEnabled: true,
			},
		},
	}

	runTaskMsgJSON, err := json.Marshal(runTaskMessage)
	return string(runTaskMsgJSON), err
}

func connectWebSocket(apiKey string) (*websocket.Conn, error) {
	header := make(http.Header)
	header.Add("X-DashScope-DataInspection", "enable")
	header.Add("Authorization", fmt.Sprintf("bearer %s", apiKey))
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, header)
	if err != nil {
		fmt.Println("连接WebSocket失败：", err)
		return nil, err
	}
	return conn, nil
}

func writeBinaryDataToFile(data []byte, filePath string) error {
	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = file.Write(data)
	return err
}

func closeConnection(conn *websocket.Conn) {
	if conn != nil {
		conn.Close()
	}
}

func clearOutputFile(filePath string) error {
	file, err := os.OpenFile(filePath, os.O_TRUNC|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	file.Close()
	return nil
}

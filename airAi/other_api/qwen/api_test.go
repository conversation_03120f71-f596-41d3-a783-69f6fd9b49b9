package qwen

import (
	"airAi/other_api"
	"airAi/utils"
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"
)

func TestText(t *testing.T) {
	//client := NewQwenClient(Config{
	//	//APIKey:  "sk-b9ea5cc9be9d4585b46cf35a91e985b9",
	//	APIKey:  "sk-130c858aa2a345949409d91ff45e0367",
	//	BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1/",
	//})
	client := NewQwenClient()
	currentDate := time.Now().Format("2006-01-02")
	prompt := fmt.Sprintf(
		MeetingPrompt, "en", currentDate, currentDate, "张三介绍了下一个版本的迭代方向，重点在用户反馈问题修复；\n李四提出优化注册流程，建议增加手机号登录；\n王五将负责对接 UI 设计资源，预计两天内完成。")
	resp, err := client.Text(context.Background(), prompt)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
	str, err := utils.ExtractAndParseJSON(resp)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("ExtractAndParseJSON", str)
	var meeting other_api.MeetingNote
	err = json.Unmarshal([]byte(str), &meeting)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("meeting = ", meeting)

}
func TestTranslate(t *testing.T) {
	//client := NewQwenClient(Config{
	//	//APIKey:  "sk-b9ea5cc9be9d4585b46cf35a91e985b9",
	//	APIKey:  "sk-130c858aa2a345949409d91ff45e0367",
	//	BaseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1/",
	//})
	client := NewQwenClient()
	prompt := fmt.Sprintf(
		TranslatePrompt, "我想买一个面包", "en")
	resp, err := client.Text(context.Background(), prompt)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
	//str, err := utils.ExtractAndParseJSON(resp)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//fmt.Println("ExtractAndParseJSON", str)
	//var meeting other_api.MeetingNote
	//err = json.Unmarshal([]byte(str), &meeting)
	//if err != nil {
	//	t.Fatal(err)
	//}
	//fmt.Println("meeting = ", meeting)

}

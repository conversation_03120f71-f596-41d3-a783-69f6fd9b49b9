package qwen

import (
	"airAi/global"
	"fmt"
	"os"
)

// GetAPIKey 获取Qwen API密钥
// 优先级：环境变量 > 配置文件 > 默认值
func GetAPIKey() string {
	// 1. 优先从环境变量获取
	if apiKey := os.Getenv("QWEN_API_KEY"); apiKey != "" {
		return apiKey
	}

	// 2. 从配置文件获取
	if global.GVA_CONFIG.QwenConfig.APIKey != "" {
		return global.GVA_CONFIG.QwenConfig.APIKey
	}

	// 3. 返回空字符串，让调用方处理错误
	return ""
}

// GetBaseURL 获取Qwen基础URL
func GetBaseURL() string {
	if baseURL := os.Getenv("QWEN_BASE_URL"); baseURL != "" {
		return baseURL
	}

	if global.GVA_CONFIG.QwenConfig.BaseURL != "" {
		return global.GVA_CONFIG.QwenConfig.BaseURL
	}

	// 默认值
	return "https://dashscope.aliyuncs.com/compatible-mode/v1/"
}

// GetDefaultModel 获取默认模型
func GetDefaultModel() string {
	if global.GVA_CONFIG.QwenConfig.DefaultModel != "" {
		return global.GVA_CONFIG.QwenConfig.DefaultModel
	}

	// 默认值
	return "qwen-plus"
}

// GetASRConfig 获取ASR配置
func GetASRConfig() ASRConfig {
	config := global.GVA_CONFIG.QwenConfig.ASR

	// 设置默认值
	if config.DefaultModel == "" {
		config.DefaultModel = "paraformer-realtime-v2"
	}
	if config.TranslationModel == "" {
		config.TranslationModel = "gummy-realtime-v1"
	}
	if config.DefaultFormat == "" {
		config.DefaultFormat = "mp3"
	}
	if config.DefaultSourceLanguage == "" {
		config.DefaultSourceLanguage = "zh"
	}
	if len(config.DefaultTargetLanguages) == 0 {
		config.DefaultTargetLanguages = []string{"en"}
	}
	if config.ConnectionTimeout == 0 {
		config.ConnectionTimeout = 30
	}
	if config.MaxReconnectAttempts == 0 {
		config.MaxReconnectAttempts = 3
	}

	return ASRConfig{
		DefaultModel:           config.DefaultModel,
		TranslationModel:       config.TranslationModel,
		DefaultFormat:          config.DefaultFormat,
		DefaultSourceLanguage:  config.DefaultSourceLanguage,
		DefaultTargetLanguages: config.DefaultTargetLanguages,
		ConnectionTimeout:      config.ConnectionTimeout,
		MaxReconnectAttempts:   config.MaxReconnectAttempts,
	}
}

// GetTranslationConfig 获取翻译配置
func GetTranslationConfig() TranslationConfig {
	config := global.GVA_CONFIG.QwenConfig.Translation

	// 设置默认值
	if config.DefaultModel == "" {
		config.DefaultModel = "qwen-plus"
	}
	if config.DefaultSourceLanguage == "" {
		config.DefaultSourceLanguage = "zh"
	}
	if config.DefaultTargetLanguage == "" {
		config.DefaultTargetLanguage = "en"
	}
	if config.RequestTimeout == 0 {
		config.RequestTimeout = 30
	}

	return TranslationConfig{
		DefaultModel:          config.DefaultModel,
		DefaultSourceLanguage: config.DefaultSourceLanguage,
		DefaultTargetLanguage: config.DefaultTargetLanguage,
		RequestTimeout:        config.RequestTimeout,
	}
}

// GetTTSConfig 获取TTS配置
func GetTTSConfig() TTSConfig {
	config := global.GVA_CONFIG.QwenConfig.TTS

	// 设置默认值
	if config.DefaultModel == "" {
		config.DefaultModel = "sambert-zhichu-v1"
	}
	if config.DefaultVoice == "" {
		config.DefaultVoice = "zhichu"
	}
	if config.DefaultSpeechRate == 0 {
		config.DefaultSpeechRate = 1.0
	}
	if config.DefaultFormat == "" {
		config.DefaultFormat = "mp3"
	}
	if config.RequestTimeout == 0 {
		config.RequestTimeout = 30
	}

	return TTSConfig{
		DefaultModel:      config.DefaultModel,
		DefaultVoice:      config.DefaultVoice,
		DefaultSpeechRate: config.DefaultSpeechRate,
		DefaultFormat:     config.DefaultFormat,
		RequestTimeout:    config.RequestTimeout,
	}
}

// 配置结构体定义
type ASRConfig struct {
	DefaultModel           string
	TranslationModel       string
	DefaultFormat          string
	DefaultSourceLanguage  string
	DefaultTargetLanguages []string
	ConnectionTimeout      int
	MaxReconnectAttempts   int
}

type TranslationConfig struct {
	DefaultModel          string
	DefaultSourceLanguage string
	DefaultTargetLanguage string
	RequestTimeout        int
}

type TTSConfig struct {
	DefaultModel      string
	DefaultVoice      string
	DefaultSpeechRate float64
	DefaultFormat     string
	RequestTimeout    int
}

// ValidateConfig 验证配置是否有效
func ValidateConfig() error {
	apiKey := GetAPIKey()
	if apiKey == "" {
		return ErrMissingAPIKey
	}

	baseURL := GetBaseURL()
	if baseURL == "" {
		return ErrMissingBaseURL
	}

	return nil
}

// 错误定义
var (
	ErrMissingAPIKey  = fmt.Errorf("Qwen API key is missing. Please set QWEN_API_KEY environment variable or configure it in config.yaml")
	ErrMissingBaseURL = fmt.Errorf("Qwen base URL is missing. Please set QWEN_BASE_URL environment variable or configure it in config.yaml")
)

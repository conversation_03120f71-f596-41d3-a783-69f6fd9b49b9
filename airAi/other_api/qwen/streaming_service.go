package qwen

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
)

// StreamingVoiceService 流式语音翻译服务接口
type StreamingVoiceService interface {
	// Start 启动流式服务
	Start(ctx context.Context) error

	// Stop 停止流式服务
	Stop() error

	// SendAudio 发送音频数据
	SendAudio(audioData []byte) error

	// GetASRResults 获取ASR结果通道
	GetASRResults() <-chan ASRResult

	// GetTranslationResults 获取翻译结果通道
	GetTranslationResults() <-chan TranslationResult

	// GetErrors 获取错误通道
	GetErrors() <-chan error

	// IsConnected 检查连接状态
	IsConnected() bool

	// GetConnectionInfo 获取连接信息
	GetConnectionInfo() ConnectionInfo
}

// StreamingServiceConfig 流式服务配置
type StreamingServiceConfig struct {
	// 基础配置
	ApiKey          string   `json:"api_key"`
	Model           string   `json:"model"`
	SourceLanguage  string   `json:"source_language"`
	TargetLanguages []string `json:"target_languages"`

	// 音频配置
	SampleRate  int    `json:"sample_rate"`
	AudioFormat string `json:"audio_format"`

	// 连接配置
	MaxReconnectAttempts int           `json:"max_reconnect_attempts"`
	ReconnectInterval    time.Duration `json:"reconnect_interval"`
	ConnectionTimeout    time.Duration `json:"connection_timeout"`

	// 缓冲区配置
	AudioBufferSize   int `json:"audio_buffer_size"`
	ResultChannelSize int `json:"result_channel_size"`

	// 功能开关
	EnableTranscription bool `json:"enable_transcription"`
	EnableTranslation   bool `json:"enable_translation"`
	EnableVAD           bool `json:"enable_vad"` // Voice Activity Detection
}

// ASRResult ASR识别结果
type ASRResult struct {
	Text        string    `json:"text"`
	IsPartial   bool      `json:"is_partial"`
	IsFinal     bool      `json:"is_final"`
	Confidence  float64   `json:"confidence,omitempty"`
	BeginTime   int64     `json:"begin_time"`
	EndTime     *int64    `json:"end_time,omitempty"`
	SentenceID  int       `json:"sentence_id,omitempty"`
	Words       []Word    `json:"words,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	SequenceNum int64     `json:"sequence_num"`
}

// TranslationResult 翻译结果
type TranslationResult struct {
	OriginalText   string    `json:"original_text"`
	TranslatedText string    `json:"translated_text"`
	SourceLanguage string    `json:"source_language"`
	TargetLanguage string    `json:"target_language"`
	IsPartial      bool      `json:"is_partial"`
	IsFinal        bool      `json:"is_final"`
	Confidence     float64   `json:"confidence,omitempty"`
	BeginTime      int64     `json:"begin_time"`
	EndTime        int64     `json:"end_time"`
	SentenceID     int       `json:"sentence_id,omitempty"`
	Timestamp      time.Time `json:"timestamp"`
	SequenceNum    int64     `json:"sequence_num"`
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	IsConnected     bool      `json:"is_connected"`
	ConnectedAt     time.Time `json:"connected_at,omitempty"`
	LastActivity    time.Time `json:"last_activity,omitempty"`
	ReconnectCount  int       `json:"reconnect_count"`
	TaskID          string    `json:"task_id,omitempty"`
	ConnectionState string    `json:"connection_state"`
	LastError       string    `json:"last_error,omitempty"`
}

// StreamingVoiceServiceImpl 流式语音翻译服务实现
type StreamingVoiceServiceImpl struct {
	config *StreamingServiceConfig

	// WebSocket连接
	conn    *websocket.Conn
	connMux sync.RWMutex

	// 任务管理
	taskID  string
	taskMux sync.RWMutex

	// 通道管理
	asrResults         chan ASRResult
	translationResults chan TranslationResult
	errors             chan error
	audioInput         chan []byte

	// 控制通道
	stopChan chan struct{}
	doneChan chan struct{}

	// 状态管理
	isRunning   bool
	isConnected bool
	stateMux    sync.RWMutex

	// 连接信息
	connectionInfo ConnectionInfo
	infoMux        sync.RWMutex

	// 序列号管理
	sequenceNum int64
	seqMux      sync.Mutex

	// 重连管理
	reconnectCount int
	lastError      error
}

// NewStreamingVoiceService 创建新的流式语音翻译服务
func NewStreamingVoiceService(config *StreamingServiceConfig) StreamingVoiceService {
	// 设置默认配置
	if config.MaxReconnectAttempts == 0 {
		config.MaxReconnectAttempts = 3
	}
	if config.ReconnectInterval == 0 {
		config.ReconnectInterval = 5 * time.Second
	}
	if config.ConnectionTimeout == 0 {
		config.ConnectionTimeout = 30 * time.Second
	}
	if config.AudioBufferSize == 0 {
		config.AudioBufferSize = 1024
	}
	if config.ResultChannelSize == 0 {
		config.ResultChannelSize = 100
	}
	if config.SampleRate == 0 {
		config.SampleRate = 16000
	}
	if config.AudioFormat == "" {
		config.AudioFormat = "pcm"
	}

	service := &StreamingVoiceServiceImpl{
		config: config,

		// 初始化通道
		asrResults:         make(chan ASRResult, config.ResultChannelSize),
		translationResults: make(chan TranslationResult, config.ResultChannelSize),
		errors:             make(chan error, 10),
		audioInput:         make(chan []byte, config.AudioBufferSize),

		// 初始化控制通道
		stopChan: make(chan struct{}),
		doneChan: make(chan struct{}),

		// 初始化连接信息
		connectionInfo: ConnectionInfo{
			ConnectionState: "disconnected",
		},
	}

	return service
}

// Start 启动流式服务
func (s *StreamingVoiceServiceImpl) Start(ctx context.Context) error {
	s.stateMux.Lock()
	if s.isRunning {
		s.stateMux.Unlock()
		return fmt.Errorf("service is already running")
	}
	s.isRunning = true
	s.stateMux.Unlock()

	// 建立WebSocket连接
	if err := s.connect(ctx); err != nil {
		s.stateMux.Lock()
		s.isRunning = false
		s.stateMux.Unlock()
		return fmt.Errorf("failed to connect: %w", err)
	}

	// 启动各种处理goroutine
	go s.messageReceiver(ctx)
	go s.audioSender(ctx)
	go s.connectionMonitor(ctx)

	return nil
}

// Stop 停止流式服务
func (s *StreamingVoiceServiceImpl) Stop() error {
	s.stateMux.Lock()
	if !s.isRunning {
		s.stateMux.Unlock()
		return nil
	}
	s.isRunning = false
	s.stateMux.Unlock()

	// 发送停止信号
	close(s.stopChan)

	// 等待所有goroutine结束
	select {
	case <-s.doneChan:
	case <-time.After(10 * time.Second):
		// 超时强制关闭
	}

	// 关闭WebSocket连接
	s.disconnect()

	// 关闭所有通道
	s.closeChannels()

	return nil
}

// SendAudio 发送音频数据
func (s *StreamingVoiceServiceImpl) SendAudio(audioData []byte) error {
	if !s.IsConnected() {
		return fmt.Errorf("service is not connected")
	}

	select {
	case s.audioInput <- audioData:
		return nil
	case <-time.After(1 * time.Second):
		return fmt.Errorf("audio input buffer is full")
	}
}

// GetASRResults 获取ASR结果通道
func (s *StreamingVoiceServiceImpl) GetASRResults() <-chan ASRResult {
	return s.asrResults
}

// GetTranslationResults 获取翻译结果通道
func (s *StreamingVoiceServiceImpl) GetTranslationResults() <-chan TranslationResult {
	return s.translationResults
}

// GetErrors 获取错误通道
func (s *StreamingVoiceServiceImpl) GetErrors() <-chan error {
	return s.errors
}

// IsConnected 检查连接状态
func (s *StreamingVoiceServiceImpl) IsConnected() bool {
	s.stateMux.RLock()
	defer s.stateMux.RUnlock()
	return s.isConnected
}

// GetConnectionInfo 获取连接信息
func (s *StreamingVoiceServiceImpl) GetConnectionInfo() ConnectionInfo {
	s.infoMux.RLock()
	defer s.infoMux.RUnlock()
	return s.connectionInfo
}

// connect 建立WebSocket连接
func (s *StreamingVoiceServiceImpl) connect(ctx context.Context) error {
	// 使用现有的连接逻辑
	conn, err := s.connectWebSocket(s.config.ApiKey)
	if err != nil {
		s.updateConnectionInfo("failed", err.Error())
		return err
	}

	s.connMux.Lock()
	s.conn = conn
	s.connMux.Unlock()

	s.stateMux.Lock()
	s.isConnected = true
	s.stateMux.Unlock()

	// 发送run-task指令
	taskID, err := s.sendRunTaskCommand()
	if err != nil {
		s.disconnect()
		return fmt.Errorf("failed to send run-task command: %w", err)
	}

	s.taskMux.Lock()
	s.taskID = taskID
	s.taskMux.Unlock()

	s.updateConnectionInfo("connected", "")

	return nil
}

// disconnect 断开WebSocket连接
func (s *StreamingVoiceServiceImpl) disconnect() {
	s.connMux.Lock()
	if s.conn != nil {
		// 发送finish-task指令
		if s.taskID != "" {
			s.sendFinishTaskCommand()
		}
		s.conn.Close()
		s.conn = nil
	}
	s.connMux.Unlock()

	s.stateMux.Lock()
	s.isConnected = false
	s.stateMux.Unlock()

	s.updateConnectionInfo("disconnected", "")
}

// updateConnectionInfo 更新连接信息
func (s *StreamingVoiceServiceImpl) updateConnectionInfo(state, errorMsg string) {
	s.infoMux.Lock()
	defer s.infoMux.Unlock()

	s.connectionInfo.ConnectionState = state
	s.connectionInfo.LastActivity = time.Now()

	if state == "connected" {
		s.connectionInfo.IsConnected = true
		s.connectionInfo.ConnectedAt = time.Now()
		s.connectionInfo.TaskID = s.taskID
	} else {
		s.connectionInfo.IsConnected = false
	}

	if errorMsg != "" {
		s.connectionInfo.LastError = errorMsg
	}
}

// messageReceiver 消息接收器
func (s *StreamingVoiceServiceImpl) messageReceiver(ctx context.Context) {
	defer func() {
		select {
		case s.doneChan <- struct{}{}:
		default:
		}
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		default:
		}

		s.connMux.RLock()
		conn := s.conn
		s.connMux.RUnlock()

		if conn == nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		// 设置读取超时
		conn.SetReadDeadline(time.Now().Add(30 * time.Second))

		_, message, err := conn.ReadMessage()
		if err != nil {
			select {
			case s.errors <- fmt.Errorf("websocket read error: %w", err):
			default:
			}

			// 如果是连接错误，尝试重连
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				s.handleConnectionError(ctx, err)
			}
			continue
		}

		// 处理接收到的消息
		s.processMessage(message)
		s.updateConnectionInfo("active", "")
	}
}

// audioSender 音频发送器
func (s *StreamingVoiceServiceImpl) audioSender(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case audioData := <-s.audioInput:
			if err := s.sendAudioData(audioData); err != nil {
				select {
				case s.errors <- fmt.Errorf("audio send error: %w", err):
				default:
				}
			}
		}
	}
}

// connectionMonitor 连接监控器
func (s *StreamingVoiceServiceImpl) connectionMonitor(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			if !s.IsConnected() {
				// 尝试重连
				s.attemptReconnect(ctx)
			}
		}
	}
}

// closeChannels 关闭所有通道
func (s *StreamingVoiceServiceImpl) closeChannels() {
	close(s.asrResults)
	close(s.translationResults)
	close(s.errors)
	close(s.audioInput)
}

// connectWebSocket 连接WebSocket服务
func (s *StreamingVoiceServiceImpl) connectWebSocket(apiKey string) (*websocket.Conn, error) {
	header := make(map[string][]string)
	header["X-DashScope-DataInspection"] = []string{"enable"}
	header["Authorization"] = []string{fmt.Sprintf("bearer %s", apiKey)}

	dialer := websocket.Dialer{
		HandshakeTimeout: s.config.ConnectionTimeout,
	}

	conn, _, err := dialer.Dial(wsURL, header)
	return conn, err
}

// sendRunTaskCommand 发送run-task指令
func (s *StreamingVoiceServiceImpl) sendRunTaskCommand() (string, error) {
	s.connMux.RLock()
	conn := s.conn
	s.connMux.RUnlock()

	if conn == nil {
		return "", fmt.Errorf("connection is nil")
	}

	// 构建参数
	params := AsrParams{
		Format:                     s.config.AudioFormat,
		SampleRate:                 s.config.SampleRate,
		SourceLanguage:             s.config.SourceLanguage,
		TranslationTargetLanguages: s.config.TargetLanguages,
		TranscriptionEnabled:       s.config.EnableTranscription,
		TranslationEnabled:         s.config.EnableTranslation,
		Rate:                       1.0,
	}

	// 生成任务ID
	taskID := generateTaskID()

	runTaskCmd := Event{
		Header: AsrHeader{
			Action:    "run-task",
			TaskID:    taskID,
			Streaming: "duplex",
		},
		Payload: Payload{
			TaskGroup:  "audio",
			Task:       "asr",
			Function:   "recognition",
			Model:      s.config.Model,
			Parameters: params,
			Input:      Input{},
		},
	}

	runTaskCmdJSON, err := json.Marshal(runTaskCmd)
	if err != nil {
		return "", err
	}

	err = conn.WriteMessage(websocket.TextMessage, runTaskCmdJSON)
	if err != nil {
		return "", err
	}

	return taskID, nil
}

// sendFinishTaskCommand 发送finish-task指令
func (s *StreamingVoiceServiceImpl) sendFinishTaskCommand() error {
	s.connMux.RLock()
	conn := s.conn
	s.connMux.RUnlock()

	if conn == nil {
		return fmt.Errorf("connection is nil")
	}

	s.taskMux.RLock()
	taskID := s.taskID
	s.taskMux.RUnlock()

	finishTaskCmd := Event{
		Header: AsrHeader{
			Action:    "finish-task",
			TaskID:    taskID,
			Streaming: "duplex",
		},
		Payload: Payload{
			Input: Input{},
		},
	}

	finishTaskCmdJSON, err := json.Marshal(finishTaskCmd)
	if err != nil {
		return err
	}

	return conn.WriteMessage(websocket.TextMessage, finishTaskCmdJSON)
}

// sendAudioData 发送音频数据
func (s *StreamingVoiceServiceImpl) sendAudioData(audioData []byte) error {
	s.connMux.RLock()
	conn := s.conn
	s.connMux.RUnlock()

	if conn == nil {
		return fmt.Errorf("connection is nil")
	}

	return conn.WriteMessage(websocket.BinaryMessage, audioData)
}

// processMessage 处理接收到的消息
func (s *StreamingVoiceServiceImpl) processMessage(message []byte) {
	var event Event
	if err := json.Unmarshal(message, &event); err != nil {
		select {
		case s.errors <- fmt.Errorf("failed to unmarshal message: %w", err):
		default:
		}
		return
	}

	// 处理不同类型的事件
	switch event.Header.Event {
	case "task-started":
		s.updateConnectionInfo("task-started", "")

	case "result-generated":
		s.processResultGenerated(&event)

	case "task-finished":
		s.updateConnectionInfo("task-finished", "")
		// 发送结束标记
		select {
		case s.asrResults <- ASRResult{
			Text:        "",
			IsPartial:   false,
			IsFinal:     true,
			Timestamp:   time.Now(),
			SequenceNum: s.getNextSequenceNum(),
		}:
		default:
		}

	case "task-failed":
		errorMsg := "Task failed"
		if event.Header.ErrorMessage != "" {
			errorMsg = event.Header.ErrorMessage
		}
		select {
		case s.errors <- fmt.Errorf("task failed: %s", errorMsg):
		default:
		}
	}
}

// processResultGenerated 处理result-generated事件
func (s *StreamingVoiceServiceImpl) processResultGenerated(event *Event) {
	if event.Payload.Output == nil {
		return
	}

	output := event.Payload.Output
	timestamp := time.Now()
	sequenceNum := s.getNextSequenceNum()

	// 处理ASR结果
	if output.Sentence.Text != "" {
		isPartial := output.Sentence.EndTime == nil

		asrResult := ASRResult{
			Text:        output.Sentence.Text,
			IsPartial:   isPartial,
			IsFinal:     !isPartial,
			BeginTime:   output.Sentence.BeginTime,
			EndTime:     output.Sentence.EndTime,
			Timestamp:   timestamp,
			SequenceNum: sequenceNum,
		}

		select {
		case s.asrResults <- asrResult:
		default:
		}
	}

	// 处理翻译结果
	for _, translation := range output.Translations {
		if translation.Text != "" {
			translationResult := TranslationResult{
				OriginalText:   output.Sentence.Text,
				TranslatedText: translation.Text,
				SourceLanguage: s.config.SourceLanguage,
				TargetLanguage: translation.Lang,
				IsPartial:      !translation.SentenceEnd,
				IsFinal:        translation.SentenceEnd,
				BeginTime:      translation.BeginTime,
				EndTime:        translation.EndTime,
				SentenceID:     translation.SentenceID,
				Timestamp:      timestamp,
				SequenceNum:    sequenceNum,
			}

			select {
			case s.translationResults <- translationResult:
			default:
			}
		}
	}
}

// getNextSequenceNum 获取下一个序列号
func (s *StreamingVoiceServiceImpl) getNextSequenceNum() int64 {
	s.seqMux.Lock()
	defer s.seqMux.Unlock()
	s.sequenceNum++
	return s.sequenceNum
}

// handleConnectionError 处理连接错误
func (s *StreamingVoiceServiceImpl) handleConnectionError(ctx context.Context, err error) {
	s.updateConnectionInfo("error", err.Error())

	// 断开当前连接
	s.disconnect()

	// 尝试重连
	go s.attemptReconnect(ctx)
}

// attemptReconnect 尝试重连
func (s *StreamingVoiceServiceImpl) attemptReconnect(ctx context.Context) {
	if s.reconnectCount >= s.config.MaxReconnectAttempts {
		select {
		case s.errors <- fmt.Errorf("max reconnect attempts reached"):
		default:
		}
		return
	}

	s.reconnectCount++
	s.updateConnectionInfo("reconnecting", "")

	// 等待重连间隔
	select {
	case <-time.After(s.config.ReconnectInterval):
	case <-ctx.Done():
		return
	case <-s.stopChan:
		return
	}

	// 尝试重新连接
	if err := s.connect(ctx); err != nil {
		select {
		case s.errors <- fmt.Errorf("reconnect failed: %w", err):
		default:
		}

		// 继续尝试重连
		go s.attemptReconnect(ctx)
	} else {
		// 重连成功，重置重连计数
		s.reconnectCount = 0
	}
}

// generateTaskID 生成任务ID
func generateTaskID() string {
	// 使用简单的时间戳生成ID，避免依赖uuid包
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

// ResultProcessor 结果处理器接口
type ResultProcessor interface {
	ProcessASRResult(result ASRResult) error
	ProcessTranslationResult(result TranslationResult) error
}

// DefaultResultProcessor 默认结果处理器
type DefaultResultProcessor struct {
	service *StreamingVoiceServiceImpl
}

// NewDefaultResultProcessor 创建默认结果处理器
func NewDefaultResultProcessor(service *StreamingVoiceServiceImpl) *DefaultResultProcessor {
	return &DefaultResultProcessor{service: service}
}

// ProcessASRResult 处理ASR结果
func (p *DefaultResultProcessor) ProcessASRResult(result ASRResult) error {
	// 可以在这里添加额外的处理逻辑，如日志记录、数据验证等
	select {
	case p.service.asrResults <- result:
		return nil
	default:
		return fmt.Errorf("ASR result channel is full")
	}
}

// ProcessTranslationResult 处理翻译结果
func (p *DefaultResultProcessor) ProcessTranslationResult(result TranslationResult) error {
	// 可以在这里添加额外的处理逻辑，如日志记录、数据验证等
	select {
	case p.service.translationResults <- result:
		return nil
	default:
		return fmt.Errorf("translation result channel is full")
	}
}

// EventHandler 事件处理器
type EventHandler struct {
	service   *StreamingVoiceServiceImpl
	processor ResultProcessor
}

// NewEventHandler 创建事件处理器
func NewEventHandler(service *StreamingVoiceServiceImpl, processor ResultProcessor) *EventHandler {
	return &EventHandler{
		service:   service,
		processor: processor,
	}
}

// HandleEvent 处理事件
func (h *EventHandler) HandleEvent(event *Event) error {
	switch event.Header.Event {
	case "task-started":
		return h.handleTaskStarted(event)
	case "result-generated":
		return h.handleResultGenerated(event)
	case "task-finished":
		return h.handleTaskFinished(event)
	case "task-failed":
		return h.handleTaskFailed(event)
	default:
		return fmt.Errorf("unknown event type: %s", event.Header.Event)
	}
}

// handleTaskStarted 处理任务开始事件
func (h *EventHandler) handleTaskStarted(event *Event) error {
	h.service.updateConnectionInfo("task-started", "")
	return nil
}

// handleResultGenerated 处理结果生成事件
func (h *EventHandler) handleResultGenerated(event *Event) error {
	if event.Payload.Output == nil {
		return nil
	}

	output := event.Payload.Output
	timestamp := time.Now()
	sequenceNum := h.service.getNextSequenceNum()

	// 处理ASR结果
	if err := h.processASRResults(output, timestamp, sequenceNum); err != nil {
		return fmt.Errorf("failed to process ASR results: %w", err)
	}

	// 处理翻译结果
	if err := h.processTranslationResults(output, timestamp, sequenceNum); err != nil {
		return fmt.Errorf("failed to process translation results: %w", err)
	}

	return nil
}

// processASRResults 处理ASR结果
func (h *EventHandler) processASRResults(output *Output, timestamp time.Time, sequenceNum int64) error {
	if output.Sentence.Text == "" {
		return nil
	}

	isPartial := output.Sentence.EndTime == nil

	// 转换Words类型
	var words []Word
	for _, w := range output.Sentence.Words {
		var endTime int64
		if w.EndTime != nil {
			endTime = *w.EndTime
		}
		words = append(words, Word{
			BeginTime:   w.BeginTime,
			EndTime:     endTime,
			Text:        w.Text,
			Punctuation: w.Punctuation,
		})
	}

	asrResult := ASRResult{
		Text:        output.Sentence.Text,
		IsPartial:   isPartial,
		IsFinal:     !isPartial,
		BeginTime:   output.Sentence.BeginTime,
		EndTime:     output.Sentence.EndTime,
		Words:       words,
		Timestamp:   timestamp,
		SequenceNum: sequenceNum,
	}

	return h.processor.ProcessASRResult(asrResult)
}

// processTranslationResults 处理翻译结果
func (h *EventHandler) processTranslationResults(output *Output, timestamp time.Time, sequenceNum int64) error {
	for _, translation := range output.Translations {
		if translation.Text == "" {
			continue
		}

		translationResult := TranslationResult{
			OriginalText:   output.Sentence.Text,
			TranslatedText: translation.Text,
			SourceLanguage: h.service.config.SourceLanguage,
			TargetLanguage: translation.Lang,
			IsPartial:      !translation.SentenceEnd,
			IsFinal:        translation.SentenceEnd,
			BeginTime:      translation.BeginTime,
			EndTime:        translation.EndTime,
			SentenceID:     translation.SentenceID,
			Timestamp:      timestamp,
			SequenceNum:    sequenceNum,
		}

		if err := h.processor.ProcessTranslationResult(translationResult); err != nil {
			return err
		}
	}

	return nil
}

// handleTaskFinished 处理任务完成事件
func (h *EventHandler) handleTaskFinished(event *Event) error {
	h.service.updateConnectionInfo("task-finished", "")

	// 发送结束标记
	endResult := ASRResult{
		Text:        "",
		IsPartial:   false,
		IsFinal:     true,
		Timestamp:   time.Now(),
		SequenceNum: h.service.getNextSequenceNum(),
	}

	return h.processor.ProcessASRResult(endResult)
}

// handleTaskFailed 处理任务失败事件
func (h *EventHandler) handleTaskFailed(event *Event) error {
	errorMsg := "Task failed"
	if event.Header.ErrorMessage != "" {
		errorMsg = event.Header.ErrorMessage
	}

	h.service.updateConnectionInfo("task-failed", errorMsg)

	select {
	case h.service.errors <- fmt.Errorf("task failed: %s", errorMsg):
	default:
	}

	return fmt.Errorf("task failed: %s", errorMsg)
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	service           *StreamingVoiceServiceImpl
	healthCheckTicker *time.Ticker
	reconnectTimer    *time.Timer
	shutdownChan      chan struct{}
	isShuttingDown    bool
	shutdownMux       sync.RWMutex
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(service *StreamingVoiceServiceImpl) *ConnectionManager {
	return &ConnectionManager{
		service:      service,
		shutdownChan: make(chan struct{}),
	}
}

// Start 启动连接管理器
func (cm *ConnectionManager) Start(ctx context.Context) {
	// 启动健康检查
	cm.startHealthCheck(ctx)

	// 启动连接监控
	go cm.monitorConnection(ctx)
}

// Stop 停止连接管理器
func (cm *ConnectionManager) Stop() {
	cm.shutdownMux.Lock()
	if cm.isShuttingDown {
		cm.shutdownMux.Unlock()
		return
	}
	cm.isShuttingDown = true
	cm.shutdownMux.Unlock()

	// 停止健康检查
	if cm.healthCheckTicker != nil {
		cm.healthCheckTicker.Stop()
	}

	// 停止重连定时器
	if cm.reconnectTimer != nil {
		cm.reconnectTimer.Stop()
	}

	// 发送关闭信号
	close(cm.shutdownChan)
}

// startHealthCheck 启动健康检查
func (cm *ConnectionManager) startHealthCheck(ctx context.Context) {
	cm.healthCheckTicker = time.NewTicker(30 * time.Second)

	go func() {
		defer cm.healthCheckTicker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-cm.shutdownChan:
				return
			case <-cm.healthCheckTicker.C:
				cm.performHealthCheck()
			}
		}
	}()
}

// performHealthCheck 执行健康检查
func (cm *ConnectionManager) performHealthCheck() {
	cm.shutdownMux.RLock()
	if cm.isShuttingDown {
		cm.shutdownMux.RUnlock()
		return
	}
	cm.shutdownMux.RUnlock()

	if !cm.service.IsConnected() {
		// 连接已断开，尝试重连
		go cm.scheduleReconnect()
		return
	}

	// 检查连接活跃度
	info := cm.service.GetConnectionInfo()
	if time.Since(info.LastActivity) > 60*time.Second {
		// 连接可能已经僵死，尝试重连
		cm.service.updateConnectionInfo("stale", "Connection appears to be stale")
		go cm.scheduleReconnect()
	}
}

// monitorConnection 监控连接状态
func (cm *ConnectionManager) monitorConnection(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-cm.shutdownChan:
			return
		case err := <-cm.service.GetErrors():
			if err != nil {
				// 处理错误，可能需要重连
				cm.handleConnectionError(err)
			}
		}
	}
}

// handleConnectionError 处理连接错误
func (cm *ConnectionManager) handleConnectionError(err error) {
	cm.shutdownMux.RLock()
	if cm.isShuttingDown {
		cm.shutdownMux.RUnlock()
		return
	}
	cm.shutdownMux.RUnlock()

	// 根据错误类型决定是否重连
	if cm.shouldReconnect(err) {
		go cm.scheduleReconnect()
	}
}

// shouldReconnect 判断是否应该重连
func (cm *ConnectionManager) shouldReconnect(err error) bool {
	// 检查是否是网络相关错误
	errorStr := err.Error()
	networkErrors := []string{
		"connection reset",
		"connection refused",
		"network is unreachable",
		"timeout",
		"websocket: close",
	}

	for _, netErr := range networkErrors {
		if strings.Contains(strings.ToLower(errorStr), netErr) {
			return true
		}
	}

	return false
}

// scheduleReconnect 安排重连
func (cm *ConnectionManager) scheduleReconnect() {
	cm.shutdownMux.RLock()
	if cm.isShuttingDown {
		cm.shutdownMux.RUnlock()
		return
	}
	cm.shutdownMux.RUnlock()

	// 如果已经在重连中，则跳过
	if cm.reconnectTimer != nil {
		return
	}

	// 设置重连延迟
	delay := cm.calculateReconnectDelay()
	cm.reconnectTimer = time.AfterFunc(delay, func() {
		cm.reconnectTimer = nil
		cm.attemptReconnect()
	})
}

// calculateReconnectDelay 计算重连延迟
func (cm *ConnectionManager) calculateReconnectDelay() time.Duration {
	info := cm.service.GetConnectionInfo()

	// 指数退避算法
	baseDelay := cm.service.config.ReconnectInterval
	maxDelay := 5 * time.Minute

	multiplier := 1 << uint(info.ReconnectCount)
	delay := time.Duration(float64(baseDelay) * float64(multiplier))
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}

// attemptReconnect 尝试重连
func (cm *ConnectionManager) attemptReconnect() {
	cm.shutdownMux.RLock()
	if cm.isShuttingDown {
		cm.shutdownMux.RUnlock()
		return
	}
	cm.shutdownMux.RUnlock()

	info := cm.service.GetConnectionInfo()
	if info.ReconnectCount >= cm.service.config.MaxReconnectAttempts {
		// 达到最大重连次数
		cm.service.updateConnectionInfo("failed", "Max reconnect attempts reached")
		return
	}

	// 断开当前连接
	cm.service.disconnect()

	// 尝试重新连接
	ctx, cancel := context.WithTimeout(context.Background(), cm.service.config.ConnectionTimeout)
	defer cancel()

	if err := cm.service.connect(ctx); err != nil {
		// 重连失败，更新重连计数并安排下次重连
		cm.service.infoMux.Lock()
		cm.service.connectionInfo.ReconnectCount++
		cm.service.infoMux.Unlock()

		cm.service.updateConnectionInfo("reconnect-failed", err.Error())
		go cm.scheduleReconnect()
	} else {
		// 重连成功，重置重连计数
		cm.service.infoMux.Lock()
		cm.service.connectionInfo.ReconnectCount = 0
		cm.service.infoMux.Unlock()

		cm.service.updateConnectionInfo("reconnected", "")
	}
}

// GracefulShutdown 优雅关闭
func (cm *ConnectionManager) GracefulShutdown(timeout time.Duration) error {
	cm.shutdownMux.Lock()
	if cm.isShuttingDown {
		cm.shutdownMux.Unlock()
		return nil
	}
	cm.isShuttingDown = true
	cm.shutdownMux.Unlock()

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 停止接收新的音频数据
	cm.service.stateMux.Lock()
	cm.service.isRunning = false
	cm.service.stateMux.Unlock()

	// 发送finish-task指令
	if cm.service.IsConnected() {
		cm.service.sendFinishTaskCommand()
	}

	// 等待处理完成或超时
	done := make(chan struct{})
	go func() {
		// 等待所有goroutine完成
		select {
		case <-cm.service.doneChan:
		case <-time.After(5 * time.Second):
		}
		close(done)
	}()

	select {
	case <-done:
		// 正常完成
	case <-ctx.Done():
		// 超时
		return fmt.Errorf("graceful shutdown timeout")
	}

	// 关闭连接
	cm.service.disconnect()

	// 关闭通道
	cm.service.closeChannels()

	// 停止连接管理器
	cm.Stop()

	return nil
}

// ConfigBuilder 配置构建器
type ConfigBuilder struct {
	config *StreamingServiceConfig
}

// NewConfigBuilder 创建配置构建器
func NewConfigBuilder() *ConfigBuilder {
	return &ConfigBuilder{
		config: &StreamingServiceConfig{
			// 默认配置
			Model:                "paraformer-realtime-v1",
			SampleRate:           16000,
			AudioFormat:          "pcm",
			MaxReconnectAttempts: 3,
			ReconnectInterval:    5 * time.Second,
			ConnectionTimeout:    30 * time.Second,
			AudioBufferSize:      1024,
			ResultChannelSize:    100,
			EnableTranscription:  true,
			EnableTranslation:    true,
			EnableVAD:            false,
		},
	}
}

// WithApiKey 设置API密钥
func (cb *ConfigBuilder) WithApiKey(apiKey string) *ConfigBuilder {
	cb.config.ApiKey = apiKey
	return cb
}

// WithModel 设置模型
func (cb *ConfigBuilder) WithModel(model string) *ConfigBuilder {
	cb.config.Model = model
	return cb
}

// WithSourceLanguage 设置源语言
func (cb *ConfigBuilder) WithSourceLanguage(language string) *ConfigBuilder {
	cb.config.SourceLanguage = language
	return cb
}

// WithTargetLanguages 设置目标语言
func (cb *ConfigBuilder) WithTargetLanguages(languages ...string) *ConfigBuilder {
	cb.config.TargetLanguages = languages
	return cb
}

// WithAudioConfig 设置音频配置
func (cb *ConfigBuilder) WithAudioConfig(sampleRate int, format string) *ConfigBuilder {
	cb.config.SampleRate = sampleRate
	cb.config.AudioFormat = format
	return cb
}

// WithConnectionConfig 设置连接配置
func (cb *ConfigBuilder) WithConnectionConfig(maxRetries int, retryInterval, timeout time.Duration) *ConfigBuilder {
	cb.config.MaxReconnectAttempts = maxRetries
	cb.config.ReconnectInterval = retryInterval
	cb.config.ConnectionTimeout = timeout
	return cb
}

// WithBufferConfig 设置缓冲区配置
func (cb *ConfigBuilder) WithBufferConfig(audioBufferSize, resultChannelSize int) *ConfigBuilder {
	cb.config.AudioBufferSize = audioBufferSize
	cb.config.ResultChannelSize = resultChannelSize
	return cb
}

// WithFeatures 设置功能开关
func (cb *ConfigBuilder) WithFeatures(transcription, translation, vad bool) *ConfigBuilder {
	cb.config.EnableTranscription = transcription
	cb.config.EnableTranslation = translation
	cb.config.EnableVAD = vad
	return cb
}

// Build 构建配置
func (cb *ConfigBuilder) Build() (*StreamingServiceConfig, error) {
	// 验证必需的配置
	if cb.config.ApiKey == "" {
		return nil, fmt.Errorf("API key is required")
	}
	if cb.config.SourceLanguage == "" {
		return nil, fmt.Errorf("source language is required")
	}
	if len(cb.config.TargetLanguages) == 0 && cb.config.EnableTranslation {
		return nil, fmt.Errorf("target languages are required when translation is enabled")
	}

	// 验证音频配置
	if cb.config.SampleRate <= 0 {
		return nil, fmt.Errorf("sample rate must be positive")
	}
	if cb.config.AudioFormat == "" {
		return nil, fmt.Errorf("audio format is required")
	}

	// 验证连接配置
	if cb.config.MaxReconnectAttempts < 0 {
		return nil, fmt.Errorf("max reconnect attempts must be non-negative")
	}
	if cb.config.ReconnectInterval <= 0 {
		return nil, fmt.Errorf("reconnect interval must be positive")
	}
	if cb.config.ConnectionTimeout <= 0 {
		return nil, fmt.Errorf("connection timeout must be positive")
	}

	// 验证缓冲区配置
	if cb.config.AudioBufferSize <= 0 {
		return nil, fmt.Errorf("audio buffer size must be positive")
	}
	if cb.config.ResultChannelSize <= 0 {
		return nil, fmt.Errorf("result channel size must be positive")
	}

	// 返回配置副本
	configCopy := *cb.config
	return &configCopy, nil
}

// ServiceFactory 服务工厂
type ServiceFactory struct{}

// NewServiceFactory 创建服务工厂
func NewServiceFactory() *ServiceFactory {
	return &ServiceFactory{}
}

// CreateService 创建流式语音翻译服务
func (sf *ServiceFactory) CreateService(config *StreamingServiceConfig) (StreamingVoiceService, error) {
	if config == nil {
		return nil, fmt.Errorf("config is required")
	}

	return NewStreamingVoiceService(config), nil
}

// CreateServiceWithDefaults 使用默认配置创建服务
func (sf *ServiceFactory) CreateServiceWithDefaults(apiKey, sourceLanguage string, targetLanguages ...string) (StreamingVoiceService, error) {
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		Build()
	if err != nil {
		return nil, err
	}

	return sf.CreateService(config)
}

// CreateASROnlyService 创建仅ASR的服务
func (sf *ServiceFactory) CreateASROnlyService(apiKey, sourceLanguage string) (StreamingVoiceService, error) {
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithFeatures(true, false, false). // 仅启用转录
		Build()
	if err != nil {
		return nil, err
	}

	return sf.CreateService(config)
}

// CreateTranslationService 创建翻译服务
func (sf *ServiceFactory) CreateTranslationService(apiKey, sourceLanguage string, targetLanguages ...string) (StreamingVoiceService, error) {
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithFeatures(true, true, false). // 启用转录和翻译
		Build()
	if err != nil {
		return nil, err
	}

	return sf.CreateService(config)
}

// CreateHighPerformanceService 创建高性能服务
func (sf *ServiceFactory) CreateHighPerformanceService(apiKey, sourceLanguage string, targetLanguages ...string) (StreamingVoiceService, error) {
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithBufferConfig(2048, 200).                            // 更大的缓冲区
		WithConnectionConfig(5, 3*time.Second, 60*time.Second). // 更积极的重连策略
		Build()
	if err != nil {
		return nil, err
	}

	return sf.CreateService(config)
}

// CreateLowLatencyService 创建低延迟服务
func (sf *ServiceFactory) CreateLowLatencyService(apiKey, sourceLanguage string, targetLanguages ...string) (StreamingVoiceService, error) {
	config, err := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithBufferConfig(512, 50).                              // 更小的缓冲区以减少延迟
		WithConnectionConfig(3, 1*time.Second, 10*time.Second). // 快速重连
		Build()
	if err != nil {
		return nil, err
	}

	return sf.CreateService(config)
}

// ConfigPresets 配置预设
type ConfigPresets struct{}

// NewConfigPresets 创建配置预设
func NewConfigPresets() *ConfigPresets {
	return &ConfigPresets{}
}

// DefaultConfig 默认配置
func (cp *ConfigPresets) DefaultConfig(apiKey, sourceLanguage string, targetLanguages ...string) *StreamingServiceConfig {
	config, _ := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		Build()
	return config
}

// HighQualityConfig 高质量配置
func (cp *ConfigPresets) HighQualityConfig(apiKey, sourceLanguage string, targetLanguages ...string) *StreamingServiceConfig {
	config, _ := NewConfigBuilder().
		WithApiKey(apiKey).
		WithModel("paraformer-realtime-v2"). // 使用更高质量的模型
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithAudioConfig(44100, "pcm"). // 更高的采样率
		WithBufferConfig(4096, 300).   // 更大的缓冲区
		WithConnectionConfig(5, 2*time.Second, 45*time.Second).
		Build()
	return config
}

// LowLatencyConfig 低延迟配置
func (cp *ConfigPresets) LowLatencyConfig(apiKey, sourceLanguage string, targetLanguages ...string) *StreamingServiceConfig {
	config, _ := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithAudioConfig(16000, "pcm").
		WithBufferConfig(256, 25).                                    // 最小缓冲区
		WithConnectionConfig(3, 500*time.Millisecond, 5*time.Second). // 快速重连
		Build()
	return config
}

// MobileConfig 移动设备配置
func (cp *ConfigPresets) MobileConfig(apiKey, sourceLanguage string, targetLanguages ...string) *StreamingServiceConfig {
	config, _ := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithAudioConfig(16000, "pcm").
		WithBufferConfig(512, 50).                              // 适中的缓冲区
		WithConnectionConfig(5, 3*time.Second, 30*time.Second). // 考虑网络不稳定
		WithFeatures(true, true, true).                         // 启用VAD以节省带宽
		Build()
	return config
}

// ServerConfig 服务器配置
func (cp *ConfigPresets) ServerConfig(apiKey, sourceLanguage string, targetLanguages ...string) *StreamingServiceConfig {
	config, _ := NewConfigBuilder().
		WithApiKey(apiKey).
		WithSourceLanguage(sourceLanguage).
		WithTargetLanguages(targetLanguages...).
		WithAudioConfig(16000, "pcm").
		WithBufferConfig(2048, 500).                             // 大缓冲区处理高并发
		WithConnectionConfig(10, 1*time.Second, 60*time.Second). // 更多重试次数
		Build()
	return config
}

// ConfigValidator 配置验证器
type ConfigValidator struct{}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{}
}

// ValidateConfig 验证配置
func (cv *ConfigValidator) ValidateConfig(config *StreamingServiceConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 验证API密钥
	if config.ApiKey == "" {
		return fmt.Errorf("API key is required")
	}

	// 验证语言设置
	if config.SourceLanguage == "" {
		return fmt.Errorf("source language is required")
	}

	if config.EnableTranslation && len(config.TargetLanguages) == 0 {
		return fmt.Errorf("target languages are required when translation is enabled")
	}

	// 验证模型
	validModels := []string{
		"paraformer-realtime-v1",
		"paraformer-realtime-v2",
		"paraformer-8k-v1",
		"paraformer-16k-v1",
	}

	isValidModel := false
	for _, validModel := range validModels {
		if config.Model == validModel {
			isValidModel = true
			break
		}
	}
	if !isValidModel {
		return fmt.Errorf("invalid model: %s", config.Model)
	}

	// 验证音频格式
	validFormats := []string{"pcm", "wav", "mp3", "aac"}
	isValidFormat := false
	for _, validFormat := range validFormats {
		if config.AudioFormat == validFormat {
			isValidFormat = true
			break
		}
	}
	if !isValidFormat {
		return fmt.Errorf("invalid audio format: %s", config.AudioFormat)
	}

	// 验证采样率
	validSampleRates := []int{8000, 16000, 22050, 44100, 48000}
	isValidSampleRate := false
	for _, validRate := range validSampleRates {
		if config.SampleRate == validRate {
			isValidSampleRate = true
			break
		}
	}
	if !isValidSampleRate {
		return fmt.Errorf("invalid sample rate: %d", config.SampleRate)
	}

	// 验证缓冲区大小
	if config.AudioBufferSize < 256 || config.AudioBufferSize > 8192 {
		return fmt.Errorf("audio buffer size must be between 256 and 8192")
	}

	if config.ResultChannelSize < 10 || config.ResultChannelSize > 1000 {
		return fmt.Errorf("result channel size must be between 10 and 1000")
	}

	// 验证连接配置
	if config.MaxReconnectAttempts < 0 || config.MaxReconnectAttempts > 20 {
		return fmt.Errorf("max reconnect attempts must be between 0 and 20")
	}

	if config.ReconnectInterval < 100*time.Millisecond || config.ReconnectInterval > 60*time.Second {
		return fmt.Errorf("reconnect interval must be between 100ms and 60s")
	}

	if config.ConnectionTimeout < 5*time.Second || config.ConnectionTimeout > 300*time.Second {
		return fmt.Errorf("connection timeout must be between 5s and 300s")
	}

	return nil
}

// ValidateLanguage 验证语言代码
func (cv *ConfigValidator) ValidateLanguage(language string) error {
	supportedLanguages := map[string]string{
		"zh": "Chinese",
		"en": "English",
		"ja": "Japanese",
		"ko": "Korean",
		"es": "Spanish",
		"fr": "French",
		"de": "German",
		"it": "Italian",
		"pt": "Portuguese",
		"ru": "Russian",
		"ar": "Arabic",
		"hi": "Hindi",
		"th": "Thai",
		"vi": "Vietnamese",
		"id": "Indonesian",
		"ms": "Malay",
		"tr": "Turkish",
		"nl": "Dutch",
		"sv": "Swedish",
		"da": "Danish",
		"no": "Norwegian",
		"fi": "Finnish",
		"pl": "Polish",
		"cs": "Czech",
		"hu": "Hungarian",
		"ro": "Romanian",
		"bg": "Bulgarian",
		"hr": "Croatian",
		"sk": "Slovak",
		"sl": "Slovenian",
		"et": "Estonian",
		"lv": "Latvian",
		"lt": "Lithuanian",
		"mt": "Maltese",
		"ga": "Irish",
		"cy": "Welsh",
		"eu": "Basque",
		"ca": "Catalan",
		"gl": "Galician",
	}

	if _, exists := supportedLanguages[language]; !exists {
		return fmt.Errorf("unsupported language: %s", language)
	}

	return nil
}

// ServiceManager 服务管理器
type ServiceManager struct {
	services map[string]StreamingVoiceService
	factory  *ServiceFactory
	mutex    sync.RWMutex
}

// NewServiceManager 创建服务管理器
func NewServiceManager() *ServiceManager {
	return &ServiceManager{
		services: make(map[string]StreamingVoiceService),
		factory:  NewServiceFactory(),
	}
}

// CreateAndRegisterService 创建并注册服务
func (sm *ServiceManager) CreateAndRegisterService(name string, config *StreamingServiceConfig) (StreamingVoiceService, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if _, exists := sm.services[name]; exists {
		return nil, fmt.Errorf("service with name %s already exists", name)
	}

	service, err := sm.factory.CreateService(config)
	if err != nil {
		return nil, err
	}

	sm.services[name] = service
	return service, nil
}

// GetService 获取服务
func (sm *ServiceManager) GetService(name string) (StreamingVoiceService, bool) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	service, exists := sm.services[name]
	return service, exists
}

// RemoveService 移除服务
func (sm *ServiceManager) RemoveService(name string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	service, exists := sm.services[name]
	if !exists {
		return fmt.Errorf("service with name %s not found", name)
	}

	// 停止服务
	if err := service.Stop(); err != nil {
		return fmt.Errorf("failed to stop service: %w", err)
	}

	delete(sm.services, name)
	return nil
}

// ListServices 列出所有服务
func (sm *ServiceManager) ListServices() []string {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	names := make([]string, 0, len(sm.services))
	for name := range sm.services {
		names = append(names, name)
	}

	return names
}

// StopAllServices 停止所有服务
func (sm *ServiceManager) StopAllServices() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	var errors []string
	for name, service := range sm.services {
		if err := service.Stop(); err != nil {
			errors = append(errors, fmt.Sprintf("failed to stop service %s: %v", name, err))
		}
	}

	// 清空服务列表
	sm.services = make(map[string]StreamingVoiceService)

	if len(errors) > 0 {
		return fmt.Errorf("errors stopping services: %s", strings.Join(errors, "; "))
	}

	return nil
}

// AudioProcessor 音频处理器接口
type AudioProcessor interface {
	ProcessChunk(chunk []byte) ([]byte, error)
	Reset() error
	GetSampleRate() int
	GetChannels() int
}

// DefaultAudioProcessor 默认音频处理器
type DefaultAudioProcessor struct {
	sampleRate int
	channels   int
	format     string
}

// NewDefaultAudioProcessor 创建默认音频处理器
func NewDefaultAudioProcessor(sampleRate, channels int, format string) *DefaultAudioProcessor {
	return &DefaultAudioProcessor{
		sampleRate: sampleRate,
		channels:   channels,
		format:     format,
	}
}

// ProcessChunk 处理音频块
func (p *DefaultAudioProcessor) ProcessChunk(chunk []byte) ([]byte, error) {
	// 默认实现：直接返回原始数据
	// 可以在这里添加音频预处理逻辑，如降噪、音量调整等
	return chunk, nil
}

// Reset 重置处理器
func (p *DefaultAudioProcessor) Reset() error {
	// 重置处理器状态
	return nil
}

// GetSampleRate 获取采样率
func (p *DefaultAudioProcessor) GetSampleRate() int {
	return p.sampleRate
}

// GetChannels 获取声道数
func (p *DefaultAudioProcessor) GetChannels() int {
	return p.channels
}

// AudioStreamer 音频流处理器
type AudioStreamer struct {
	service         *StreamingVoiceServiceImpl
	processor       AudioProcessor
	chunkSize       int
	processingQueue chan []byte
	stopChan        chan struct{}
	isRunning       bool
	mutex           sync.RWMutex

	// 统计信息
	totalChunks   int64
	totalBytes    int64
	droppedChunks int64
	lastChunkTime time.Time
	avgChunkSize  float64
}

// NewAudioStreamer 创建音频流处理器
func NewAudioStreamer(service *StreamingVoiceServiceImpl, processor AudioProcessor, chunkSize int) *AudioStreamer {
	return &AudioStreamer{
		service:         service,
		processor:       processor,
		chunkSize:       chunkSize,
		processingQueue: make(chan []byte, service.config.AudioBufferSize),
		stopChan:        make(chan struct{}),
	}
}

// Start 启动音频流处理器
func (as *AudioStreamer) Start(ctx context.Context) error {
	as.mutex.Lock()
	if as.isRunning {
		as.mutex.Unlock()
		return fmt.Errorf("audio streamer is already running")
	}
	as.isRunning = true
	as.mutex.Unlock()

	// 启动音频处理goroutine
	go as.processAudioLoop(ctx)

	return nil
}

// Stop 停止音频流处理器
func (as *AudioStreamer) Stop() error {
	as.mutex.Lock()
	if !as.isRunning {
		as.mutex.Unlock()
		return nil
	}
	as.isRunning = false
	as.mutex.Unlock()

	close(as.stopChan)
	return nil
}

// StreamAudioChunk 流式处理音频块
func (as *AudioStreamer) StreamAudioChunk(chunk []byte) error {
	as.mutex.RLock()
	if !as.isRunning {
		as.mutex.RUnlock()
		return fmt.Errorf("audio streamer is not running")
	}
	as.mutex.RUnlock()

	// 更新统计信息
	as.updateStats(chunk)

	select {
	case as.processingQueue <- chunk:
		return nil
	default:
		// 队列已满，丢弃音频块
		atomic.AddInt64(&as.droppedChunks, 1)
		return fmt.Errorf("audio processing queue is full, chunk dropped")
	}
}

// processAudioLoop 音频处理循环
func (as *AudioStreamer) processAudioLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-as.stopChan:
			return
		case chunk := <-as.processingQueue:
			if err := as.processChunk(chunk); err != nil {
				// 发送错误到服务的错误通道
				select {
				case as.service.errors <- fmt.Errorf("audio processing error: %w", err):
				default:
				}
			}
		}
	}
}

// processChunk 处理单个音频块
func (as *AudioStreamer) processChunk(chunk []byte) error {
	// 使用音频处理器处理音频块
	processedChunk, err := as.processor.ProcessChunk(chunk)
	if err != nil {
		return fmt.Errorf("failed to process audio chunk: %w", err)
	}

	// 将处理后的音频块发送到服务
	return as.service.SendAudio(processedChunk)
}

// updateStats 更新统计信息
func (as *AudioStreamer) updateStats(chunk []byte) {
	atomic.AddInt64(&as.totalChunks, 1)
	atomic.AddInt64(&as.totalBytes, int64(len(chunk)))

	as.lastChunkTime = time.Now()

	// 计算平均块大小
	totalChunks := atomic.LoadInt64(&as.totalChunks)
	totalBytes := atomic.LoadInt64(&as.totalBytes)
	as.avgChunkSize = float64(totalBytes) / float64(totalChunks)
}

// GetStats 获取统计信息
func (as *AudioStreamer) GetStats() AudioStreamStats {
	return AudioStreamStats{
		TotalChunks:   atomic.LoadInt64(&as.totalChunks),
		TotalBytes:    atomic.LoadInt64(&as.totalBytes),
		DroppedChunks: atomic.LoadInt64(&as.droppedChunks),
		LastChunkTime: as.lastChunkTime,
		AvgChunkSize:  as.avgChunkSize,
		IsRunning:     as.isRunning,
	}
}

// AudioStreamStats 音频流统计信息
type AudioStreamStats struct {
	TotalChunks   int64     `json:"total_chunks"`
	TotalBytes    int64     `json:"total_bytes"`
	DroppedChunks int64     `json:"dropped_chunks"`
	LastChunkTime time.Time `json:"last_chunk_time"`
	AvgChunkSize  float64   `json:"avg_chunk_size"`
	IsRunning     bool      `json:"is_running"`
}

// ConcurrentAudioManager 并发音频管理器
type ConcurrentAudioManager struct {
	streamers    map[string]*AudioStreamer
	service      *StreamingVoiceServiceImpl
	mutex        sync.RWMutex
	maxStreamers int
}

// NewConcurrentAudioManager 创建并发音频管理器
func NewConcurrentAudioManager(service *StreamingVoiceServiceImpl, maxStreamers int) *ConcurrentAudioManager {
	return &ConcurrentAudioManager{
		streamers:    make(map[string]*AudioStreamer),
		service:      service,
		maxStreamers: maxStreamers,
	}
}

// CreateStreamer 创建音频流处理器
func (cam *ConcurrentAudioManager) CreateStreamer(id string, processor AudioProcessor, chunkSize int) (*AudioStreamer, error) {
	cam.mutex.Lock()
	defer cam.mutex.Unlock()

	if len(cam.streamers) >= cam.maxStreamers {
		return nil, fmt.Errorf("maximum number of streamers (%d) reached", cam.maxStreamers)
	}

	if _, exists := cam.streamers[id]; exists {
		return nil, fmt.Errorf("streamer with id %s already exists", id)
	}

	streamer := NewAudioStreamer(cam.service, processor, chunkSize)
	cam.streamers[id] = streamer

	return streamer, nil
}

// GetStreamer 获取音频流处理器
func (cam *ConcurrentAudioManager) GetStreamer(id string) (*AudioStreamer, bool) {
	cam.mutex.RLock()
	defer cam.mutex.RUnlock()

	streamer, exists := cam.streamers[id]
	return streamer, exists
}

// RemoveStreamer 移除音频流处理器
func (cam *ConcurrentAudioManager) RemoveStreamer(id string) error {
	cam.mutex.Lock()
	defer cam.mutex.Unlock()

	streamer, exists := cam.streamers[id]
	if !exists {
		return fmt.Errorf("streamer with id %s not found", id)
	}

	// 停止流处理器
	if err := streamer.Stop(); err != nil {
		return fmt.Errorf("failed to stop streamer: %w", err)
	}

	delete(cam.streamers, id)
	return nil
}

// GetAllStats 获取所有流处理器的统计信息
func (cam *ConcurrentAudioManager) GetAllStats() map[string]AudioStreamStats {
	cam.mutex.RLock()
	defer cam.mutex.RUnlock()

	stats := make(map[string]AudioStreamStats)
	for id, streamer := range cam.streamers {
		stats[id] = streamer.GetStats()
	}

	return stats
}

// StopAllStreamers 停止所有流处理器
func (cam *ConcurrentAudioManager) StopAllStreamers() error {
	cam.mutex.Lock()
	defer cam.mutex.Unlock()

	var errors []string
	for id, streamer := range cam.streamers {
		if err := streamer.Stop(); err != nil {
			errors = append(errors, fmt.Sprintf("failed to stop streamer %s: %v", id, err))
		}
	}

	// 清空流处理器列表
	cam.streamers = make(map[string]*AudioStreamer)

	if len(errors) > 0 {
		return fmt.Errorf("errors stopping streamers: %s", strings.Join(errors, "; "))
	}

	return nil
}

package wechat

import (
	"airAi/global"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// WechatOAuthService 微信OAuth服务
type WechatOAuthService struct {
	AppID       string
	AppSecret   string
	RedirectURL string
	AuthURL     string
	TokenURL    string
	UserInfoURL string
	Scope       string
}

// WechatTokenResponse 微信access_token响应
type WechatTokenResponse struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	OpenID       string `json:"openid"`
	Scope        string `json:"scope"`
	UnionID      string `json:"unionid"`
	ErrorCode    int    `json:"errcode"`
	ErrorMsg     string `json:"errmsg"`
}

// WechatUserInfo 微信用户信息
type WechatUserInfo struct {
	OpenID     string   `json:"openid"`
	UnionID    string   `json:"unionid"`
	Nickname   string   `json:"nickname"`
	Sex        int      `json:"sex"`
	Province   string   `json:"province"`
	City       string   `json:"city"`
	Country    string   `json:"country"`
	HeadImgURL string   `json:"headimgurl"`
	Privilege  []string `json:"privilege"`
	ErrorCode  int      `json:"errcode"`
	ErrorMsg   string   `json:"errmsg"`
}

// NewWechatOAuthService 创建微信OAuth服务实例
func NewWechatOAuthService() *WechatOAuthService {
	config := global.GVA_CONFIG.WechatConfig

	// 设置默认URL
	authURL := config.AuthURL
	if authURL == "" {
		authURL = "https://open.weixin.qq.com/connect/oauth2/authorize"
	}

	tokenURL := config.TokenURL
	if tokenURL == "" {
		tokenURL = "https://api.weixin.qq.com/sns/oauth2/access_token"
	}

	userInfoURL := config.UserInfoURL
	if userInfoURL == "" {
		userInfoURL = "https://api.weixin.qq.com/sns/userinfo"
	}

	scope := config.Scope
	if scope == "" {
		scope = "snsapi_userinfo"
	}

	return &WechatOAuthService{
		AppID:       config.AppID,
		AppSecret:   config.AppSecret,
		RedirectURL: config.RedirectURL,
		AuthURL:     authURL,
		TokenURL:    tokenURL,
		UserInfoURL: userInfoURL,
		Scope:       scope,
	}
}

// IsEnabled 检查微信登录是否启用
func (w *WechatOAuthService) IsEnabled() bool {
	return global.GVA_CONFIG.WechatConfig.Enabled &&
		w.AppID != "" &&
		w.AppSecret != "" &&
		w.RedirectURL != ""
}

// GenerateAuthURL 生成微信授权URL
func (w *WechatOAuthService) GenerateAuthURL(state string) string {
	params := url.Values{}
	params.Set("appid", w.AppID)
	params.Set("redirect_uri", w.RedirectURL)
	params.Set("response_type", "code")
	params.Set("scope", w.Scope)
	params.Set("state", state)

	return fmt.Sprintf("%s?%s#wechat_redirect", w.AuthURL, params.Encode())
}

// GetAccessToken 通过授权码获取access_token
func (w *WechatOAuthService) GetAccessToken(code string) (*WechatTokenResponse, error) {
	params := url.Values{}
	params.Set("appid", w.AppID)
	params.Set("secret", w.AppSecret)
	params.Set("code", code)
	params.Set("grant_type", "authorization_code")

	requestURL := fmt.Sprintf("%s?%s", w.TokenURL, params.Encode())

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(requestURL)
	if err != nil {
		global.GVA_LOG.Error("微信获取access_token请求失败: " + err.Error())
		return nil, fmt.Errorf("微信获取access_token请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("微信获取access_token响应读取失败: " + err.Error())
		return nil, fmt.Errorf("微信获取access_token响应读取失败: %w", err)
	}

	var tokenResp WechatTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		global.GVA_LOG.Error("微信获取access_token响应解析失败: " + err.Error())
		return nil, fmt.Errorf("微信获取access_token响应解析失败: %w", err)
	}

	// 检查微信API错误
	if tokenResp.ErrorCode != 0 {
		global.GVA_LOG.Error(fmt.Sprintf("微信API错误: %d - %s", tokenResp.ErrorCode, tokenResp.ErrorMsg))
		return nil, fmt.Errorf("微信API错误: %d - %s", tokenResp.ErrorCode, tokenResp.ErrorMsg)
	}

	return &tokenResp, nil
}

// GetUserInfo 通过access_token获取用户信息
func (w *WechatOAuthService) GetUserInfo(accessToken, openID string) (*WechatUserInfo, error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("openid", openID)
	params.Set("lang", "zh_CN")

	requestURL := fmt.Sprintf("%s?%s", w.UserInfoURL, params.Encode())

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(requestURL)
	if err != nil {
		global.GVA_LOG.Error("微信获取用户信息请求失败: " + err.Error())
		return nil, fmt.Errorf("微信获取用户信息请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("微信获取用户信息响应读取失败: " + err.Error())
		return nil, fmt.Errorf("微信获取用户信息响应读取失败: %w", err)
	}

	var userInfo WechatUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		global.GVA_LOG.Error("微信获取用户信息响应解析失败: " + err.Error())
		return nil, fmt.Errorf("微信获取用户信息响应解析失败: %w", err)
	}

	// 检查微信API错误
	if userInfo.ErrorCode != 0 {
		global.GVA_LOG.Error(fmt.Sprintf("微信API错误: %d - %s", userInfo.ErrorCode, userInfo.ErrorMsg))
		return nil, fmt.Errorf("微信API错误: %d - %s", userInfo.ErrorCode, userInfo.ErrorMsg)
	}

	return &userInfo, nil
}

// ValidateState 验证state参数（防止CSRF攻击）
func (w *WechatOAuthService) ValidateState(state string) bool {
	// 这里可以实现更复杂的state验证逻辑
	// 例如检查state是否在有效期内，是否包含正确的签名等
	return state != "" && len(state) >= 8
}

// GenerateState 生成随机state参数
func (w *WechatOAuthService) GenerateState() string {
	// 生成一个简单的state，实际应用中应该使用更安全的方法
	return fmt.Sprintf("wechat_%d", time.Now().UnixNano())
}

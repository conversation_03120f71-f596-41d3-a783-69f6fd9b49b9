package deepseek

import (
	"fmt"
	"testing"
)

func TestChat(t *testing.T) {
	client := NewClient()
	// 构建聊天请求
	chatReq := ChatRequest{
		Model: "deepseek-chat",
		Messages: []Message{
			{
				Role:    "user",
				Content: "hello world",
			},
		},
		MaxTokens: 2048,
	}

	rsp, err := client.Chat(chatReq)
	if err != nil {
		t.<PERSON>rror(err)
	}
	if rsp.Error != nil {
		t.<PERSON><PERSON>r(rsp.Error)
	}
	t.Log(rsp)
	fmt.Printf("%+v\n", rsp)
}

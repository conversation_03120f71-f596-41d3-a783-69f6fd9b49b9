package deepseek

import (
	"airAi/common/requst"
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

const apiKey = "sk-debe6d9e5ba04f7a98021e9693bc641f"

// Client 封装 DeepSeek API 客户端
type Client struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
}

// NewClient 创建新的 DeepSeek 客户端
func NewClient() *Client {
	return &Client{
		apiKey:     apiKey,
		baseURL:    "https://api.deepseek.com/v1",
		httpClient: &http.Client{},
	}
}

// ChatMessage 发送聊天请求
func (c *Client) ChatMessage(msg string) (*ChatResponse, error) {
	// 构建聊天请求
	chatReq := ChatRequest{
		Model: "deepseek-chat",
		Messages: []Message{
			{
				Role:    "user",
				Content: msg,
			},
		},
		MaxTokens: 2048,
	}
	return c.Chat(chatReq)
}

// Chat 发送聊天请求
func (c *Client) Chat(req ChatRequest) (*ChatResponse, error) {
	endpoint := c.baseURL + "/chat/completions"
	return c.doRequest(endpoint, req)
}

// doRequest 执行API请求
func (c *Client) doRequest(endpoint string, payload interface{}) (*ChatResponse, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	httpReq, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}
	var apiResp ChatResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &apiResp, nil
}

// StreamingTextChat 流式文本聊天方法
// 用于实现DeepSeek AI文本对话的流式响应，逐步返回AI生成的内容
func (c *Client) StreamingTextChat(ctx context.Context, msg string, resultChan chan<- StreamingChatResult) error {
	// 构建流式聊天请求
	chatReq := ChatRequest{
		Model: "deepseek-reasoner",
		Messages: []Message{
			{
				Role:    "user",
				Content: msg,
			},
		},
		MaxTokens: 2048,
		Stream:    true, // 启用流式响应
	}

	return c.streamingChat(ctx, chatReq, resultChan)
}

// StreamingTextChatWithContext 带对话上下文的流式文本聊天方法
// 用于实现DeepSeek AI文本对话的流式响应，支持完整的对话历史
func (c *Client) StreamingTextChatWithContext(ctx context.Context, contextMessages []requst.ConversationMessage, systemPrompt string, resultChan chan<- StreamingChatResult) error {
	// 构建消息列表
	messages := make([]Message, 0)

	// 添加系统提示词（如果提供）
	if systemPrompt != "" {
		messages = append(messages, Message{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// 添加对话历史消息
	for _, msg := range contextMessages {
		messages = append(messages, Message{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// 构建流式聊天请求
	chatReq := ChatRequest{
		Model:     "deepseek-reasoner",
		Messages:  messages,
		MaxTokens: 2048,
		Stream:    true, // 启用流式响应
	}

	return c.streamingChat(ctx, chatReq, resultChan)
}

// streamingChat 执行流式聊天请求
func (c *Client) streamingChat(ctx context.Context, req ChatRequest, resultChan chan<- StreamingChatResult) error {
	endpoint := c.baseURL + "/chat/completions"

	// 序列化请求数据
	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)
	httpReq.Header.Set("Accept", "text/event-stream")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 处理流式响应
	return c.processStreamingResponse(ctx, resp.Body, resultChan)
}

// processStreamingResponse 处理流式响应数据
func (c *Client) processStreamingResponse(ctx context.Context, body io.Reader, resultChan chan<- StreamingChatResult) error {
	scanner := bufio.NewScanner(body)

	for scanner.Scan() {
		line := scanner.Text()

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 跳过空行和非数据行
		if line == "" || !strings.HasPrefix(line, "data: ") {
			continue
		}

		// 提取数据部分
		data := strings.TrimPrefix(line, "data: ")

		// 检查是否为结束标记
		if data == "[DONE]" {
			result := StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
			}

			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
			break
		}

		// 解析JSON数据
		var streamResp StreamingChatResponse
		if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
			// 忽略解析错误，继续处理下一行
			continue
		}

		// 检查是否有错误
		if streamResp.Error != nil {
			result := StreamingChatResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     streamResp.Error.Message,
			}

			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
			return fmt.Errorf("API error: %s", streamResp.Error.Message)
		}

		// 处理内容
		if len(streamResp.Choices) > 0 {
			choice := streamResp.Choices[0]

			if choice.Delta.Content != "" {
				result := StreamingChatResult{
					Content:   choice.Delta.Content,
					IsPartial: true,
					IsEnd:     false,
				}

				select {
				case resultChan <- result:
				case <-ctx.Done():
					return ctx.Err()
				default:
					// 通道已满或已关闭，跳过此块
				}
			}

			// 检查是否为最后一个块
			if choice.FinishReason != "" {
				result := StreamingChatResult{
					Content:   "",
					IsPartial: false,
					IsEnd:     true,
				}

				select {
				case resultChan <- result:
				case <-ctx.Done():
					return ctx.Err()
				default:
				}
				break
			}
		}
	}

	// 检查扫描器错误
	if err := scanner.Err(); err != nil {
		result := StreamingChatResult{
			Content:   "",
			IsPartial: false,
			IsEnd:     true,
			Error:     err.Error(),
		}

		select {
		case resultChan <- result:
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
		return fmt.Errorf("failed to read response: %v", err)
	}

	return nil
}

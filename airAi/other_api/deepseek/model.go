package deepseek

// ChatRequest 定义请求结构体
type ChatRequest struct {
	Model     string    `json:"model"`
	Messages  []Message `json:"messages"`
	MaxTokens int       `json:"max_tokens,omitempty"`
	Stream    bool      `json:"stream"`
}

// Message 定义消息结构体
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatResponse 定义响应结构体
type ChatResponse struct {
	ID      string         `json:"id"`
	Object  string         `json:"object"`
	Created int64          `json:"created"`
	Choices []Choice       `json:"choices"`
	Usage   Usage          `json:"usage"`
	Error   *ErrorResponse `json:"error"`
}

type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type ErrorResponse struct {
	Message string `json:"message"`
	MsgType string `json:"type"`
	Param   string `json:"param"`
	Code    string `json:"code"`
}

// StreamingChatResult 流式聊天结果结构体
// 用于表示DeepSeek AI文本聊天的流式响应结果
type StreamingChatResult struct {
	Content   string `json:"content"`         // AI生成的文本内容片段
	IsPartial bool   `json:"is_partial"`      // 是否为部分结果：true=部分内容，false=完整结果
	IsEnd     bool   `json:"is_end"`          // 是否为结束标记：true=响应结束，false=继续响应
	Error     string `json:"error,omitempty"` // 错误信息（如果有）
}

// StreamingChoice 流式响应选择结构体
type StreamingChoice struct {
	Index int `json:"index"`
	Delta struct {
		Content string `json:"content"`
	} `json:"delta"`
	FinishReason string `json:"finish_reason"`
}

// StreamingChatResponse 流式聊天响应结构体
type StreamingChatResponse struct {
	ID      string            `json:"id"`
	Object  string            `json:"object"`
	Created int64             `json:"created"`
	Choices []StreamingChoice `json:"choices"`
	Error   *ErrorResponse    `json:"error"`
}

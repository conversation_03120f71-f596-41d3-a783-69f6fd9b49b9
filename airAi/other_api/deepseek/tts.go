package deepseek

// TextToSpeech 文本转语音
//func TextToSpeech(text string) ([]byte, error) {
//	// 1. 建立WebSocket连接
//	conn, err := createWebSocketConn()
//	if err != nil {
//		return nil, fmt.Errorf("failed to create websocket connection: %v", err)
//	}
//	defer conn.Close()
//
//	// 2. 构造请求数据
//	req := TTSRequest{}
//	req.Common.AppID = appID
//	req.Business.Aue = "lame" // 输出mp3格式
//	req.Business.Sfl = 0      // 非流式
//	req.Business.Auf = "audio/L16;rate=16000"
//	req.Business.Vcn = "xiaoyan" // 发音人
//	req.Business.Speed = 50      // 中等语速
//	req.Business.Volume = 50     // 中等音量
//	req.Business.Pitch = 50      // 中等音高
//	req.Data.Text = base64.StdEncoding.EncodeToString([]byte(text))
//	req.Data.Encoding = "base64"
//	req.Data.Status = 2 // 最后一个数据块
//	reqBytes, err := json.Marshal(req)
//	if err != nil {
//		return nil, fmt.Errorf("failed to marshal request: %v", err)
//	}
//
//	// 3. 发送请求
//	if err := conn.WriteMessage(websocket.TextMessage, reqBytes); err != nil {
//		return nil, fmt.Errorf("failed to send message: %v", err)
//	}
//
//	// 4. 接收响应
//	_, respBytes, err := conn.ReadMessage()
//	if err != nil {
//		return nil, fmt.Errorf("failed to read message: %v", err)
//	}
//
//	var resp TTSResponse
//	if err := json.Unmarshal(respBytes, &resp); err != nil {
//		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
//	}
//
//	if resp.Code != 0 {
//		return nil, fmt.Errorf("API error: %s (code: %d)", resp.Message, resp.Code)
//	}
//	// 5. 解码音频数据
//	audioData, err := base64.StdEncoding.DecodeString(resp.Data.Audio)
//	if err != nil {
//		return nil, fmt.Errorf("failed to decode audio data: %v", err)
//	}
//
//	return audioData, nil
//}

package firebase

import (
	"airAi/global"
	"context"
	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"fmt"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

type Firebase struct {
	App               *firebase.App
	RegistrationToken string
}

func NewFirebase(token string) (*Firebase, error) {
	app, err := firebase.NewApp(context.Background(), nil, option.WithCredentialsFile("firebase.json"))
	if err != nil {
		return nil, err
	}
	return &Firebase{
		App:               app,
		RegistrationToken: token,
	}, nil
}

func (f *Firebase) SendToToken(data map[string]string) (string, error) {
	// [START send_to_token_golang]
	// Obtain a messaging.Client from the App.
	ctx := context.Background()
	client, err := f.App.Messaging(ctx)
	if err != nil {
		global.GVA_LOG.Error("messaging failed", zap.Any("err", err))
		return "", err
	}
	message := &messaging.Message{
		Data:  data,
		Token: f.RegistrationToken,
	}

	// Send a message to the device corresponding to the provided
	// registration token.
	response, err := client.Send(ctx, message)
	if err != nil {
		return "", err
	}
	// Response is a message ID string.
	fmt.Println("Successfully sent message:", response)
	// [END send_to_token_golang]
	return response, nil
}

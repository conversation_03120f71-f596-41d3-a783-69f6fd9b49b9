package brevo

type SendEmail struct {
	Sender struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	} `json:"sender"`
	To          *[]Recipient `json:"to,omitempty"` // 使用指针并添加 `omitempty`
	Bcc         *[]Recipient `json:"bcc,omitempty"`
	Cc          *[]Recipient `json:"cc,omitempty"`
	HtmlContent string       `json:"htmlContent"`
	Subject     string       `json:"subject"`
	ReplyTo     *Recipient   `json:"replyTo,omitempty"`
	Tags        *[]string    `json:"tags,omitempty"`
}

type Recipient struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

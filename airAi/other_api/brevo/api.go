package brevo

import (
	"airAi/core/consts"
	"airAi/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
)

func getAccountFromEmail(email string) string {
	// 使用 strings.Split 方法
	parts := strings.Split(email, "@")
	if len(parts) > 1 {
		return parts[0]
	}
	return ""
}

const ApiUrl = "https://api.brevo.com/v3"

func SendEmailMessage(to string, code int64) (bool, error) {
	msg := fmt.Sprintf(consts.EmailMessage, code, consts.SmsExpirationMinTime)
	return SendMessage(to, msg)
}

func SendMessage(to, msg string) (bool, error) {
	api := "/smtp/email"
	var data SendEmail
	data.Sender.Name = "kwolfs"
	data.Sender.Email = "<EMAIL>"
	data.HtmlContent = msg
	data.Subject = consts.EmailSubject
	if to != "" {
		data.To = &[]Recipient{
			{
				Name:  getAccountFromEmail(to),
				Email: to,
			},
		}
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return false, err
	}
	url := fmt.Sprintf("%v%v", ApiUrl, api)
	httpClient := utils.NewHttpClient(url, "POST")
	httpClient.SetHeader("api-key", "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")
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return false, err
	}
	if string(resBody) == "" {
		return false, nil
	}

	return true, nil
}

func SendCreateLoginMessage(to string, login int64, password string) (bool, error) {
	str := `
		<!DOCTYPE html>
<html>
<body>
    <p>【Kwolfs】</p>
    <p>Login: <strong>%v</strong></p>
    <p>Password: <strong>%v</strong></p>
</body>
</html>`
	msg := fmt.Sprintf(str, login, password)
	return SendMessage(to, msg)
}

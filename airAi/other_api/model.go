package other_api

type Animal struct {
	Name           string `json:"name"`
	ScientificName string `json:"scientific_name"`
	Habitat        string `json:"habitat"`
	FeedingHabits  string `json:"feeding_habits"`
	Description    string `json:"description"`
}

type MeetingNote struct {
	Title       string       `json:"title"`
	Date        string       `json:"date"`
	Attendees   []string     `json:"attendees"`
	Summary     string       `json:"summary"`
	Topics      []Topic      `json:"topics"`
	ActionItems []ActionItem `json:"action_items"`
}

type Topic struct {
	Topic       string `json:"topic"`
	Details     string `json:"details"`
	Conclusions string `json:"conclusions"`
}

type ActionItem struct {
	Item    string `json:"item"`
	Owner   string `json:"owner"`
	DueDate string `json:"due_date"`
}

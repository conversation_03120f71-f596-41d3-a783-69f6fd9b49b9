package sms

import (
	"airAi/core/consts"
	"airAi/global"
	"airAi/utils"
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"
)

func GenerateSmsSignature(params map[string]string, secret string) string {
	// Extract keys from the params map
	signValidatorAttributes := make([]string, 0, len(params))
	for key := range params {
		if key == "sign" {
			continue
		}
		signValidatorAttributes = append(signValidatorAttributes, key)
	}
	// Sort keys
	sort.Strings(signValidatorAttributes)
	// Prepare the signature string
	lines := make([]string, 0, len(signValidatorAttributes)+1)
	for _, key := range signValidatorAttributes {
		if val, ok := params[key]; ok {
			lines = append(lines, key+"="+val)
		}
	}
	// Add the access key (secret)
	lines = append(lines, "accessKey="+secret)
	// Join lines with '|'
	signatureString := strings.Join(lines, "|")
	// Return the md5 hash of the signature string
	hash := md5.Sum([]byte(signatureString))
	return hex.EncodeToString(hash[:])
}
func SendMessage(mobile string, code int64) (bool, error) {
	api := "/admin_api/sms/send-single"
	msg := fmt.Sprintf(consts.Message, code, consts.SmsExpirationMinTime)
	data := make(map[string]string)
	data["merchant_code"] = global.GVA_CONFIG.Sms.Merchant
	data["mobile"] = mobile
	data["template_text"] = msg
	data["timestamp"] = fmt.Sprintf("%d", time.Now().Unix())
	data["sign"] = GenerateSmsSignature(data, global.GVA_CONFIG.Sms.Secret)
	jsonData, err := json.Marshal(data)
	if err != nil {
		return false, err
	}
	url := fmt.Sprintf("%v%v", global.GVA_CONFIG.Sms.Url, api)
	httpClient := utils.NewHttpClient(url, "POST")
	body := bytes.NewBuffer(jsonData)
	httpClient.SetBody(body)
	resBody, err := httpClient.Send()
	if err != nil {
		return false, err
	}
	if string(resBody) == "" {
		return false, nil
	}
	fmt.Println(string(resBody))
	var result Response
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return false, err
	}
	if !result.Ret.State {
		return false, fmt.Errorf("%v", result.Msg)
	}
	return true, nil
}

package response

// WechatAuthResponse 微信授权响应
type WechatAuthResponse struct {
	// 授权URL
	AuthURL string `json:"auth_url"`
	
	// 状态参数
	State string `json:"state"`
	
	// 二维码URL（如果支持）
	QRCodeURL string `json:"qr_code_url,omitempty"`
}

// WechatUserResponse 微信用户信息响应
type WechatUserResponse struct {
	// 微信OpenID
	OpenID string `json:"open_id"`
	
	// 微信UnionID
	UnionID string `json:"union_id,omitempty"`
	
	// 用户昵称
	Nickname string `json:"nickname"`
	
	// 用户性别 (1-男性，2-女性，0-未知)
	Sex int `json:"sex"`
	
	// 用户头像URL
	HeadImgURL string `json:"head_img_url"`
	
	// 用户所在省份
	Province string `json:"province"`
	
	// 用户所在城市
	City string `json:"city"`
	
	// 用户所在国家
	Country string `json:"country"`
}

// WechatLoginResponse 微信登录成功响应
type WechatLoginResponse struct {
	// 用户信息
	User User `json:"user"`
	
	// 微信用户信息
	WechatUser WechatUserResponse `json:"wechat_user"`
	
	// JWT Token
	Token string `json:"token"`
	
	// Token过期时间（毫秒时间戳）
	ExpiresAt int64 `json:"expires_at"`
	
	// 是否为新用户
	IsNewUser bool `json:"is_new_user"`
}

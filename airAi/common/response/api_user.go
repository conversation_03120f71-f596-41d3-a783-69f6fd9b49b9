package response

import "github.com/google/uuid"

type LoginResponse struct {
	User      User   `json:"user"`      // 用户
	Token     string `json:"token"`     // 登陆token
	ExpiresAt int64  `json:"expiresAt"` // token过期时间
}

type User struct {
	UUID      uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"`                                                     // 用户UUID
	Username  string    `json:"userName" gorm:"index;comment:用户登录名"`                                                  // 用户登录名
	Password  string    `json:"-"  gorm:"comment:用户登录密码"`                                                             // 用户登录密码
	NickName  string    `json:"nickName" gorm:"default:User;comment:用户昵称"`                                            // 用户昵称
	HeaderImg string    `json:"headerImg" gorm:"default:https://qmplusimg.henrongyi.top/gva_header.jpg;comment:用户头像"` // 用户头像
	Phone     string    `json:"phone"  gorm:"comment:用户手机号"`                                                          // 用户手机号
	Email     string    `json:"email"  gorm:"comment:用户邮箱"`                                                           // 用户邮箱
	Enable    int       `json:"enable" gorm:"default:1;comment:用户是否被冻结 1正常 2冻结"`                                      //用户
}

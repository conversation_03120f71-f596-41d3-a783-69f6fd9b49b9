package requst

type LoginRequest struct {
	Account  string `json:"account"`            // 手机号
	Code     int64  `json:"code"`               // 使用验证码登陆时填
	Password string `json:"password,omitempty"` // 使用密码时填写
}

// RegisterCode User register structure
type RegisterCode struct {
	//Code    int64  `json:"code" example:"123456" `                  // 验证码
	Account string `json:"account" binding:"required" example:"账号"` // 账号
}

// ResetPassword User phone
type ResetPassword struct {
	Phone    string `json:"phone" binding:"required" example:"手机号"`   // 手机号
	Code     int64  `json:"code" binding:"required" example:"123456"` // 验证码
	Password string `json:"password" binding:"required"`              // 密码
}

// Feedback User phone
type Feedback struct {
	Content string `json:"content" binding:"required" example:"意见"` // 意见
}

type CheckCodeRequest struct {
	Account string `json:"account"` // 手机号
	Code    int64  `json:"code"`    // 验证码
}

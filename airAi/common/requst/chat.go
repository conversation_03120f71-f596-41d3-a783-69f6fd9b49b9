package requst

// ConversationMessage 对话消息结构体
// 用于表示对话历史中的单条消息
type ConversationMessage struct {
	Role      string `json:"role"`      // 消息角色：user（用户）或 assistant（AI助手）
	Content   string `json:"content"`   // 消息内容
	Timestamp int64  `json:"timestamp"` // 消息时间戳（Unix时间戳）
}

type ChatRequest struct {
	Text       string  `form:"text" json:"text" binding:"required"`         // 文本文件
	SpeechType string  `form:"speechType" json:"speechType" default:"qwen"` // deepseek/qwen
	Stream     bool    `form:"stream" json:"stream" default:"false"`        // 是否启用流式响应：true=流式返回AI响应，false=普通响应
	SpeechRate float64 `form:"speechRate" json:"speechRate" default:"1.0"`  // 语音速率：0.5-2.0，默认1.0为正常语速

	// 对话上下文相关字段
	ConversationID string                `form:"conversationId" json:"conversationId"` // 对话会话ID，用于标识和维护对话上下文
	Messages       []ConversationMessage `form:"messages" json:"messages"`             // 对话历史消息列表，包含之前的用户和AI消息
	ContextWindow  int                   `form:"contextWindow" json:"contextWindow"`   // 上下文窗口大小，限制发送给AI的历史消息数量，默认为10
	SystemPrompt   string                `form:"systemPrompt" json:"systemPrompt"`     // 系统提示词，用于设置AI的行为和角色
}

package requst

// WechatAuthRequest 微信授权请求参数
type WechatAuthRequest struct {
	// 重定向URL（可选，用于自定义回调地址）
	RedirectURL string `json:"redirect_url" form:"redirect_url"`
	
	// 状态参数（可选，用于防止CSRF攻击）
	State string `json:"state" form:"state"`
}

// WechatCallbackRequest 微信授权回调请求参数
type WechatCallbackRequest struct {
	// 授权码
	Code string `json:"code" form:"code" binding:"required"`
	
	// 状态参数
	State string `json:"state" form:"state"`
	
	// 错误代码（当用户拒绝授权时）
	Error string `json:"error" form:"error"`
	
	// 错误描述
	ErrorDescription string `json:"error_description" form:"error_description"`
}

// WechatLoginRequest 微信登录请求参数（用于手动提交授权码）
type WechatLoginRequest struct {
	// 授权码
	Code string `json:"code" binding:"required"`
	
	// 状态参数
	State string `json:"state"`
}

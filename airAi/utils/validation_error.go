package utils

import (
	"airAi/core/i18n"
	"fmt"
)

// ValidationErrorType 定义验证错误类型
type ValidationErrorType string

const (
	// 通用验证错误类型
	ValidationFieldRequired  ValidationErrorType = "validation_field_required"
	ValidationFormatInvalid  ValidationErrorType = "validation_format_invalid"
	ValidationRangeInvalid   ValidationErrorType = "validation_range_invalid"
	ValidationStructExpected ValidationErrorType = "validation_struct_expected"
	ValidationRuleDuplicate  ValidationErrorType = "validation_rule_duplicate"
)

// ValidationError 国际化验证错误结构
type ValidationError struct {
	Type     ValidationErrorType `json:"type"`
	Field    string              `json:"field"`
	Rule     string              `json:"rule,omitempty"`
	RawError string              `json:"raw_error,omitempty"`
}

// Error 实现error接口，返回原始错误信息（用于向后兼容）
func (ve *ValidationError) Error() string {
	if ve.RawError != "" {
		return ve.RawError
	}
	return fmt.Sprintf("%s validation failed for field %s", ve.Type, ve.Field)
}

// GetI18nMessage 获取国际化错误消息
func (ve *ValidationError) GetI18nMessage(lang string) string {
	// 构建模板数据
	data := map[string]interface{}{
		"Field": ve.Field,
		"Rule":  ve.Rule,
	}

	// 尝试使用go-i18n系统获取消息
	message := i18n.GetMessageV2(lang, string(ve.Type))
	if message != string(ve.Type) {
		// 如果go-i18n返回了翻译，尝试进行模板替换
		return replaceTemplate(message, data)
	}

	// 回退到旧的i18n系统
	message = i18n.GetMessage(lang, i18n.MessageKey(ve.Type))
	return replaceTemplate(message, data)
}

// GetI18nKey 获取国际化消息键
func (ve *ValidationError) GetI18nKey() string {
	return string(ve.Type)
}

// NewValidationError 创建新的验证错误
func NewValidationError(errorType ValidationErrorType, field string, rule ...string) *ValidationError {
	ve := &ValidationError{
		Type:  errorType,
		Field: field,
	}

	if len(rule) > 0 {
		ve.Rule = rule[0]
	}

	// 生成原始错误信息（向后兼容）
	switch errorType {
	case ValidationFieldRequired:
		ve.RawError = field + "值不能为空"
	case ValidationFormatInvalid:
		ve.RawError = field + "格式校验不通过"
	case ValidationRangeInvalid:
		if len(rule) > 0 {
			ve.RawError = field + "长度或值不在合法范围," + rule[0]
		} else {
			ve.RawError = field + "长度或值不在合法范围"
		}
	case ValidationStructExpected:
		ve.RawError = "expect struct"
	case ValidationRuleDuplicate:
		if len(rule) > 0 {
			ve.RawError = rule[0] + "已注册,无法重复注册"
		} else {
			ve.RawError = field + "已注册,无法重复注册"
		}
	default:
		ve.RawError = fmt.Sprintf("%s validation failed", field)
	}

	return ve
}

// replaceTemplate 简单的模板替换函数
func replaceTemplate(template string, data map[string]interface{}) string {
	result := template
	for key, value := range data {
		placeholder := fmt.Sprintf("{{.%s}}", key)
		replacement := fmt.Sprintf("%v", value)
		// 只有当值不为空时才进行替换
		if replacement != "" && replacement != "<nil>" {
			result = replaceAll(result, placeholder, replacement)
		}
	}
	return result
}

// replaceAll 替换字符串中的所有匹配项
func replaceAll(s, old, new string) string {
	// 简单的字符串替换实现
	for {
		newS := replace(s, old, new)
		if newS == s {
			break
		}
		s = newS
	}
	return s
}

// replace 替换字符串中的第一个匹配项
func replace(s, old, new string) string {
	if old == "" {
		return s
	}

	// 查找第一个匹配位置
	index := indexOf(s, old)
	if index == -1 {
		return s
	}

	// 执行替换
	return s[:index] + new + s[index+len(old):]
}

// indexOf 查找子字符串的位置
func indexOf(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// IsValidationError 检查错误是否为验证错误
func IsValidationError(err error) (*ValidationError, bool) {
	if ve, ok := err.(*ValidationError); ok {
		return ve, true
	}
	return nil, false
}

// MapValidationError 将传统验证错误映射为国际化验证错误
func MapValidationError(err error) *ValidationError {
	if err == nil {
		return nil
	}

	errMsg := err.Error()

	// 检测不同类型的验证错误
	if indexOf(errMsg, "值不能为空") != -1 {
		// 提取字段名
		field := extractFieldName(errMsg, "值不能为空")
		return NewValidationError(ValidationFieldRequired, field)
	}

	if indexOf(errMsg, "格式校验不通过") != -1 {
		// 提取字段名
		field := extractFieldName(errMsg, "格式校验不通过")
		return NewValidationError(ValidationFormatInvalid, field)
	}

	if indexOf(errMsg, "长度或值不在合法范围") != -1 {
		// 提取字段名和规则
		field, rule := extractFieldAndRule(errMsg, "长度或值不在合法范围")
		return NewValidationError(ValidationRangeInvalid, field, rule)
	}

	if errMsg == "expect struct" {
		return NewValidationError(ValidationStructExpected, "")
	}

	if indexOf(errMsg, "已注册,无法重复注册") != -1 {
		// 提取规则名
		rule := extractFieldName(errMsg, "已注册,无法重复注册")
		return NewValidationError(ValidationRuleDuplicate, "", rule)
	}

	// 未知错误类型，创建通用验证错误
	return &ValidationError{
		Type:     ValidationFormatInvalid,
		Field:    "unknown",
		RawError: errMsg,
	}
}

// extractFieldName 从错误消息中提取字段名
func extractFieldName(errMsg, suffix string) string {
	index := indexOf(errMsg, suffix)
	if index == -1 {
		return "unknown"
	}
	return errMsg[:index]
}

// extractFieldAndRule 从错误消息中提取字段名和规则
func extractFieldAndRule(errMsg, separator string) (string, string) {
	index := indexOf(errMsg, separator)
	if index == -1 {
		return "unknown", ""
	}

	field := errMsg[:index]
	remaining := errMsg[index+len(separator):]

	// 查找逗号分隔的规则部分
	commaIndex := indexOf(remaining, ",")
	if commaIndex != -1 {
		rule := remaining[commaIndex+1:]
		return field, rule
	}

	return field, ""
}

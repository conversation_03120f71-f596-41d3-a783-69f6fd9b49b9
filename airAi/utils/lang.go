package utils

import (
	"airAi/core/consts"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
)

// GetLang 从上下文中获取语言代码（兼容现有代码）
func GetLang(c *gin.Context) string {
	lang := c.GetHeader("lang")
	if lang == "" {
		lang = consts.DefaultLangCode // 默认语言
	}
	return lang
}

// GetLanguageFromRequest 从请求中获取语言代码（增强版本）
// 支持多种方式获取语言参数：查询参数、表单参数、请求头
func GetLanguageFromRequest(c *gin.Context) string {
	// 优先从查询参数获取
	if lang := c.Query("lang"); lang != "" {
		return NormalizeLanguageCode(lang)
	}

	// 从表单参数获取
	if lang := c.PostForm("lang"); lang != "" {
		return NormalizeLanguageCode(lang)
	}

	// 从Accept-Language请求头获取
	if lang := c.<PERSON>Header("Accept-Language"); lang != "" {
		// 解析Accept-Language头，取第一个语言
		if parsedLang := parseAcceptLanguage(lang); parsedLang != "" {
			return NormalizeLanguageCode(parsedLang)
		}
	}

	// 从lang请求头获取（兼容现有代码）
	if lang := c.GetHeader("lang"); lang != "" {
		return NormalizeLanguageCode(lang)
	}

	// 默认返回中文
	return consts.DefaultLangCode
}

// NormalizeLanguageCode 标准化语言代码
func NormalizeLanguageCode(lang string) string {
	// 转换为小写
	lang = strings.ToLower(lang)

	// 使用常量中定义的映射表
	if normalizedLang, exists := consts.SupportedLanguages[lang]; exists {
		return normalizedLang
	}

	// 如果不支持该语言代码，返回默认语言
	return consts.DefaultLangCode
}

// parseAcceptLanguage 解析Accept-Language请求头
func parseAcceptLanguage(acceptLang string) string {
	// 简单解析Accept-Language头，取第一个语言
	// 例如: "zh-CN,zh;q=0.9,en;q=0.8" -> "zh-CN"
	if acceptLang == "" {
		return ""
	}

	// 按逗号分割
	languages := strings.Split(acceptLang, ",")
	if len(languages) == 0 {
		return ""
	}

	// 取第一个语言，去除权重信息
	firstLang := strings.TrimSpace(languages[0])
	if idx := strings.Index(firstLang, ";"); idx > 0 {
		firstLang = firstLang[:idx]
	}

	return strings.TrimSpace(firstLang)
}

// IsLanguageSupported 检查是否支持指定语言
func IsLanguageSupported(lang string) bool {
	normalizedLang := NormalizeLanguageCode(lang)
	return normalizedLang != consts.DefaultLangCode || lang == consts.DefaultLangCode
}

// 会议纪要支持的语言映射表
var MeetingLanguageMap = map[string]string{
	"zh":    "中文",
	"zh_cn": "简体中文",
	"zh_tw": "繁体中文",
	"en":    "English",
	"ja":    "日本語",
	"ko":    "한국어",
	"es":    "Español",
	"fr":    "Français",
	"de":    "Deutsch",
	"it":    "Italiano",
	"pt":    "Português",
	"ru":    "Русский",
	"ar":    "العربية",
	"hi":    "हिन्दी",
	"th":    "ไทย",
	"vi":    "Tiếng Việt",
	"id":    "Bahasa Indonesia",
	"ms":    "Bahasa Melayu",
	"tr":    "Türkçe",
	"nl":    "Nederlands",
	"sv":    "Svenska",
	"da":    "Dansk",
	"no":    "Norsk",
	"fi":    "Suomi",
	"pl":    "Polski",
	"cs":    "Čeština",
	"hu":    "Magyar",
	"ro":    "Română",
}

// ValidateMeetingLanguage 验证会议纪要语言代码是否有效
func ValidateMeetingLanguage(languageCode string) error {
	if languageCode == "" {
		return fmt.Errorf("语言代码不能为空")
	}

	if _, exists := MeetingLanguageMap[languageCode]; !exists {
		return fmt.Errorf("不支持的语言代码: %s", languageCode)
	}

	return nil
}

// GetMeetingLanguageName 获取语言代码对应的自然语言名称
func GetMeetingLanguageName(languageCode string) (string, error) {
	if err := ValidateMeetingLanguage(languageCode); err != nil {
		return "", err
	}

	return MeetingLanguageMap[languageCode], nil
}

// GetDefaultMeetingLanguage 获取默认的会议纪要语言
func GetDefaultMeetingLanguage() string {
	return "zh" // 默认使用中文
}

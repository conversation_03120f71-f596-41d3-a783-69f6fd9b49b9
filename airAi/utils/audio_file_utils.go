package utils

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"airAi/global"

	"go.uber.org/zap"
)

// AudioFileManager 音频文件管理器
type AudioFileManager struct {
	OutputDir string
}

// NewAudioFileManager 创建音频文件管理器
func NewAudioFileManager(outputDir string) *AudioFileManager {
	return &AudioFileManager{
		OutputDir: outputDir,
	}
}

// EnsureOutputDirectory 确保输出目录存在
func (afm *AudioFileManager) EnsureOutputDirectory() error {
	if _, err := os.Stat(afm.OutputDir); os.IsNotExist(err) {
		err = os.MkdirAll(afm.OutputDir, 0755)
		if err != nil {
			return fmt.Errorf("failed to create output directory %s: %w", afm.OutputDir, err)
		}
		if global.GVA_LOG != nil {
			global.GVA_LOG.Info("Created audio output directory", zap.String("path", afm.OutputDir))
		}
	}
	return nil
}

// SaveAudioFile 保存音频文件
func (afm *AudioFileManager) SaveAudioFile(audioData []byte, segmentID int, timestamp int64, format string) (string, error) {
	// 确保输出目录存在
	if err := afm.EnsureOutputDirectory(); err != nil {
		return "", err
	}

	// 生成文件名：voice_response_[timestamp]_segment_[segment_id].mp3
	filename := fmt.Sprintf("voice_response_%d_segment_%d.%s", timestamp, segmentID, format)
	filepath := filepath.Join(afm.OutputDir, filename)

	// 写入音频数据到文件
	err := os.WriteFile(filepath, audioData, 0644)
	if err != nil {
		return "", fmt.Errorf("failed to write audio file %s: %w", filepath, err)
	}

	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("Successfully saved audio file",
			zap.String("filepath", filepath),
			zap.Int("segment_id", segmentID),
			zap.Int64("timestamp", timestamp),
			zap.String("format", format),
			zap.Int("size_bytes", len(audioData)),
		)
	}

	return filepath, nil
}

// SaveBase64AudioFile 保存base64编码的音频文件
func (afm *AudioFileManager) SaveBase64AudioFile(base64Data string, segmentID int, timestamp int64, format string) (string, error) {
	// 解码base64数据
	audioData, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 audio data: %w", err)
	}

	return afm.SaveAudioFile(audioData, segmentID, timestamp, format)
}

// VerifyAudioFile 验证音频文件
func (afm *AudioFileManager) VerifyAudioFile(filepath string, expectedDuration float64, expectedFormat string) (*AudioFileInfo, error) {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filepath)
	if err != nil {
		return nil, fmt.Errorf("audio file not found: %w", err)
	}

	// 读取文件内容进行基本验证
	audioData, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read audio file: %w", err)
	}

	// 基本格式验证
	if err := afm.validateAudioFormat(audioData, expectedFormat); err != nil {
		return nil, fmt.Errorf("audio format validation failed: %w", err)
	}

	audioFileInfo := &AudioFileInfo{
		FilePath:  filepath,
		FileSize:  fileInfo.Size(),
		Format:    expectedFormat,
		Duration:  expectedDuration,
		CreatedAt: fileInfo.ModTime(),
		IsValid:   true,
		DataSize:  len(audioData),
	}

	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("Audio file verification completed",
			zap.String("filepath", filepath),
			zap.Int64("file_size", fileInfo.Size()),
			zap.Float64("expected_duration", expectedDuration),
			zap.String("format", expectedFormat),
			zap.Bool("is_valid", true),
		)
	}

	return audioFileInfo, nil
}

// validateAudioFormat 验证音频格式
func (afm *AudioFileManager) validateAudioFormat(audioData []byte, expectedFormat string) error {
	if len(audioData) < 4 {
		return fmt.Errorf("audio data too short for format validation")
	}

	switch expectedFormat {
	case "mp3":
		// MP3文件通常以ID3标签开始或直接以MP3帧开始
		// ID3v2标签以"ID3"开始，MP3帧以0xFF 0xFB开始
		if len(audioData) >= 3 && string(audioData[:3]) == "ID3" {
			return nil // ID3标签
		}
		if len(audioData) >= 2 && audioData[0] == 0xFF && (audioData[1]&0xE0) == 0xE0 {
			return nil // MP3帧同步字
		}
		return fmt.Errorf("invalid MP3 format: missing ID3 tag or MP3 frame sync")

	case "wav":
		// WAV文件以"RIFF"开始
		if len(audioData) >= 4 && string(audioData[:4]) == "RIFF" {
			return nil
		}
		return fmt.Errorf("invalid WAV format: missing RIFF header")

	default:
		// 对于其他格式，只检查数据不为空
		if len(audioData) == 0 {
			return fmt.Errorf("empty audio data")
		}
		return nil
	}
}

// AudioFileInfo 音频文件信息
type AudioFileInfo struct {
	FilePath  string    `json:"file_path"`
	FileSize  int64     `json:"file_size"`
	Format    string    `json:"format"`
	Duration  float64   `json:"duration"`
	CreatedAt time.Time `json:"created_at"`
	IsValid   bool      `json:"is_valid"`
	DataSize  int       `json:"data_size"`
}

// GetAudioFileInfo 获取音频文件信息
func (afm *AudioFileManager) GetAudioFileInfo(filepath string) (*AudioFileInfo, error) {
	fileInfo, err := os.Stat(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// 从文件名推断格式
	ext := filepath[len(filepath)-3:]
	format := "unknown"
	if ext == "mp3" || ext == "wav" || ext == "m4a" {
		format = ext
	}

	return &AudioFileInfo{
		FilePath:  filepath,
		FileSize:  fileInfo.Size(),
		Format:    format,
		CreatedAt: fileInfo.ModTime(),
		IsValid:   true,
	}, nil
}

// CleanupOldFiles 清理旧的音频文件
func (afm *AudioFileManager) CleanupOldFiles(maxAge time.Duration) error {
	if _, err := os.Stat(afm.OutputDir); os.IsNotExist(err) {
		return nil // 目录不存在，无需清理
	}

	entries, err := os.ReadDir(afm.OutputDir)
	if err != nil {
		return fmt.Errorf("failed to read output directory: %w", err)
	}

	cutoffTime := time.Now().Add(-maxAge)
	cleanedCount := 0

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		filePath := filepath.Join(afm.OutputDir, entry.Name())
		fileInfo, err := entry.Info()
		if err != nil {
			continue
		}

		if fileInfo.ModTime().Before(cutoffTime) {
			if err := os.Remove(filePath); err == nil {
				cleanedCount++
			}
		}
	}

	if cleanedCount > 0 && global.GVA_LOG != nil {
		global.GVA_LOG.Info("Cleaned up old audio files",
			zap.Int("cleaned_count", cleanedCount),
			zap.String("output_dir", afm.OutputDir),
			zap.Duration("max_age", maxAge),
		)
	}

	return nil
}

// GetDefaultAudioFileManager 获取默认的音频文件管理器
func GetDefaultAudioFileManager() *AudioFileManager {
	outputDir := "./audio_output"
	if envDir := os.Getenv("AUDIO_OUTPUT_DIR"); envDir != "" {
		outputDir = envDir
	}
	return NewAudioFileManager(outputDir)
}

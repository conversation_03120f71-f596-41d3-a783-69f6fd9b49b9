package utils

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

const (
	sequenceBits = 1                   // 序列号位数
	sequenceMask = 1<<sequenceBits - 1 // 序列号掩码
)

type UniqueIDGenerator struct {
	sync.Mutex
	lastTimestamp int64
	sequence      int64
}

func NewUniqueIDGenerator() *UniqueIDGenerator {
	return &UniqueIDGenerator{
		lastTimestamp: -1,
		sequence:      0,
	}
}

func GetGenerateID() int64 {
	return NewUniqueIDGenerator().GenerateID()
}

func GetGenerateIdToString() string {
	return fmt.Sprintf("%d", NewUniqueIDGenerator().GenerateID())
}

func (g *UniqueIDGenerator) GenerateID() int64 {
	g.Lock()
	defer g.Unlock()

	timestamp := time.Now().Unix()
	if timestamp == g.lastTimestamp {
		g.sequence = (g.sequence + 1) & sequenceMask
		if g.sequence == 0 {
			// 等待下一个时间戳
			for timestamp <= g.lastTimestamp {
				timestamp = time.Now().Unix()
			}
		}
	} else {
		g.sequence = 0
	}

	g.lastTimestamp = timestamp

	// 取时间戳的前8位
	id := (timestamp % 100000000) * 10
	// 添加序列号
	id += g.sequence

	return id
}

// GenerateOTP generates a 6-digit OTP (One Time Password)
func GenerateOTP() int {
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)

	return r.Intn(999999-100000+1) + 100000
}

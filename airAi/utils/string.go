package utils

import (
	"crypto/rand"
	"encoding/base32"
	"encoding/json"
	"fmt"
	"strings"
)

func ExtractAndParseJSON(input string) (string, error) {
	// 找到第一个 { 和最后一个 }
	start := strings.Index(input, "{")
	if start == -1 {
		return "", fmt.<PERSON><PERSON><PERSON>("invalid JSON")
	}

	end := strings.LastIndex(input, "}")
	if end == -1 {
		return "", fmt.<PERSON><PERSON><PERSON>("invalid json end")
	}

	if start >= end {
		return "", fmt.<PERSON><PERSON><PERSON>("invalid json")
	}

	jsonStr := strings.TrimSpace(input[start : end+1])
	// 验证是否是有效的JSON
	if !json.Valid([]byte(jsonStr)) {
		return "", fmt.<PERSON><PERSON><PERSON>("invalid")
	}

	return jsonStr, nil
}

func RandomUsername(num int64) (string, error) {
	b := make([]byte, num)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return "usr_" + base32.StdEncoding.WithPadding(base32.NoPadding).EncodeToString(b), nil
}

package utils

var (
	IdVerify        = Rules{"ID": []string{NotEmpty()}}
	ApiVerify       = Rules{"Path": {NotEmpty()}, "Description": {NotEmpty()}, "ApiGroup": {NotEmpty()}, "Method": {NotEmpty()}}
	LoginVerify     = Rules{"Account": {NotEmpty()}}
	CheckCodeVerify = Rules{"Account": {NotEmpty()}, "Code": {NotEmpty()}}
	FeedbackVerify  = Rules{"Content": {NotEmpty()}}
	ChatVerify      = Rules{"Text": {NotEmpty()}}
	// ContextChatVerify 对话上下文聊天验证规则
	// 用于验证带有对话上下文的聊天请求参数
	ContextChatVerify = Rules{
		"Text": {NotEmpty()}, // 当前消息文本不能为空
	}
	TtsVerify           = Rules{"Text": {NotEmpty()}, "SpeechRate": {Ge("0.5"), <PERSON>("2.0")}}
	ResetPasswordVerify = Rules{"Phone": {NotEmpty()}, "Code": {NotEmpty()}, "Password": {NotEmpty()}}
)

package utils

import (
	"regexp"
	"strings"
)

func PhoneValidation(phone string) bool {
	// 支持国际格式的手机号验证正则表达式
	re := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	return re.MatchString(phone)
}

func EmailValidation(email string) bool {
	// 正则表达式检查邮箱格式
	re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return re.MatchString(email)
}

// 检查手机号是否是国内的
func isDomesticPhoneNumber(phoneNumber string) bool {
	// 国内手机号正则
	domesticRegex := regexp.MustCompile(`^(1[3-9][0-9]{9}|861[3-9][0-9]{9})$`)
	return domesticRegex.MatchString(phoneNumber)
}
func IsDomesticPhoneNumberWithIntl(phoneNumber string) bool {
	// 去掉国际代码前缀
	if strings.HasPrefix(phoneNumber, "+86") {
		phoneNumber = phoneNumber[3:] // 去掉 "+86"
	}
	// 调用国内手机号检测
	return isDomesticPhoneNumber(phoneNumber)
}

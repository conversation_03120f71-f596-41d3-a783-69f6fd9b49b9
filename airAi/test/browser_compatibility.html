<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器兼容性检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .check-item.pass {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .check-item.fail {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .check-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .status.pass { background: #28a745; color: white; }
        .status.fail { background: #dc3545; color: white; }
        .status.warning { background: #ffc107; color: black; }
        .solution {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .solution h4 {
            margin-top: 0;
            color: #004085;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 浏览器兼容性检查</h1>
        <p>检查您的浏览器是否支持WebSocket音频识别功能</p>
    </div>

    <div class="container">
        <h3>🌐 基本环境信息</h3>
        <div class="info-grid">
            <div class="info-item">
                <strong>浏览器:</strong><br>
                <span id="browserInfo">检测中...</span>
            </div>
            <div class="info-item">
                <strong>协议:</strong><br>
                <span id="protocolInfo">检测中...</span>
            </div>
            <div class="info-item">
                <strong>主机:</strong><br>
                <span id="hostInfo">检测中...</span>
            </div>
            <div class="info-item">
                <strong>用户代理:</strong><br>
                <span id="userAgentInfo">检测中...</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🔧 API支持检查</h3>
        <div id="apiChecks">
            <div class="check-item" id="check-navigator">
                <span>navigator 对象</span>
                <span class="status">检查中...</span>
            </div>
            <div class="check-item" id="check-mediaDevices">
                <span>navigator.mediaDevices</span>
                <span class="status">检查中...</span>
            </div>
            <div class="check-item" id="check-getUserMedia">
                <span>getUserMedia API</span>
                <span class="status">检查中...</span>
            </div>
            <div class="check-item" id="check-mediaRecorder">
                <span>MediaRecorder API</span>
                <span class="status">检查中...</span>
            </div>
            <div class="check-item" id="check-websocket">
                <span>WebSocket API</span>
                <span class="status">检查中...</span>
            </div>
            <div class="check-item" id="check-https">
                <span>HTTPS 协议</span>
                <span class="status">检查中...</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🎵 音频格式支持</h3>
        <div id="formatChecks">
            <p>检测中...</p>
        </div>
    </div>

    <div class="container">
        <h3>🧪 功能测试</h3>
        <button onclick="testMicrophoneAccess()">测试麦克风访问</button>
        <button onclick="testAudioRecording()">测试音频录制</button>
        <button onclick="testWebSocketConnection()">测试WebSocket连接</button>
        <div id="testResults" style="margin-top: 15px;"></div>
    </div>

    <div id="solutionContainer"></div>

    <script>
        let testResults = [];

        function updateCheckItem(id, status, message = '') {
            const item = document.getElementById(id);
            const statusSpan = item.querySelector('.status');
            
            item.className = `check-item ${status}`;
            statusSpan.className = `status ${status}`;
            statusSpan.textContent = status === 'pass' ? '✅ 支持' : 
                                   status === 'fail' ? '❌ 不支持' : 
                                   '⚠️ 警告';
            
            if (message) {
                const existingMessage = item.querySelector('.message');
                if (existingMessage) {
                    existingMessage.remove();
                }
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.fontSize = '12px';
                messageDiv.style.marginTop = '5px';
                messageDiv.textContent = message;
                item.appendChild(messageDiv);
            }
        }

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `check-item ${type}`;
            resultDiv.innerHTML = `
                <span>${message}</span>
                <span class="status ${type}">${new Date().toLocaleTimeString()}</span>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function checkBasicEnvironment() {
            // 浏览器信息
            const browserInfo = navigator.userAgent.includes('Chrome') ? 'Chrome' :
                              navigator.userAgent.includes('Firefox') ? 'Firefox' :
                              navigator.userAgent.includes('Safari') ? 'Safari' :
                              navigator.userAgent.includes('Edge') ? 'Edge' : '未知';
            
            document.getElementById('browserInfo').textContent = browserInfo;
            document.getElementById('protocolInfo').textContent = location.protocol;
            document.getElementById('hostInfo').textContent = location.host;
            document.getElementById('userAgentInfo').textContent = navigator.userAgent.substring(0, 50) + '...';
        }

        function checkAPISupport() {
            // Navigator 对象
            updateCheckItem('check-navigator', navigator ? 'pass' : 'fail');

            // MediaDevices
            updateCheckItem('check-mediaDevices', 
                navigator.mediaDevices ? 'pass' : 'fail',
                navigator.mediaDevices ? '' : '可能需要HTTPS协议');

            // getUserMedia
            const hasGetUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia ||
                                  navigator.getUserMedia || navigator.webkitGetUserMedia || 
                                  navigator.mozGetUserMedia || navigator.msGetUserMedia;
            updateCheckItem('check-getUserMedia', hasGetUserMedia ? 'pass' : 'fail');

            // MediaRecorder
            updateCheckItem('check-mediaRecorder', window.MediaRecorder ? 'pass' : 'fail');

            // WebSocket
            updateCheckItem('check-websocket', window.WebSocket ? 'pass' : 'fail');

            // HTTPS
            const isSecure = location.protocol === 'https:' || 
                           location.hostname === 'localhost' || 
                           location.hostname === '127.0.0.1';
            updateCheckItem('check-https', isSecure ? 'pass' : 'warning',
                isSecure ? '' : '建议使用HTTPS协议以确保麦克风访问');
        }

        function checkAudioFormats() {
            const formatChecks = document.getElementById('formatChecks');
            
            if (!window.MediaRecorder) {
                formatChecks.innerHTML = '<p class="check-item fail">MediaRecorder不支持，无法检查音频格式</p>';
                return;
            }

            const formats = [
                'audio/webm;codecs=opus',
                'audio/webm;codecs=vp8,opus',
                'audio/webm',
                'audio/mp4',
                'audio/ogg;codecs=opus',
                'audio/wav',
                'audio/mpeg'
            ];

            let html = '';
            formats.forEach(format => {
                const supported = MediaRecorder.isTypeSupported(format);
                html += `
                    <div class="check-item ${supported ? 'pass' : 'fail'}">
                        <span>${format}</span>
                        <span class="status ${supported ? 'pass' : 'fail'}">
                            ${supported ? '✅ 支持' : '❌ 不支持'}
                        </span>
                    </div>
                `;
            });

            formatChecks.innerHTML = html;
        }

        async function testMicrophoneAccess() {
            try {
                addTestResult('开始测试麦克风访问...', 'warning');
                
                const getUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia ||
                                   navigator.getUserMedia || navigator.webkitGetUserMedia || 
                                   navigator.mozGetUserMedia || navigator.msGetUserMedia;

                if (!getUserMedia) {
                    throw new Error('getUserMedia API不支持');
                }

                const stream = await (navigator.mediaDevices ? 
                    navigator.mediaDevices.getUserMedia({ audio: true }) :
                    new Promise((resolve, reject) => {
                        getUserMedia.call(navigator, { audio: true }, resolve, reject);
                    }));

                addTestResult('✅ 麦克风访问成功', 'pass');
                
                // 获取音频轨道信息
                const audioTracks = stream.getAudioTracks();
                if (audioTracks.length > 0) {
                    const track = audioTracks[0];
                    addTestResult(`音频设备: ${track.label || '默认麦克风'}`, 'pass');
                }

                // 清理资源
                stream.getTracks().forEach(track => track.stop());

            } catch (error) {
                addTestResult(`❌ 麦克风访问失败: ${error.message}`, 'fail');
                
                if (error.name === 'NotAllowedError') {
                    addTestResult('提示: 请允许浏览器访问麦克风', 'warning');
                } else if (error.name === 'NotFoundError') {
                    addTestResult('提示: 未找到麦克风设备', 'warning');
                }
            }
        }

        async function testAudioRecording() {
            try {
                addTestResult('开始测试音频录制...', 'warning');
                
                if (!window.MediaRecorder) {
                    throw new Error('MediaRecorder API不支持');
                }

                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const recorder = new MediaRecorder(stream);
                
                let dataReceived = false;
                recorder.ondataavailable = (event) => {
                    dataReceived = true;
                    addTestResult(`录制数据: ${event.data.size} 字节`, 'pass');
                };

                recorder.start();
                setTimeout(() => {
                    recorder.stop();
                    stream.getTracks().forEach(track => track.stop());
                    
                    if (dataReceived) {
                        addTestResult('✅ 音频录制测试成功', 'pass');
                    } else {
                        addTestResult('⚠️ 未接收到录制数据', 'warning');
                    }
                }, 2000);

            } catch (error) {
                addTestResult(`❌ 音频录制测试失败: ${error.message}`, 'fail');
            }
        }

        async function testWebSocketConnection() {
            try {
                addTestResult('开始测试WebSocket连接...', 'warning');
                
                const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${location.host}/v1/ws/audioRecognize`;
                
                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    addTestResult('✅ WebSocket连接成功', 'pass');
                    ws.close();
                };

                ws.onerror = (error) => {
                    addTestResult('❌ WebSocket连接失败', 'fail');
                };

                ws.onclose = (event) => {
                    if (event.code === 1000) {
                        addTestResult('WebSocket连接正常关闭', 'pass');
                    }
                };

            } catch (error) {
                addTestResult(`❌ WebSocket测试失败: ${error.message}`, 'fail');
            }
        }

        function generateSolutions() {
            const issues = [];
            
            // 检查各种问题
            if (!navigator.mediaDevices) {
                issues.push({
                    problem: 'navigator.mediaDevices 不可用',
                    solutions: [
                        '使用HTTPS协议访问页面',
                        '更新浏览器到最新版本',
                        '使用Chrome、Firefox、Safari等现代浏览器'
                    ]
                });
            }

            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                issues.push({
                    problem: '非HTTPS协议限制',
                    solutions: [
                        '配置HTTPS证书',
                        '使用反向代理(如Nginx)提供HTTPS',
                        '在localhost环境下测试'
                    ]
                });
            }

            if (!window.MediaRecorder) {
                issues.push({
                    problem: 'MediaRecorder API不支持',
                    solutions: [
                        '更新浏览器版本',
                        '使用支持MediaRecorder的浏览器',
                        '考虑使用polyfill库'
                    ]
                });
            }

            // 生成解决方案HTML
            if (issues.length > 0) {
                let html = '<div class="container"><h3>🔧 解决方案建议</h3>';
                
                issues.forEach(issue => {
                    html += `
                        <div class="solution">
                            <h4>问题: ${issue.problem}</h4>
                            <ul>
                                ${issue.solutions.map(solution => `<li>${solution}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                });
                
                html += '</div>';
                document.getElementById('solutionContainer').innerHTML = html;
            }
        }

        // 页面加载时执行检查
        window.addEventListener('load', () => {
            checkBasicEnvironment();
            checkAPISupport();
            checkAudioFormats();
            generateSolutions();
        });
    </script>
</body>
</html>

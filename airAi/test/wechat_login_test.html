<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background-color: #07c160;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #06ad56;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信登录功能测试</h1>
        
        <div class="form-group">
            <label for="apiBase">API基础URL:</label>
            <input type="text" id="apiBase" value="http://localhost:8888/api" placeholder="http://localhost:8888/api">
        </div>
        
        <div class="form-group">
            <label for="language">语言:</label>
            <select id="language">
                <option value="zh_CN">中文</option>
                <option value="en">English</option>
                <option value="id">Bahasa Indonesia</option>
                <option value="hi">हिन्दी</option>
            </select>
        </div>
        
        <h2>测试步骤</h2>
        
        <h3>1. 获取微信授权URL</h3>
        <button class="button" onclick="getAuthUrl()">获取授权URL</button>
        <button class="button" onclick="redirectToWechat()">直接跳转微信授权</button>
        
        <h3>2. 模拟授权码登录</h3>
        <div class="form-group">
            <label for="authCode">授权码 (code):</label>
            <input type="text" id="authCode" placeholder="从微信回调中获取的code参数">
        </div>
        <div class="form-group">
            <label for="stateParam">状态参数 (state):</label>
            <input type="text" id="stateParam" placeholder="可选的state参数">
        </div>
        <button class="button" onclick="loginWithCode()">使用授权码登录</button>
        
        <h3>3. 处理URL回调</h3>
        <button class="button" onclick="handleCallback()">处理当前URL的回调参数</button>
        
        <div id="result" class="result" style="display: none;">
            <h4>响应结果:</h4>
            <div id="resultContent" class="code-block"></div>
        </div>
    </div>

    <script>
        function getApiBase() {
            return document.getElementById('apiBase').value || 'http://localhost:8888/api';
        }
        
        function getLanguage() {
            return document.getElementById('language').value || 'zh_CN';
        }
        
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            contentDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        async function getAuthUrl() {
            try {
                const response = await fetch(`${getApiBase()}/v1/wechat/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept-Language': getLanguage()
                    },
                    body: JSON.stringify({
                        state: 'test_' + Date.now()
                    })
                });
                
                const result = await response.json();
                showResult(result, result.code !== 0);
                
                if (result.code === 0) {
                    // 显示授权URL，用户可以手动访问
                    alert('授权URL已生成，请查看结果区域。您可以复制URL在新窗口中打开。');
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }
        
        function redirectToWechat() {
            // 直接跳转到微信授权重定向端点
            const state = 'redirect_' + Date.now();
            window.location.href = `${getApiBase()}/v1/wechat/redirect?state=${state}&lang=${getLanguage()}`;
        }
        
        async function loginWithCode() {
            const code = document.getElementById('authCode').value;
            const state = document.getElementById('stateParam').value;
            
            if (!code) {
                alert('请输入授权码');
                return;
            }
            
            try {
                const response = await fetch(`${getApiBase()}/v1/wechat/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept-Language': getLanguage()
                    },
                    body: JSON.stringify({
                        code: code,
                        state: state
                    })
                });
                
                const result = await response.json();
                showResult(result, result.code !== 0);
                
                if (result.code === 0) {
                    alert('登录成功！Token已在结果中显示。');
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }
        
        function handleCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const error = urlParams.get('error');
            const errorDescription = urlParams.get('error_description');
            
            if (error) {
                showResult({
                    error: error,
                    error_description: errorDescription,
                    message: '用户取消了授权或发生其他错误'
                }, true);
                return;
            }
            
            if (code) {
                // 自动填充授权码
                document.getElementById('authCode').value = code;
                if (state) {
                    document.getElementById('stateParam').value = state;
                }
                
                showResult({
                    message: '检测到回调参数',
                    code: code,
                    state: state,
                    note: '授权码已自动填充到表单中，您可以点击"使用授权码登录"按钮继续'
                });
            } else {
                showResult({
                    message: '当前URL中没有检测到微信回调参数',
                    current_url: window.location.href
                });
            }
        }
        
        // 页面加载时自动检查回调参数
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('code') || urlParams.get('error')) {
                handleCallback();
            }
        };
    </script>
</body>
</html>

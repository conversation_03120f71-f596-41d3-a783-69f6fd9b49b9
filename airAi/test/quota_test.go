package test

import (
	"testing"
	"time"

	"airAi/service/airport"
	"model/airpods"

	"github.com/stretchr/testify/assert"
)

// TestQuotaModel 测试配额模型的基本功能
func TestQuotaModel(t *testing.T) {
	// 创建测试配额
	quota := &airpods.Quota{
		Uid:                1,
		MonthlyQuota:       300,
		CurrentUsage:       50,
		ExtraPurchases:     100,
		LastResetDate:      time.Now(),
		SubscriptionStatus: 1,
		SubscriptionExpiry: time.Now().AddDate(0, 1, 0), // 一个月后过期
		IsActive:           true,
	}

	// 测试总配额计算
	totalQuota := quota.GetTotalQuota()
	assert.Equal(t, 400, totalQuota, "总配额应该是基础配额300 + 额外购买100 = 400")

	// 测试剩余配额计算
	remaining := quota.GetRemainingQuota()
	assert.Equal(t, 350, remaining, "剩余配额应该是400 - 50 = 350")

	// 测试是否可以使用
	canUse := quota.CanUse()
	assert.True(t, canUse, "配额激活且有剩余，应该可以使用")

	// 测试订阅是否有效
	isActive := quota.IsSubscriptionActive()
	assert.True(t, isActive, "订阅状态为1且未过期，应该有效")
}

// TestQuotaModelEdgeCases 测试配额模型的边界情况
func TestQuotaModelEdgeCases(t *testing.T) {
	// 测试配额用完的情况
	quota := &airpods.Quota{
		Uid:                1,
		MonthlyQuota:       300,
		CurrentUsage:       400, // 使用量超过总配额
		ExtraPurchases:     100,
		LastResetDate:      time.Now(),
		SubscriptionStatus: 1,
		SubscriptionExpiry: time.Now().AddDate(0, 1, 0),
		IsActive:           true,
	}

	remaining := quota.GetRemainingQuota()
	assert.Equal(t, 0, remaining, "使用量超过总配额时，剩余配额应该为0")

	canUse := quota.CanUse()
	assert.False(t, canUse, "配额用完时，不应该可以使用")

	// 测试订阅过期的情况
	expiredQuota := &airpods.Quota{
		Uid:                1,
		MonthlyQuota:       300,
		CurrentUsage:       50,
		ExtraPurchases:     0,
		LastResetDate:      time.Now(),
		SubscriptionStatus: 1,
		SubscriptionExpiry: time.Now().AddDate(0, -1, 0), // 一个月前过期
		IsActive:           true,
	}

	totalQuota := expiredQuota.GetTotalQuota()
	assert.Equal(t, 0, totalQuota, "订阅过期时，总配额应该只包含额外购买的配额")

	isActive := expiredQuota.IsSubscriptionActive()
	assert.False(t, isActive, "订阅过期时，应该不活跃")

	// 测试配额未激活的情况
	inactiveQuota := &airpods.Quota{
		Uid:                1,
		MonthlyQuota:       300,
		CurrentUsage:       50,
		ExtraPurchases:     100,
		LastResetDate:      time.Now(),
		SubscriptionStatus: 1,
		SubscriptionExpiry: time.Now().AddDate(0, 1, 0),
		IsActive:           false, // 配额未激活
	}

	canUse = inactiveQuota.CanUse()
	assert.False(t, canUse, "配额未激活时，不应该可以使用")
}

// TestQuotaConstants 测试配额相关常量
func TestQuotaConstants(t *testing.T) {
	assert.Equal(t, 300, airport.DefaultMonthlyQuota, "默认月度配额应该是300")
	assert.Equal(t, 70.0, airport.MonthlySubscriptionFee, "月度订阅费用应该是70元")
	assert.Equal(t, 50, airport.ExtraQuotaUnit, "额外配额单位应该是50次")
	assert.Equal(t, 10.0, airport.ExtraQuotaPrice, "额外配额价格应该是10元")

	assert.Equal(t, 1, airport.UsageTypeChat, "聊天使用类型应该是1")
	assert.Equal(t, 2, airport.UsageTypeASR, "语音识别使用类型应该是2")
	assert.Equal(t, 3, airport.UsageTypeTTS, "文字转语音使用类型应该是3")

	assert.Equal(t, 1, airport.PurchaseTypeExtraQuota, "额外配额购买类型应该是1")
	assert.Equal(t, 2, airport.PurchaseTypeMonthlySubscription, "月度订阅购买类型应该是2")

	assert.Equal(t, 0, airport.PaymentStatusPending, "待支付状态应该是0")
	assert.Equal(t, 1, airport.PaymentStatusPaid, "已支付状态应该是1")
	assert.Equal(t, 2, airport.PaymentStatusCancelled, "已取消状态应该是2")
	assert.Equal(t, 3, airport.PaymentStatusRefunded, "已退款状态应该是3")
}

// TestPurchaseRecordModel 测试购买记录模型
func TestPurchaseRecordModel(t *testing.T) {
	record := &airpods.PurchaseRecord{
		Uid:           1,
		PurchaseType:  airport.PurchaseTypeExtraQuota,
		PurchaseUnits: 2,
		Amount:        20.0,
		Status:        airport.PaymentStatusPaid,
		PaymentMethod: "system",
		OrderID:       "EQ_1_1234567890",
		Description:   "购买额外配额 2 单位 (100 次)",
		ClientIP:      "***********",
	}

	// 测试购买类型文本
	typeText := record.GetPurchaseTypeText()
	assert.Equal(t, "额外配额", typeText, "购买类型1应该对应'额外配额'")

	// 测试状态文本
	statusText := record.GetStatusText()
	assert.Equal(t, "已支付", statusText, "状态1应该对应'已支付'")

	// 测试月度订阅记录
	subscriptionRecord := &airpods.PurchaseRecord{
		PurchaseType: airport.PurchaseTypeMonthlySubscription,
		Status:       airport.PaymentStatusPending,
	}

	typeText = subscriptionRecord.GetPurchaseTypeText()
	assert.Equal(t, "月度订阅", typeText, "购买类型2应该对应'月度订阅'")

	statusText = subscriptionRecord.GetStatusText()
	assert.Equal(t, "待支付", statusText, "状态0应该对应'待支付'")
}

// TestUsageLogModel 测试使用日志模型
func TestUsageLogModel(t *testing.T) {
	log := &airpods.QuotaUsageLog{
		Uid:            1,
		UsageType:      airport.UsageTypeChat,
		RequestPath:    "/v1/syncChat",
		RequestMethod:  "POST",
		ClientIP:       "***********",
		UserAgent:      "Mozilla/5.0",
		UsageDate:      time.Now(),
		Success:        true,
		ErrorMessage:   "",
		ResponseTime:   1500,
		ConversationID: "conv_123456",
		RequestSize:    100,
		ResponseSize:   500,
	}

	// 测试使用类型文本
	typeText := log.GetUsageTypeText()
	assert.Equal(t, "聊天", typeText, "使用类型1应该对应'聊天'")

	// 测试成功状态文本
	successText := log.GetSuccessText()
	assert.Equal(t, "成功", successText, "成功状态应该对应'成功'")

	// 测试失败情况
	failedLog := &airpods.QuotaUsageLog{
		UsageType: airport.UsageTypeASR,
		Success:   false,
	}

	typeText = failedLog.GetUsageTypeText()
	assert.Equal(t, "语音识别", typeText, "使用类型2应该对应'语音识别'")

	successText = failedLog.GetSuccessText()
	assert.Equal(t, "失败", successText, "失败状态应该对应'失败'")
}

// TestQuotaCalculations 测试配额计算逻辑
func TestQuotaCalculations(t *testing.T) {
	// 测试购买额外配额的计算
	units := 3
	expectedAmount := float64(units) * airport.ExtraQuotaPrice
	assert.Equal(t, 30.0, expectedAmount, "购买3单位额外配额应该花费30元")

	expectedQuota := units * airport.ExtraQuotaUnit
	assert.Equal(t, 150, expectedQuota, "购买3单位应该获得150次配额")

	// 测试月度订阅费用
	assert.Equal(t, 70.0, airport.MonthlySubscriptionFee, "月度订阅费用应该是70元")
}

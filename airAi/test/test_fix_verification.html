<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>getUserMedia修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .test-item.pass {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .test-item.fail {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .test-item.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .status.pass { background: #28a745; color: white; }
        .status.fail { background: #dc3545; color: white; }
        .status.warning { background: #ffc107; color: black; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 getUserMedia修复验证</h1>
        <p>此页面用于验证getUserMedia错误修复是否生效</p>
    </div>

    <div class="container">
        <h3>🔍 环境检测</h3>
        <div id="environmentTests">
            <div class="test-item" id="test-protocol">
                <span>协议检查</span>
                <span class="status">检测中...</span>
            </div>
            <div class="test-item" id="test-navigator">
                <span>navigator对象</span>
                <span class="status">检测中...</span>
            </div>
            <div class="test-item" id="test-mediaDevices">
                <span>navigator.mediaDevices</span>
                <span class="status">检测中...</span>
            </div>
            <div class="test-item" id="test-getUserMedia">
                <span>getUserMedia API</span>
                <span class="status">检测中...</span>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🧪 修复功能测试</h3>
        <button onclick="testOriginalMethod()">测试原始方法（可能失败）</button>
        <button onclick="testCompatMethod()">测试兼容性方法（应该成功）</button>
        <button onclick="testErrorHandling()">测试错误处理</button>
        
        <div id="testResults" style="margin-top: 15px;"></div>
    </div>

    <div class="container">
        <h3>📋 修复前后对比</h3>
        
        <h4>修复前的代码（会出错）：</h4>
        <div class="code-block">
// 直接调用，没有检查API是否存在
const stream = await navigator.mediaDevices.getUserMedia({
    audio: true
});
        </div>

        <h4>修复后的代码（兼容性处理）：</h4>
        <div class="code-block">
// 1. 先检查API支持
function checkAudioSupport() {
    const issues = [];
    if (!navigator.mediaDevices) {
        issues.push('navigator.mediaDevices 不可用');
    }
    return issues;
}

// 2. 兼容性处理函数
async function getUserMediaCompat(constraints) {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        return await navigator.mediaDevices.getUserMedia(constraints);
    }
    
    // 旧版浏览器兼容
    const getUserMedia = navigator.getUserMedia || 
                       navigator.webkitGetUserMedia || 
                       navigator.mozGetUserMedia;
    
    if (getUserMedia) {
        return new Promise((resolve, reject) => {
            getUserMedia.call(navigator, constraints, resolve, reject);
        });
    }
    
    throw new Error('此浏览器不支持音频录制功能');
}

// 3. 安全调用
const supportIssues = checkAudioSupport();
if (supportIssues.length > 0) {
    // 显示错误信息和解决建议
    return;
}

const stream = await getUserMediaCompat({ audio: true });
        </div>
    </div>

    <script>
        // 复制修复后的函数
        function checkAudioSupport() {
            const issues = [];
            
            if (!navigator.mediaDevices) {
                issues.push('navigator.mediaDevices 不可用');
            }
            
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                issues.push('getUserMedia API 不支持');
            }
            
            if (location.protocol !== 'https:' && 
                location.hostname !== 'localhost' && 
                location.hostname !== '127.0.0.1') {
                issues.push('非HTTPS协议可能限制麦克风访问');
            }
            
            return issues;
        }

        async function getUserMediaCompat(constraints) {
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                return await navigator.mediaDevices.getUserMedia(constraints);
            }
            
            const getUserMedia = navigator.getUserMedia || 
                               navigator.webkitGetUserMedia || 
                               navigator.mozGetUserMedia || 
                               navigator.msGetUserMedia;
            
            if (getUserMedia) {
                return new Promise((resolve, reject) => {
                    getUserMedia.call(navigator, constraints, resolve, reject);
                });
            }
            
            throw new Error('此浏览器不支持音频录制功能');
        }

        function updateTestItem(id, status, message = '') {
            const item = document.getElementById(id);
            const statusSpan = item.querySelector('.status');
            
            item.className = `test-item ${status}`;
            statusSpan.className = `status ${status}`;
            statusSpan.textContent = status === 'pass' ? '✅ 通过' : 
                                   status === 'fail' ? '❌ 失败' : 
                                   '⚠️ 警告';
            
            if (message) {
                const existingMessage = item.querySelector('.message');
                if (existingMessage) {
                    existingMessage.remove();
                }
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message';
                messageDiv.style.fontSize = '12px';
                messageDiv.style.marginTop = '5px';
                messageDiv.textContent = message;
                item.appendChild(messageDiv);
            }
        }

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-item ${type}`;
            resultDiv.innerHTML = `
                <span>${message}</span>
                <span class="status ${type}">${new Date().toLocaleTimeString()}</span>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function runEnvironmentTests() {
            // 协议检查
            const isSecure = location.protocol === 'https:' || 
                           location.hostname === 'localhost' || 
                           location.hostname === '127.0.0.1';
            updateTestItem('test-protocol', isSecure ? 'pass' : 'warning', 
                `当前协议: ${location.protocol}`);

            // Navigator对象
            updateTestItem('test-navigator', navigator ? 'pass' : 'fail');

            // MediaDevices
            updateTestItem('test-mediaDevices', 
                navigator.mediaDevices ? 'pass' : 'fail',
                navigator.mediaDevices ? '' : '可能需要HTTPS协议');

            // getUserMedia
            const hasGetUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia ||
                                  navigator.getUserMedia || navigator.webkitGetUserMedia || 
                                  navigator.mozGetUserMedia || navigator.msGetUserMedia;
            updateTestItem('test-getUserMedia', hasGetUserMedia ? 'pass' : 'fail');
        }

        async function testOriginalMethod() {
            addTestResult('测试原始方法（直接调用）...', 'warning');
            
            try {
                // 模拟原始的直接调用方式
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                addTestResult('✅ 原始方法成功（环境支持良好）', 'pass');
                stream.getTracks().forEach(track => track.stop());
            } catch (error) {
                addTestResult(`❌ 原始方法失败: ${error.message}`, 'fail');
                addTestResult('这就是为什么需要兼容性处理的原因', 'warning');
            }
        }

        async function testCompatMethod() {
            addTestResult('测试兼容性方法...', 'warning');
            
            try {
                // 先检查支持情况
                const issues = checkAudioSupport();
                if (issues.length > 0) {
                    addTestResult('检测到兼容性问题:', 'warning');
                    issues.forEach(issue => {
                        addTestResult(`  • ${issue}`, 'warning');
                    });
                }

                // 使用兼容性方法
                const stream = await getUserMediaCompat({ audio: true });
                addTestResult('✅ 兼容性方法成功！', 'pass');
                addTestResult('麦克风访问正常，修复生效', 'pass');
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                addTestResult(`兼容性方法结果: ${error.message}`, 'fail');
                
                if (error.message.includes('不支持')) {
                    addTestResult('建议: 更新浏览器或使用HTTPS', 'warning');
                }
            }
        }

        function testErrorHandling() {
            addTestResult('测试错误处理机制...', 'warning');
            
            // 检查各种错误情况
            const issues = checkAudioSupport();
            
            if (issues.length === 0) {
                addTestResult('✅ 环境检查通过，无需错误处理', 'pass');
            } else {
                addTestResult('检测到以下问题:', 'warning');
                issues.forEach(issue => {
                    addTestResult(`  • ${issue}`, 'warning');
                });
                
                addTestResult('错误处理机制正常工作', 'pass');
            }
            
            // 提供解决建议
            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                addTestResult('💡 建议: 配置HTTPS以获得最佳兼容性', 'warning');
            }
        }

        // 页面加载时运行环境测试
        window.addEventListener('load', () => {
            runEnvironmentTests();
            
            // 显示总结
            setTimeout(() => {
                const issues = checkAudioSupport();
                if (issues.length === 0) {
                    addTestResult('🎉 环境检查完成，修复方案应该能正常工作', 'pass');
                } else {
                    addTestResult('⚠️ 检测到兼容性问题，但修复方案会处理这些问题', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>

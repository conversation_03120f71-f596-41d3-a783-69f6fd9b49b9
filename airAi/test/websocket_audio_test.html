<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket音频识别测试 - 增强版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .container {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .container h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        button.success {
            background: #28a745;
        }

        button.success:hover {
            background: #218838;
        }

        button.danger {
            background: #dc3545;
        }

        button.danger:hover {
            background: #c82333;
        }

        .status {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
            border-left: 4px solid;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .status.recording {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .status.uploading {
            background: #cce7ff;
            color: #004085;
            border-left-color: #007bff;
        }

        .file-upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #0056b3;
            background: #e6f3ff;
        }

        .file-upload-area.dragover {
            border-color: #28a745;
            background: #e6ffe6;
        }

        .file-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .audio-controls {
            margin: 15px 0;
            text-align: center;
        }

        audio {
            width: 100%;
            max-width: 400px;
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .data-transfer-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .results {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-item {
            margin: 8px 0;
            padding: 12px;
            border-left: 4px solid;
            border-radius: 4px;
            font-size: 14px;
        }

        .result-item.info {
            border-left-color: #007bff;
            background: #f8f9ff;
        }

        .result-item.error {
            border-left-color: #dc3545;
            background: #fff5f5;
            color: #721c24;
        }

        .result-item.partial {
            border-left-color: #ffc107;
            background: #fffbf0;
            color: #856404;
        }

        .result-item.final {
            border-left-color: #28a745;
            background: #f0fff4;
            color: #155724;
        }

        .result-item.translation {
            border-left-color: #6f42c1;
            background: #f8f5ff;
            color: #4a2c7a;
        }

        .timestamp {
            font-size: 11px;
            color: #6c757d;
            font-weight: normal;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }

        .instruction {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #004085;
        }

        .instruction h4 {
            margin-top: 0;
            color: #003d82;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>WebSocket音频识别测试 - 增强版</h1>
        <p>支持实时录音和音频文件上传的语音识别测试工具</p>
    </div>

    <!-- 使用说明 -->
    <div class="instruction">
        <h4>📋 使用说明</h4>
        <ol>
            <li><strong>连接WebSocket</strong>：首先点击"连接WebSocket"建立与服务器的连接</li>
            <li><strong>选择音频输入方式</strong>：
                <ul>
                    <li>实时录音：使用麦克风进行实时语音识别</li>
                    <li>文件上传：选择本地音频文件进行识别（支持mp3、wav、m4a等格式）</li>
                </ul>
            </li>
            <li><strong>查看结果</strong>：识别结果会实时显示在下方，包括原文和翻译</li>
            <li><strong>数据传输监控</strong>：可以查看音频数据的传输状态和统计信息</li>
        </ol>
    </div>

    <!-- WebSocket连接控制 -->
    <div class="container">
        <h3>🔗 WebSocket连接控制</h3>
        <button id="connectBtn" onclick="connectWebSocket()">连接WebSocket</button>
        <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled class="danger">断开连接</button>
        <div id="connectionStatus" class="status disconnected">未连接到服务器</div>
        <div class="data-transfer-info">
            <strong>连接信息：</strong><br>
            <span id="wsUrl">WebSocket URL: 未连接</span><br>
            <span id="connectionTime">连接时间: --</span>
        </div>
    </div>

    <div class="two-column">
        <!-- 实时录音功能 -->
        <div class="container">
            <h3>🎤 实时录音识别</h3>
            <button id="startRecordBtn" onclick="startRecording()" disabled class="success">开始录音</button>
            <button id="stopRecordBtn" onclick="stopRecording()" disabled class="danger">停止录音</button>
            <div id="recordingStatus" class="status">未开始录音</div>

            <div class="data-transfer-info">
                <strong>录音参数：</strong><br>
                采样率: 16000Hz | 声道: 单声道 | 编码: WebM/Opus<br>
                <strong>数据传输：</strong><br>
                <span id="recordingDataInfo">等待开始录音...</span>
            </div>
        </div>

        <!-- 音频文件上传功能 -->
        <div class="container">
            <h3>📁 音频文件上传识别</h3>
            <div class="file-upload-area" id="fileUploadArea"
                onclick="document.getElementById('audioFileInput').click()">
                <div id="uploadPrompt">
                    <p><strong>点击选择音频文件</strong></p>
                    <p>或拖拽文件到此区域</p>
                    <p style="font-size: 12px; color: #6c757d;">
                        支持格式：MP3, WAV, M4A, AAC, OGG<br>
                        最大文件大小：50MB
                    </p>
                </div>
            </div>

            <input type="file" id="audioFileInput" accept="audio/*" style="display: none;"
                onchange="handleFileSelect(event)">

            <div id="fileInfo" class="file-info">
                <strong>文件信息：</strong><br>
                <span id="fileName">文件名: --</span><br>
                <span id="fileSize">文件大小: --</span><br>
                <span id="fileType">文件类型: --</span><br>
                <span id="fileDuration">音频时长: --</span>
            </div>

            <div class="audio-controls" id="audioControls" style="display: none;">
                <audio id="audioPreview" controls></audio><br>
                <button onclick="uploadAudioFile()" id="uploadBtn" disabled class="success">上传并识别</button>
                <button onclick="clearFileSelection()" class="danger">清除选择</button>
            </div>

            <div id="uploadStatus" class="status hidden">准备上传</div>
            <div class="progress-bar" id="uploadProgress" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
    </div>

    <!-- 数据传输监控 -->
    <div class="container">
        <h3>📊 数据传输监控</h3>
        <div class="data-transfer-info" id="transferStats">
            <strong>传输统计：</strong><br>
            发送数据包: <span id="sentPackets">0</span> |
            发送字节数: <span id="sentBytes">0</span> |
            接收消息数: <span id="receivedMessages">0</span><br>
            <strong>最近活动：</strong><br>
            <span id="lastActivity">无活动</span>
        </div>

        <div style="margin-top: 15px;">
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="testAudioRecording()">测试音频录制</button>
            <button onclick="clearResults()">清空结果</button>
            <button onclick="resetStats()">重置统计</button>
        </div>
    </div>

    <!-- 识别结果显示 -->
    <div class="container">
        <h3>🎯 识别结果</h3>
        <div id="results" class="results">
            <p style="text-align: center; color: #6c757d;">等待识别结果...</p>
        </div>
    </div>

    <script>
        // ==================== 全局变量定义 ====================

        let ws = null;                    // WebSocket连接对象
        let mediaRecorder = null;         // 媒体录制器对象
        let audioChunks = [];            // 录音数据块数组
        let isRecording = false;         // 录音状态标志
        let selectedAudioFile = null;    // 选中的音频文件
        let connectionStartTime = null;  // 连接开始时间

        // 统计数据
        let stats = {
            sentPackets: 0,      // 发送的数据包数量
            sentBytes: 0,        // 发送的字节数
            receivedMessages: 0, // 接收的消息数量
            lastActivity: null   // 最后活动时间
        };

        // ==================== WebSocket连接管理 ====================

        /**
         * 建立WebSocket连接
         * 功能：连接到服务器的WebSocket音频识别接口
         */
        function connectWebSocket() {
            // 根据当前协议确定WebSocket协议（HTTP->WS, HTTPS->WSS）
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/v1/ws/audioRecognize`;

            try {
                // 创建WebSocket连接
                ws = new WebSocket(wsUrl);
                connectionStartTime = new Date();

                // 更新连接信息显示
                document.getElementById('wsUrl').textContent = `WebSocket URL: ${wsUrl}`;
                addResult('正在连接WebSocket服务器...', 'info');

                // WebSocket连接成功事件处理
                ws.onopen = () => {
                    updateConnectionStatus('已连接到服务器', 'connected');
                    updateConnectionInfo();
                    enableConnectionControls(true);
                    addResult('✅ WebSocket连接成功，可以开始音频识别', 'info');
                    updateLastActivity('WebSocket连接建立');
                };

                // WebSocket消息接收事件处理
                ws.onmessage = (event) => {
                    stats.receivedMessages++;
                    updateLastActivity('接收到服务器消息');
                    updateTransferStats();

                    try {
                        // 尝试解析JSON格式的识别结果
                        const data = JSON.parse(event.data);
                        handleRecognitionResult(data);
                    } catch (e) {
                        // 如果不是JSON格式，显示原始数据
                        addResult(`📨 接收到原始数据: ${event.data}`, 'info');
                    }
                };

                // WebSocket连接关闭事件处理
                ws.onclose = (event) => {
                    updateConnectionStatus('连接已关闭', 'disconnected');
                    enableConnectionControls(false);

                    // 根据关闭代码显示不同的消息
                    let closeReason = '未知原因';
                    if (event.code === 1000) closeReason = '正常关闭';
                    else if (event.code === 1001) closeReason = '端点离开';
                    else if (event.code === 1006) closeReason = '连接异常断开';

                    addResult(`❌ WebSocket连接关闭: ${event.code} - ${closeReason}`, 'error');
                    updateLastActivity('WebSocket连接关闭');
                };

                // WebSocket错误事件处理
                ws.onerror = (error) => {
                    addResult(`❌ WebSocket连接错误: ${error}`, 'error');
                    updateLastActivity('WebSocket连接错误');
                };

            } catch (error) {
                addResult(`❌ 连接失败: ${error.message}`, 'error');
            }
        }

        /**
         * 断开WebSocket连接
         * 功能：主动关闭WebSocket连接并清理资源
         */
        function disconnectWebSocket() {
            if (ws) {
                // 如果正在录音，先停止录音
                if (isRecording) {
                    stopRecording();
                }

                ws.close(1000, '用户主动断开连接');
                ws = null;
                connectionStartTime = null;

                // 更新UI状态
                document.getElementById('wsUrl').textContent = 'WebSocket URL: 未连接';
                document.getElementById('connectionTime').textContent = '连接时间: --';
                addResult('🔌 已主动断开WebSocket连接', 'info');
            }
        }

        /**
         * 启用/禁用连接相关控件
         * @param {boolean} connected - 是否已连接
         */
        function enableConnectionControls(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('startRecordBtn').disabled = !connected;
            document.getElementById('uploadBtn').disabled = !connected || !selectedAudioFile;
        }

        /**
         * 更新连接信息显示
         */
        function updateConnectionInfo() {
            if (connectionStartTime) {
                const duration = Math.floor((new Date() - connectionStartTime) / 1000);
                document.getElementById('connectionTime').textContent =
                    `连接时间: ${formatDuration(duration)}`;
            }
        }

        // ==================== 实时录音功能 ====================

        /**
         * 开始录音
         * 功能：启动麦克风录音，实时发送音频数据到服务器
         */
        async function startRecording() {
            try {
                // 检查WebSocket连接状态
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    addResult('❌ 请先连接WebSocket服务器', 'error');
                    return;
                }

                // 请求麦克风权限并获取音频流
                addResult('🎤 正在请求麦克风权限...', 'info');

                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,        // 采样率16kHz（服务器要求）
                        channelCount: 1,          // 单声道
                        echoCancellation: true,   // 回声消除
                        noiseSuppression: true,   // 噪声抑制
                        autoGainControl: true     // 自动增益控制
                    }
                });

                addResult('✅ 麦克风权限获取成功', 'info');

                // 检测支持的音频格式
                const supportedFormats = [
                    'audio/webm;codecs=opus',
                    'audio/webm;codecs=vp8,opus',
                    'audio/webm',
                    'audio/mp4',
                    'audio/ogg;codecs=opus',
                    'audio/wav'
                ];

                let selectedFormat = null;
                for (const format of supportedFormats) {
                    if (MediaRecorder.isTypeSupported(format)) {
                        selectedFormat = format;
                        break;
                    }
                }

                if (!selectedFormat) {
                    throw new Error('浏览器不支持任何音频录制格式');
                }

                addResult(`🔧 使用音频格式: ${selectedFormat}`, 'info');

                // 创建媒体录制器
                const options = {
                    mimeType: selectedFormat,
                    audioBitsPerSecond: 128000  // 设置音频比特率
                };

                mediaRecorder = new MediaRecorder(stream, options);
                audioChunks = [];
                let chunkCount = 0;
                let totalBytes = 0;

                // 音频数据可用事件处理
                mediaRecorder.ondataavailable = (event) => {
                    console.log('MediaRecorder ondataavailable:', {
                        dataSize: event.data.size,
                        dataType: event.data.type,
                        timestamp: new Date().toISOString()
                    });

                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        chunkCount++;
                        totalBytes += event.data.size;

                        addResult(`📊 生成音频数据块 #${chunkCount}: ${formatBytes(event.data.size)} (${event.data.type})`, 'info');

                        // 实时发送音频数据到WebSocket服务器
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            try {
                                ws.send(event.data);

                                // 更新统计信息
                                stats.sentPackets++;
                                stats.sentBytes += event.data.size;
                                updateTransferStats();
                                updateLastActivity(`发送音频数据块 #${chunkCount} (${formatBytes(event.data.size)})`);

                                // 更新录音数据信息显示
                                document.getElementById('recordingDataInfo').textContent =
                                    `已发送 ${chunkCount} 个数据块，总计 ${formatBytes(totalBytes)}`;

                                addResult(`📤 已发送数据块 #${chunkCount}: ${formatBytes(event.data.size)}`, 'partial');
                            } catch (error) {
                                addResult(`❌ 发送音频数据失败: ${error.message}`, 'error');
                                stopRecording();
                            }
                        } else {
                            addResult('⚠️ WebSocket连接已断开，无法发送音频数据', 'error');
                            stopRecording();
                        }
                    } else {
                        // 记录空数据块的情况
                        addResult(`⚠️ 接收到空音频数据块 #${chunkCount + 1}`, 'error');
                        console.warn('MediaRecorder generated empty data chunk');
                    }
                };

                // 录音停止事件处理
                mediaRecorder.onstop = () => {
                    // 停止所有音频轨道
                    stream.getTracks().forEach(track => track.stop());

                    // 更新UI状态
                    updateRecordingStatus('录音已停止');
                    document.getElementById('startRecordBtn').disabled = false;
                    document.getElementById('stopRecordBtn').disabled = true;

                    // 发送录音结束信号
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send('stop');
                        updateLastActivity('发送录音停止信号');
                    }

                    addResult(`🎤 录音结束，共发送 ${chunkCount} 个音频数据块`, 'info');
                };

                // 录音停止事件处理
                mediaRecorder.onstop = () => {
                    // 停止所有音频轨道
                    stream.getTracks().forEach(track => track.stop());

                    // 更新UI状态
                    updateRecordingStatus('录音已停止');
                    document.getElementById('startRecordBtn').disabled = false;
                    document.getElementById('stopRecordBtn').disabled = true;

                    // 发送录音结束信号
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send('stop');
                        updateLastActivity('发送录音停止信号');
                    }

                    addResult(`🎤 录音结束，共发送 ${chunkCount} 个音频数据块，总计 ${formatBytes(totalBytes)}`, 'info');
                };

                // 录音错误事件处理
                mediaRecorder.onerror = (event) => {
                    console.error('MediaRecorder error:', event.error);
                    addResult(`❌ 录音错误: ${event.error}`, 'error');
                    stopRecording();
                };

                // 录音开始事件处理
                mediaRecorder.onstart = () => {
                    addResult('🎤 MediaRecorder已启动，开始录音...', 'info');
                    console.log('MediaRecorder started with options:', options);
                };

                // 开始录音（每1000毫秒生成一个数据块，确保有足够的音频数据）
                const timeslice = 1000; // 1秒间隔
                addResult(`🔧 启动录音，数据块间隔: ${timeslice}ms`, 'info');

                mediaRecorder.start(timeslice);
                isRecording = true;

                // 更新UI状态
                updateRecordingStatus('正在录音中...', 'recording');
                document.getElementById('startRecordBtn').disabled = true;
                document.getElementById('stopRecordBtn').disabled = false;
                document.getElementById('recordingDataInfo').textContent = '正在录音，等待数据...';

                addResult('🎤 开始录音，音频数据将实时发送到服务器', 'info');
                updateLastActivity('开始录音');

                // 添加录音状态监控
                setTimeout(() => {
                    if (isRecording && chunkCount === 0) {
                        addResult('⚠️ 录音已启动但未生成数据，请检查麦克风权限和音量', 'error');
                    }
                }, 3000); // 3秒后检查

            } catch (error) {
                // 处理各种可能的错误
                let errorMessage = '录音失败';
                if (error.name === 'NotAllowedError') {
                    errorMessage = '麦克风权限被拒绝，请允许访问麦克风';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = '未找到麦克风设备';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage = '浏览器不支持录音功能';
                } else {
                    errorMessage = `录音失败: ${error.message}`;
                }

                addResult(`❌ ${errorMessage}`, 'error');
                updateLastActivity('录音失败');
            }
        }

        /**
         * 停止录音
         * 功能：停止麦克风录音并清理资源
         */
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                updateLastActivity('停止录音');
                addResult('⏹️ 录音已停止', 'info');
            }
        }

        // ==================== 音频文件上传功能 ====================

        /**
         * 处理文件选择事件
         * @param {Event} event - 文件选择事件
         */
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processSelectedFile(file);
            }
        }

        /**
         * 处理选中的音频文件
         * @param {File} file - 选中的音频文件
         */
        function processSelectedFile(file) {
            // 验证文件类型
            const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/aac', 'audio/ogg', 'audio/mpeg'];
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a|aac|ogg)$/i)) {
                addResult('❌ 不支持的文件格式，请选择音频文件（MP3, WAV, M4A, AAC, OGG）', 'error');
                return;
            }

            // 验证文件大小（最大50MB）
            const maxSize = 50 * 1024 * 1024;
            if (file.size > maxSize) {
                addResult('❌ 文件过大，请选择小于50MB的音频文件', 'error');
                return;
            }

            selectedAudioFile = file;

            // 显示文件信息
            displayFileInfo(file);

            // 创建音频预览
            createAudioPreview(file);

            addResult(`📁 已选择音频文件: ${file.name}`, 'info');
        }

        /**
         * 显示文件信息
         * @param {File} file - 音频文件
         */
        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = `文件名: ${file.name}`;
            document.getElementById('fileSize').textContent = `文件大小: ${formatBytes(file.size)}`;
            document.getElementById('fileType').textContent = `文件类型: ${file.type || '未知'}`;

            // 显示文件信息区域
            document.getElementById('fileInfo').classList.add('show');
            document.getElementById('audioControls').style.display = 'block';

            // 启用上传按钮（如果WebSocket已连接）
            if (ws && ws.readyState === WebSocket.OPEN) {
                document.getElementById('uploadBtn').disabled = false;
            }
        }

        /**
         * 创建音频预览
         * @param {File} file - 音频文件
         */
        function createAudioPreview(file) {
            const audioPreview = document.getElementById('audioPreview');
            const url = URL.createObjectURL(file);
            audioPreview.src = url;

            // 获取音频时长
            audioPreview.addEventListener('loadedmetadata', () => {
                const duration = audioPreview.duration;
                document.getElementById('fileDuration').textContent =
                    `音频时长: ${formatDuration(duration)}`;
            });

            // 清理URL对象
            audioPreview.addEventListener('load', () => {
                URL.revokeObjectURL(url);
            });
        }

        /**
         * 上传音频文件进行识别
         */
        async function uploadAudioFile() {
            if (!selectedAudioFile) {
                addResult('❌ 请先选择音频文件', 'error');
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addResult('❌ 请先连接WebSocket服务器', 'error');
                return;
            }

            try {
                // 更新上传状态
                updateUploadStatus('正在上传音频文件...', 'uploading');
                document.getElementById('uploadProgress').style.display = 'block';
                document.getElementById('uploadBtn').disabled = true;

                addResult(`📤 开始上传音频文件: ${selectedAudioFile.name}`, 'info');
                updateLastActivity('开始上传音频文件');

                // 分块上传音频文件
                await uploadFileInChunks(selectedAudioFile);

            } catch (error) {
                addResult(`❌ 文件上传失败: ${error.message}`, 'error');
                updateUploadStatus('上传失败', 'error');
                updateLastActivity('文件上传失败');
            } finally {
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('uploadProgress').style.display = 'none';
            }
        }

        /**
         * 分块上传文件
         * @param {File} file - 要上传的文件
         */
        async function uploadFileInChunks(file) {
            const chunkSize = 64 * 1024; // 64KB per chunk
            const totalChunks = Math.ceil(file.size / chunkSize);
            let uploadedChunks = 0;

            for (let i = 0; i < totalChunks; i++) {
                const start = i * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const chunk = file.slice(start, end);

                // 发送数据块
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(chunk);

                    // 更新统计信息
                    stats.sentPackets++;
                    stats.sentBytes += chunk.size;
                    uploadedChunks++;

                    // 更新进度
                    const progress = (uploadedChunks / totalChunks) * 100;
                    updateUploadProgress(progress);
                    updateTransferStats();
                    updateLastActivity(`上传进度: ${uploadedChunks}/${totalChunks} 块`);

                    // 添加小延迟避免发送过快
                    await new Promise(resolve => setTimeout(resolve, 10));
                } else {
                    throw new Error('WebSocket连接已断开');
                }
            }

            // 发送上传完成信号
            ws.send('stop');
            updateUploadStatus('上传完成，等待识别结果...', 'uploading');
            addResult(`✅ 文件上传完成，共发送 ${totalChunks} 个数据块`, 'info');
            updateLastActivity('文件上传完成');
        }

        /**
         * 清除文件选择
         */
        function clearFileSelection() {
            selectedAudioFile = null;
            document.getElementById('audioFileInput').value = '';
            document.getElementById('fileInfo').classList.remove('show');
            document.getElementById('audioControls').style.display = 'none';
            document.getElementById('uploadStatus').classList.add('hidden');
            document.getElementById('uploadProgress').style.display = 'none';
            document.getElementById('uploadBtn').disabled = true;

            addResult('🗑️ 已清除文件选择', 'info');
        }

        // ==================== 识别结果处理 ====================

        /**
         * 处理识别结果
         * @param {Object} data - 服务器返回的识别结果
         */
        function handleRecognitionResult(data) {
            let resultType = 'info';
            let message = '';

            if (data.error) {
                resultType = 'error';
                message = `❌ 错误: ${data.message || data.error}`;
            } else if (data.payload && data.payload.output) {
                // 处理ASR识别结果
                const output = data.payload.output;
                if (output.sentence && output.sentence.text) {
                    resultType = output.sentence.end_time ? 'final' : 'partial';
                    const statusIcon = resultType === 'final' ? '✅' : '⏳';
                    message = `${statusIcon} 识别结果: ${output.sentence.text}`;
                }

                // 处理翻译结果
                if (output.translations && output.translations.length > 0) {
                    output.translations.forEach(translation => {
                        if (translation.text) {
                            addResult(`🌐 翻译结果 (${translation.lang}): ${translation.text}`, 'translation');
                        }
                    });
                }

                // 处理转录结果
                if (output.transcription && output.transcription.text) {
                    addResult(`📝 转录结果: ${output.transcription.text}`, 'final');
                }
            } else if (data.isEnd) {
                resultType = 'info';
                message = '🏁 识别结束';
            } else {
                message = `📋 未知结果格式: ${JSON.stringify(data)}`;
            }

            if (message) {
                addResult(message, resultType);
            }
        }

        // ==================== UI更新函数 ====================

        /**
         * 更新连接状态显示
         * @param {string} status - 状态文本
         * @param {string} className - CSS类名
         */
        function updateConnectionStatus(status, className) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        /**
         * 更新录音状态显示
         * @param {string} status - 状态文本
         * @param {string} className - CSS类名
         */
        function updateRecordingStatus(status, className = '') {
            const statusEl = document.getElementById('recordingStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }

        /**
         * 更新上传状态显示
         * @param {string} status - 状态文本
         * @param {string} className - CSS类名
         */
        function updateUploadStatus(status, className = '') {
            const statusEl = document.getElementById('uploadStatus');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
            statusEl.classList.remove('hidden');
        }

        /**
         * 更新上传进度
         * @param {number} progress - 进度百分比 (0-100)
         */
        function updateUploadProgress(progress) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${progress}%`;
        }

        /**
         * 添加结果到显示区域
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型 (info, error, partial, final, translation)
         */
        function addResult(message, type = 'info') {
            const resultsEl = document.getElementById('results');

            // 如果是第一条消息，清除默认提示
            if (resultsEl.children.length === 1 && resultsEl.children[0].tagName === 'P') {
                resultsEl.innerHTML = '';
            }

            const resultEl = document.createElement('div');
            resultEl.className = `result-item ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            resultEl.innerHTML = `
                <span class="timestamp">${timestamp}</span><br>
                ${message}
            `;

            resultsEl.appendChild(resultEl);
            resultsEl.scrollTop = resultsEl.scrollHeight;

            // 限制显示的消息数量，避免页面过长
            if (resultsEl.children.length > 100) {
                resultsEl.removeChild(resultsEl.firstChild);
            }
        }

        /**
         * 更新传输统计信息
         */
        function updateTransferStats() {
            document.getElementById('sentPackets').textContent = stats.sentPackets;
            document.getElementById('sentBytes').textContent = formatBytes(stats.sentBytes);
            document.getElementById('receivedMessages').textContent = stats.receivedMessages;
        }

        /**
         * 更新最后活动信息
         * @param {string} activity - 活动描述
         */
        function updateLastActivity(activity) {
            stats.lastActivity = new Date();
            document.getElementById('lastActivity').textContent =
                `${stats.lastActivity.toLocaleTimeString()} - ${activity}`;
        }

        // ==================== 工具函数 ====================

        /**
         * 格式化字节数为可读格式
         * @param {number} bytes - 字节数
         * @returns {string} 格式化后的字符串
         */
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 格式化时长为可读格式
         * @param {number} seconds - 秒数
         * @returns {string} 格式化后的时长字符串
         */
        function formatDuration(seconds) {
            if (isNaN(seconds)) return '--';
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // ==================== 测试和控制函数 ====================

        /**
         * 发送测试消息
         */
        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMessage = 'test message from client';
                ws.send(testMessage);
                stats.sentPackets++;
                updateTransferStats();
                updateLastActivity('发送测试消息');
                addResult('📤 发送测试消息: ' + testMessage, 'info');
            } else {
                addResult('❌ WebSocket未连接，无法发送测试消息', 'error');
            }
        }

        /**
         * 测试音频录制功能
         */
        async function testAudioRecording() {
            try {
                addResult('🧪 开始测试音频录制功能...', 'info');

                // 检查浏览器支持
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('浏览器不支持音频录制');
                }

                // 检查MediaRecorder支持
                if (!window.MediaRecorder) {
                    throw new Error('浏览器不支持MediaRecorder');
                }

                // 测试音频格式支持
                const formats = [
                    'audio/webm;codecs=opus',
                    'audio/webm;codecs=vp8,opus',
                    'audio/webm',
                    'audio/mp4',
                    'audio/ogg;codecs=opus',
                    'audio/wav'
                ];

                addResult('🔍 检查支持的音频格式:', 'info');
                formats.forEach(format => {
                    const supported = MediaRecorder.isTypeSupported(format);
                    addResult(`  ${supported ? '✅' : '❌'} ${format}`, 'info');
                });

                // 测试麦克风访问
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });

                addResult('✅ 麦克风访问测试成功', 'info');

                // 创建测试录制器
                const testRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                let testDataReceived = false;
                testRecorder.ondataavailable = (event) => {
                    testDataReceived = true;
                    addResult(`✅ 测试录制器生成数据: ${formatBytes(event.data.size)}`, 'info');
                };

                // 开始测试录制
                testRecorder.start(1000);

                // 2秒后停止测试
                setTimeout(() => {
                    testRecorder.stop();
                    stream.getTracks().forEach(track => track.stop());

                    if (testDataReceived) {
                        addResult('✅ 音频录制功能测试通过', 'info');
                    } else {
                        addResult('❌ 音频录制功能测试失败：未生成数据', 'error');
                    }
                }, 2000);

            } catch (error) {
                addResult(`❌ 音频录制测试失败: ${error.message}`, 'error');
            }
        }

        /**
         * 清空识别结果
         */
        function clearResults() {
            document.getElementById('results').innerHTML =
                '<p style="text-align: center; color: #6c757d;">等待识别结果...</p>';
            addResult('🗑️ 已清空识别结果', 'info');
        }

        /**
         * 重置统计数据
         */
        function resetStats() {
            stats = {
                sentPackets: 0,
                sentBytes: 0,
                receivedMessages: 0,
                lastActivity: null
            };
            updateTransferStats();
            document.getElementById('lastActivity').textContent = '无活动';
            document.getElementById('recordingDataInfo').textContent = '等待开始录音...';
            addResult('📊 已重置统计数据', 'info');
        }

        // ==================== 拖拽上传功能 ====================

        /**
         * 初始化拖拽上传功能
         */
        function initDragAndDrop() {
            const uploadArea = document.getElementById('fileUploadArea');

            // 阻止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // 拖拽进入和悬停效果
            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            // 拖拽离开效果
            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            // 处理文件拖拽
            uploadArea.addEventListener('drop', handleDrop, false);
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            document.getElementById('fileUploadArea').classList.add('dragover');
        }

        function unhighlight(e) {
            document.getElementById('fileUploadArea').classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                processSelectedFile(files[0]);
            }
        }

        // ==================== 页面初始化 ====================

        /**
         * 页面加载完成后的初始化
         */
        window.addEventListener('load', () => {
            // 初始化拖拽上传功能
            initDragAndDrop();

            // 定时更新连接时间
            setInterval(() => {
                if (connectionStartTime) {
                    updateConnectionInfo();
                }
            }, 1000);

            // 添加欢迎消息
            addResult('🎉 页面加载完成，可以开始测试WebSocket音频识别功能', 'info');
            addResult('💡 提示：请先点击"连接WebSocket"建立连接', 'info');
        });
    </script>
</body>

</html>
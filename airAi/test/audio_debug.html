<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频录制调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-item {
            margin: 2px 0;
            padding: 2px;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-warning { color: #ffc107; }
        .log-error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>音频录制调试工具</h1>
    
    <div class="container">
        <h3>浏览器兼容性检查</h3>
        <button onclick="checkBrowserSupport()">检查浏览器支持</button>
        <button onclick="checkAudioFormats()">检查音频格式支持</button>
    </div>

    <div class="container">
        <h3>音频设备测试</h3>
        <button onclick="testMicrophoneAccess()">测试麦克风访问</button>
        <button onclick="testAudioRecording()">测试音频录制</button>
        <button onclick="testRealTimeRecording()">测试实时录制</button>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioStream = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = `log-item log-${type}`;
            logItem.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logItem);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function checkBrowserSupport() {
            log('开始检查浏览器支持...', 'info');
            
            // 检查基本API支持
            const checks = [
                { name: 'navigator.mediaDevices', supported: !!navigator.mediaDevices },
                { name: 'getUserMedia', supported: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia) },
                { name: 'MediaRecorder', supported: !!window.MediaRecorder },
                { name: 'WebSocket', supported: !!window.WebSocket },
                { name: 'Blob', supported: !!window.Blob }
            ];

            checks.forEach(check => {
                log(`${check.name}: ${check.supported ? '✅ 支持' : '❌ 不支持'}`, 
                    check.supported ? 'success' : 'error');
            });

            // 检查浏览器信息
            log(`浏览器: ${navigator.userAgent}`, 'info');
            log(`协议: ${location.protocol}`, 'info');
        }

        function checkAudioFormats() {
            log('检查音频格式支持...', 'info');
            
            if (!window.MediaRecorder) {
                log('MediaRecorder不支持，无法检查音频格式', 'error');
                return;
            }

            const formats = [
                'audio/webm;codecs=opus',
                'audio/webm;codecs=vp8,opus',
                'audio/webm',
                'audio/mp4',
                'audio/ogg;codecs=opus',
                'audio/wav',
                'audio/mpeg'
            ];

            formats.forEach(format => {
                const supported = MediaRecorder.isTypeSupported(format);
                log(`${format}: ${supported ? '✅ 支持' : '❌ 不支持'}`, 
                    supported ? 'success' : 'warning');
            });
        }

        async function testMicrophoneAccess() {
            log('测试麦克风访问...', 'info');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                log('✅ 麦克风访问成功', 'success');
                
                // 获取音频轨道信息
                const audioTracks = stream.getAudioTracks();
                if (audioTracks.length > 0) {
                    const track = audioTracks[0];
                    const settings = track.getSettings();
                    log(`音频轨道标签: ${track.label}`, 'info');
                    log(`采样率: ${settings.sampleRate}Hz`, 'info');
                    log(`声道数: ${settings.channelCount}`, 'info');
                    log(`设备ID: ${settings.deviceId}`, 'info');
                }

                // 清理资源
                stream.getTracks().forEach(track => track.stop());
                audioStream = null;

            } catch (error) {
                log(`❌ 麦克风访问失败: ${error.name} - ${error.message}`, 'error');
                
                if (error.name === 'NotAllowedError') {
                    log('提示: 请允许浏览器访问麦克风', 'warning');
                } else if (error.name === 'NotFoundError') {
                    log('提示: 未找到麦克风设备', 'warning');
                }
            }
        }

        async function testAudioRecording() {
            log('测试音频录制...', 'info');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });

                audioStream = stream;
                
                // 尝试不同的音频格式
                const formats = [
                    'audio/webm;codecs=opus',
                    'audio/webm',
                    'audio/mp4'
                ];

                let selectedFormat = null;
                for (const format of formats) {
                    if (MediaRecorder.isTypeSupported(format)) {
                        selectedFormat = format;
                        break;
                    }
                }

                if (!selectedFormat) {
                    throw new Error('没有支持的音频格式');
                }

                log(`使用格式: ${selectedFormat}`, 'info');

                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: selectedFormat,
                    audioBitsPerSecond: 128000
                });

                let dataReceived = false;
                let totalSize = 0;

                mediaRecorder.ondataavailable = (event) => {
                    dataReceived = true;
                    totalSize += event.data.size;
                    log(`接收到数据: ${event.data.size} 字节 (总计: ${totalSize} 字节)`, 'success');
                };

                mediaRecorder.onstart = () => {
                    log('录制开始', 'info');
                };

                mediaRecorder.onstop = () => {
                    log('录制停止', 'info');
                    if (dataReceived) {
                        log(`✅ 录制测试成功，总共接收 ${totalSize} 字节`, 'success');
                    } else {
                        log('❌ 录制测试失败，未接收到数据', 'error');
                    }
                    
                    // 清理资源
                    stream.getTracks().forEach(track => track.stop());
                    audioStream = null;
                };

                mediaRecorder.onerror = (event) => {
                    log(`录制错误: ${event.error}`, 'error');
                };

                // 开始录制
                mediaRecorder.start(1000);
                
                // 3秒后停止
                setTimeout(() => {
                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        mediaRecorder.stop();
                    }
                }, 3000);

            } catch (error) {
                log(`❌ 录制测试失败: ${error.message}`, 'error');
            }
        }

        async function testRealTimeRecording() {
            log('测试实时录制（模拟WebSocket发送）...', 'info');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1
                    }
                });

                const recorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                let chunkCount = 0;
                let totalBytes = 0;

                recorder.ondataavailable = (event) => {
                    chunkCount++;
                    totalBytes += event.data.size;
                    
                    log(`数据块 #${chunkCount}: ${event.data.size} 字节`, 'info');
                    
                    // 模拟WebSocket发送
                    if (event.data.size > 0) {
                        log(`模拟发送到WebSocket: ${event.data.size} 字节`, 'success');
                    } else {
                        log(`警告: 空数据块`, 'warning');
                    }
                };

                recorder.onstart = () => {
                    log('实时录制开始', 'info');
                };

                recorder.onstop = () => {
                    log(`实时录制结束: 共 ${chunkCount} 个数据块，总计 ${totalBytes} 字节`, 'info');
                    stream.getTracks().forEach(track => track.stop());
                };

                // 开始录制，每500ms生成一个数据块
                recorder.start(500);
                
                // 5秒后停止
                setTimeout(() => {
                    recorder.stop();
                }, 5000);

            } catch (error) {
                log(`❌ 实时录制测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            log('音频录制调试工具已加载', 'info');
            checkBrowserSupport();
        });
    </script>
</body>
</html>

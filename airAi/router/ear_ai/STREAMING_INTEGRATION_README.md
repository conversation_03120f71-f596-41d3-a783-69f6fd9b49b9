# Streaming Voice Translation API Integration

This document describes the integration of the new streaming voice translation REST API endpoints into the existing ear router configuration.

## Integration Summary

The new streaming audio upload API has been successfully integrated into the existing ear router alongside the current WebSocket-based streaming voice translation endpoint.

## Added Endpoints

### Core Streaming API Endpoints

1. **POST `/v1/streamingAudioUpload`** - Upload audio file for real-time processing with SSE streaming
2. **GET `/v1/streamingSessions`** - List all active processing sessions
3. **GET `/v1/streamingSessions/:session_id`** - Get detailed status of a specific session
4. **DELETE `/v1/streamingSessions/:session_id`** - Stop a specific processing session

### Information Endpoints

5. **GET `/v1/streamingHealth`** - Health check for streaming API
6. **GET `/v1/streamingFormats`** - Get supported audio formats and languages

## Integration Details

### Files Modified

1. **`airAi/router/ear_ai/enter.go`**
   - Added import for `airAi/other_api/qwen` package
   - Created `streamingAPIHandler` instance using `qwen.NewStreamingAPIHandler()`

2. **`airAi/router/ear_ai/ear_agent.go`**
   - Added 6 new endpoints to the `InitEarAuthorRouter` function
   - Maintained consistency with existing naming conventions
   - Added Chinese comments following the existing pattern

### Router Structure

The new endpoints are added to the existing `v1` router group with the same middleware:
- `middleware.OperationRecord()` for operation logging
- Same authentication pattern as existing endpoints

### Endpoint Comparison

| Existing Endpoint | New Endpoint | Purpose |
|------------------|--------------|---------|
| `/streamingVoiceTranslate` (WebSocket) | `/streamingAudioUpload` (REST+SSE) | Real-time voice translation |
| N/A | `/streamingSessions` | Session management |
| N/A | `/streamingHealth` | Health monitoring |

## Usage Examples

### Audio Upload with Streaming Results

```bash
curl -X POST "http://localhost:8080/v1/streamingAudioUpload" \
  -H "Accept: text/event-stream" \
  -F "audio_file=@audio.wav" \
  -F "api_key=your-api-key" \
  -F "source_language=zh" \
  -F "target_languages=en"
```

### Session Management

```bash
# List active sessions
curl "http://localhost:8080/v1/streamingSessions"

# Get session status
curl "http://localhost:8080/v1/streamingSessions/session-uuid"

# Stop session
curl -X DELETE "http://localhost:8080/v1/streamingSessions/session-uuid"
```

### Health Check

```bash
curl "http://localhost:8080/v1/streamingHealth"
```

### Get Supported Formats

```bash
curl "http://localhost:8080/v1/streamingFormats"
```

## Response Format

### SSE Streaming Response (from `/streamingAudioUpload`)

```
event: asr
data: {"type":"asr_result","session_id":"uuid","data":{"original_text":"你好","is_partial":true}}

event: translation
data: {"type":"translation_result","session_id":"uuid","data":{"translated_text":"Hello","target_language":"en"}}

event: end
data: 
```

### Session List Response (from `/streamingSessions`)

```json
{
  "active_sessions": [
    {
      "session_id": "uuid",
      "status": "processing",
      "file_name": "audio.wav",
      "progress": 50.0,
      "source_language": "zh",
      "target_languages": ["en"]
    }
  ],
  "total_count": 1
}
```

## Authentication and Middleware

The new endpoints inherit the same middleware configuration as existing endpoints:
- Operation recording for audit trails
- Same authentication pattern (currently using `middleware.OperationRecord()`)
- Consistent error handling and logging

## Compatibility

- **Backward Compatible**: Existing endpoints remain unchanged
- **Consistent Naming**: Follows existing API naming conventions
- **Same Router Group**: Uses the same `v1` router group
- **Middleware Inheritance**: Uses the same middleware stack

## Benefits

1. **REST API Alternative**: Provides REST-based alternative to WebSocket streaming
2. **File Upload Support**: Direct multipart file upload capability
3. **Session Management**: Built-in session tracking and management
4. **Health Monitoring**: Dedicated health check endpoints
5. **Format Discovery**: API to discover supported formats and languages

## Next Steps

1. **Testing**: Test the new endpoints with various audio formats
2. **Documentation**: Update API documentation to include new endpoints
3. **Monitoring**: Set up monitoring for the new streaming sessions
4. **Client Libraries**: Create client libraries for easy integration

## Notes

- The new streaming API handler is instantiated once and reused across requests
- Session management provides better control over concurrent uploads
- Health check endpoint enables better monitoring and load balancing
- Format endpoint helps clients understand API capabilities

This integration provides a comprehensive REST API for streaming voice translation while maintaining full compatibility with the existing WebSocket-based implementation.

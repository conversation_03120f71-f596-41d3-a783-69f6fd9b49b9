package ear_ai

import (
	"airAi/middleware"

	"github.com/gin-gonic/gin"
)

type EarRouter struct{}

func (e *EarRouter) InitEarRouter(Router *gin.RouterGroup) {
	earRouter := Router.Group("/v1").Use(middleware.OperationRecord())
	{
		earRouter.POST("/login", airportApi.Login)         // 登陆
		earRouter.POST("/checkCode", airportApi.CheckCode) // 验证验证码

		//earRouter.POST("/register", airportApi)    // 注册
		earRouter.POST("/resetPassword", airportApi.ResetPassword) // 重置密码
		earRouter.POST("/getCode", airportApi.SmCode)              // 获取验证码

	}
}

func (e *EarRouter) InitEarAuthorRouter(Router *gin.RouterGroup) {
	//earRouter := Router.Group("v1").Use(middleware.JWTAuth()).Use(middleware.OperationRecord())
	earRouter := Router.Group("v1").Use(middleware.OperationRecord())
	{
		earRouter.POST("/recognize", airportApi.Recognize)    // 上传文件
		earRouter.POST("/updateUser", airportApi.UpdateUser)  // 修改用户
		earRouter.GET("/getUserInfo", airportApi.GetUserInfo) // 获取用户信息
		//earRouter.GET("/chat", airportApi.Chat)                      // ws聊天
		earRouter.POST("/textChat", airportApi.TextChat)             // 文字聊天
		earRouter.POST("/streamTextChat", airportApi.StreamTextChat) // 流式文字聊天
		earRouter.POST("/xfTtsChat", airportApi.TextToSpeech)        // 讯飞文字转语音
		earRouter.POST("/syncChat", airportApi.SyncChat)             // 同步聊天
		earRouter.POST("/translate", airportApi.Translate)           //文字翻译
		earRouter.POST("/tts", airportApi.TToSpeech)                 //文字转语音
		earRouter.POST("/audioTranslate", airportApi.AudioTranslate) //语音翻译
		//earRouter.POST("/streamingVoiceTranslate", airportApi.StreamingVoiceTranslate)                     //流式语音翻译
		earRouter.POST("/streamingVoiceTranslate", airportApi.StreamingVoiceTranslate)                     //流式语音翻译
		earRouter.POST("/concurrentStreamingVoiceTranslate", airportApi.ConcurrentStreamingVoiceTranslate) //并发流式语音翻译

		// 语音识别接口（使用PfRealtimeV2模型）
		earRouter.POST("/speechRecognition", airportApi.SpeechRecognition)                   // 语音识别（非流式）
		earRouter.POST("/streamingSpeechRecognition", airportApi.StreamingSpeechRecognition) // 流式语音识别

		earRouter.POST("/meeting", airportApi.Meeting)         //会议
		earRouter.POST("/textMeeting", airportApi.TextMeeting) //文字转会议

		earRouter.POST("/feedback", airportApi.Feedback) //意见反馈

		// 新增WebSocket音频识别接口
		earRouter.GET("/ws/audioRecognize", airportApi.WsAudioRecognize)
		earRouter.GET("/ws/audio", airportApi.WsAudio)
	}
}

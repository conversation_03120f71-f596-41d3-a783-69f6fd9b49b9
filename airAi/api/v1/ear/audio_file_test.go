package ear

import (
	"airAi/other_api/qwen"
	"airAi/utils"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestAudioFileManager 测试音频文件管理器
func TestAudioFileManager(t *testing.T) {
	// 创建临时测试目录
	testDir := "./test_audio_output"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 测试目录创建
	err := audioManager.EnsureOutputDirectory()
	assert.NoError(t, err)
	assert.DirExists(t, testDir)

	// 创建模拟音频数据
	mockAudioData := []byte("mock mp3 audio data for testing")
	segmentID := 1
	timestamp := time.Now().Unix()
	format := "mp3"

	// 测试音频文件保存
	filePath, err := audioManager.SaveAudioFile(mockAudioData, segmentID, timestamp, format)
	assert.NoError(t, err)
	assert.NotEmpty(t, filePath)
	assert.FileExists(t, filePath)

	// 验证文件名格式
	expectedFilename := fmt.Sprintf("voice_response_%d_segment_%d.%s", timestamp, segmentID, format)
	assert.Contains(t, filePath, expectedFilename)

	// 验证文件内容
	savedData, err := os.ReadFile(filePath)
	assert.NoError(t, err)
	assert.Equal(t, mockAudioData, savedData)
}

// TestAudioFileVerification 测试音频文件验证
func TestAudioFileVerification(t *testing.T) {
	testDir := "./test_audio_verification"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)
	err := audioManager.EnsureOutputDirectory()
	assert.NoError(t, err)

	// 创建有效的MP3文件头
	mp3Header := []byte{0xFF, 0xFB, 0x90, 0x00} // MP3帧同步字
	mockMP3Data := append(mp3Header, []byte("mock mp3 audio content")...)

	// 保存文件
	filePath, err := audioManager.SaveAudioFile(mockMP3Data, 1, time.Now().Unix(), "mp3")
	assert.NoError(t, err)

	// 验证文件
	audioFileInfo, err := audioManager.VerifyAudioFile(filePath, 2.5, "mp3")
	assert.NoError(t, err)
	assert.NotNil(t, audioFileInfo)
	assert.Equal(t, "mp3", audioFileInfo.Format)
	assert.Equal(t, 2.5, audioFileInfo.Duration)
	assert.True(t, audioFileInfo.IsValid)
	assert.Equal(t, int64(len(mockMP3Data)), audioFileInfo.FileSize)
}

// TestBase64AudioFileSaving 测试Base64音频文件保存
func TestBase64AudioFileSaving(t *testing.T) {
	testDir := "./test_base64_audio"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 创建模拟音频数据并编码为base64
	mockAudioData := []byte("mock audio data for base64 testing")
	base64Data := base64.StdEncoding.EncodeToString(mockAudioData)

	// 保存base64编码的音频文件
	filePath, err := audioManager.SaveBase64AudioFile(base64Data, 2, time.Now().Unix(), "mp3")
	assert.NoError(t, err)
	assert.FileExists(t, filePath)

	// 验证解码后的内容
	savedData, err := os.ReadFile(filePath)
	assert.NoError(t, err)
	assert.Equal(t, mockAudioData, savedData)
}

// TestIncrementalAudioFileSaving 测试增量音频文件保存
func TestIncrementalAudioFileSaving(t *testing.T) {
	// 模拟增量语音处理的音频文件保存
	testCases := []struct {
		segmentID    int
		text         string
		audioData    []byte
		format       string
		duration     float64
		expectedFile string
	}{
		{
			segmentID: 1,
			text:      "Hello world",
			audioData: []byte("mock audio for hello world"),
			format:    "mp3",
			duration:  1.5,
		},
		{
			segmentID: 2,
			text:      "this is a test",
			audioData: []byte("mock audio for this is a test"),
			format:    "mp3",
			duration:  2.0,
		},
		{
			segmentID: 3,
			text:      "Alibaba Speech Lab",
			audioData: []byte("mock audio for Alibaba Speech Lab"),
			format:    "mp3",
			duration:  2.5,
		},
	}

	testDir := "./test_incremental_audio"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)
	timestamp := time.Now().Unix()

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Segment_%d", tc.segmentID), func(t *testing.T) {
			// 创建语音响应数据
			voiceResponse := &qwen.VoiceResponseData{
				AudioData:   tc.audioData,
				AudioFormat: tc.format,
				Duration:    tc.duration,
				SampleRate:  16000,
				Metadata: map[string]string{
					"segment_id":      fmt.Sprintf("%d", tc.segmentID),
					"processing_type": "incremental",
					"text_length":     fmt.Sprintf("%d", len(tc.text)),
					"translated_text": tc.text,
					"tts_model":       "sambert-zhinan-v1",
				},
			}

			// 保存音频文件
			filePath, err := audioManager.SaveAudioFile(
				voiceResponse.AudioData,
				tc.segmentID,
				timestamp,
				voiceResponse.AudioFormat,
			)
			assert.NoError(t, err)
			assert.FileExists(t, filePath)

			// 验证文件名包含正确的段ID
			assert.Contains(t, filePath, fmt.Sprintf("segment_%d", tc.segmentID))

			// 验证文件内容
			savedData, err := os.ReadFile(filePath)
			assert.NoError(t, err)
			assert.Equal(t, tc.audioData, savedData)

			// 验证文件信息
			audioFileInfo, err := audioManager.GetAudioFileInfo(filePath)
			assert.NoError(t, err)
			assert.Equal(t, tc.format, audioFileInfo.Format)
			assert.Equal(t, int64(len(tc.audioData)), audioFileInfo.FileSize)
		})
	}
}

// TestAudioFileNamingConvention 测试音频文件命名约定
func TestAudioFileNamingConvention(t *testing.T) {
	testDir := "./test_naming_convention"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	testCases := []struct {
		segmentID int
		timestamp int64
		format    string
		expected  string
	}{
		{1, 1234567890, "mp3", "voice_response_1234567890_segment_1.mp3"},
		{2, 1234567891, "wav", "voice_response_1234567891_segment_2.wav"},
		{10, 1234567892, "mp3", "voice_response_1234567892_segment_10.mp3"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Segment_%d", tc.segmentID), func(t *testing.T) {
			mockData := []byte("test audio data")

			filePath, err := audioManager.SaveAudioFile(mockData, tc.segmentID, tc.timestamp, tc.format)
			assert.NoError(t, err)

			// 验证文件名
			filename := filepath.Base(filePath)
			assert.Equal(t, tc.expected, filename)
		})
	}
}

// TestAudioContentVerification 测试音频内容验证
func TestAudioContentVerification(t *testing.T) {
	testDir := "./test_content_verification"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 测试不同格式的音频验证
	testCases := []struct {
		format      string
		audioData   []byte
		shouldPass  bool
		description string
	}{
		{
			format:      "mp3",
			audioData:   []byte("ID3mock mp3 data"),
			shouldPass:  true,
			description: "Valid MP3 with ID3 tag",
		},
		{
			format:      "mp3",
			audioData:   []byte{0xFF, 0xFB, 0x90, 0x00, 0x01, 0x02},
			shouldPass:  true,
			description: "Valid MP3 with frame sync",
		},
		{
			format:      "wav",
			audioData:   []byte("RIFFmock wav data"),
			shouldPass:  true,
			description: "Valid WAV with RIFF header",
		},
		{
			format:      "mp3",
			audioData:   []byte("invalid mp3 data"),
			shouldPass:  false,
			description: "Invalid MP3 format",
		},
		{
			format:      "wav",
			audioData:   []byte("invalid wav data"),
			shouldPass:  false,
			description: "Invalid WAV format",
		},
	}

	for i, tc := range testCases {
		t.Run(fmt.Sprintf("Test_%d_%s", i+1, tc.description), func(t *testing.T) {
			filePath, err := audioManager.SaveAudioFile(tc.audioData, i+1, time.Now().Unix(), tc.format)
			assert.NoError(t, err)

			// 验证音频格式
			_, err = audioManager.VerifyAudioFile(filePath, 1.0, tc.format)
			if tc.shouldPass {
				assert.NoError(t, err, "Expected validation to pass for %s", tc.description)
			} else {
				assert.Error(t, err, "Expected validation to fail for %s", tc.description)
			}
		})
	}
}

// TestAudioFileCleanup 测试音频文件清理
func TestAudioFileCleanup(t *testing.T) {
	testDir := "./test_cleanup"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 创建一些测试文件
	mockData := []byte("test data")

	// 创建旧文件（模拟1小时前）
	oldTimestamp := time.Now().Add(-2 * time.Hour).Unix()
	oldFilePath, err := audioManager.SaveAudioFile(mockData, 1, oldTimestamp, "mp3")
	assert.NoError(t, err)

	// 创建新文件
	newTimestamp := time.Now().Unix()
	newFilePath, err := audioManager.SaveAudioFile(mockData, 2, newTimestamp, "mp3")
	assert.NoError(t, err)

	// 验证文件都存在
	assert.FileExists(t, oldFilePath)
	assert.FileExists(t, newFilePath)

	// 手动修改旧文件的修改时间来模拟旧文件
	oldTime := time.Now().Add(-2 * time.Hour)
	err = os.Chtimes(oldFilePath, oldTime, oldTime)
	assert.NoError(t, err)

	// 清理1小时以前的文件
	err = audioManager.CleanupOldFiles(1 * time.Hour)
	assert.NoError(t, err)

	// 验证旧文件被删除，新文件保留
	assert.NoFileExists(t, oldFilePath)
	assert.FileExists(t, newFilePath)
}

// TestAudioFileMetadata 测试音频文件元数据
func TestAudioFileMetadata(t *testing.T) {
	// 测试增量处理的音频文件元数据
	voiceResponse := &qwen.VoiceResponseData{
		AudioData:   []byte("mock audio data"),
		AudioFormat: "mp3",
		Duration:    2.5,
		SampleRate:  16000,
		Metadata: map[string]string{
			"segment_id":      "1",
			"processing_type": "incremental",
			"text_length":     "11",
			"translated_text": "Hello world",
			"tts_model":       "sambert-zhinan-v1",
			"voice":           "default",
			"status":          "generated",
		},
	}

	// 验证元数据完整性
	assert.Equal(t, "1", voiceResponse.Metadata["segment_id"])
	assert.Equal(t, "incremental", voiceResponse.Metadata["processing_type"])
	assert.Equal(t, "11", voiceResponse.Metadata["text_length"])
	assert.Equal(t, "Hello world", voiceResponse.Metadata["translated_text"])
	assert.Equal(t, "sambert-zhinan-v1", voiceResponse.Metadata["tts_model"])

	// 验证音频属性
	assert.Equal(t, "mp3", voiceResponse.AudioFormat)
	assert.Equal(t, 2.5, voiceResponse.Duration)
	assert.Equal(t, 16000, voiceResponse.SampleRate)
	assert.NotEmpty(t, voiceResponse.AudioData)
}

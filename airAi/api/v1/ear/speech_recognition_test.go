package ear

import (
	"airAi/other_api/qwen"
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestSpeechRecognition 测试非流式语音识别API
func TestSpeechRecognition(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/speechRecognition", api.SpeechRecognition)

	// 创建测试音频文件内容
	testAudioData := make([]byte, 2048)
	for i := range testAudioData {
		testAudioData[i] = byte(i % 256)
	}

	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	fileWriter, err := writer.CreateFormFile("file", "test_audio.wav")
	assert.NoError(t, err)
	_, err = fileWriter.Write(testAudioData)
	assert.NoError(t, err)

	// 添加源语言字段
	err = writer.WriteField("sourceLanguage", "zh")
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/v1/speechRecognition", &buf)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 执行请求
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 解析响应
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应结构
	assert.Contains(t, response, "code")
	assert.Contains(t, response, "data")
	assert.Contains(t, response, "msg")

	// 验证数据结构包含original_text字段
	if data, ok := response["data"].(map[string]interface{}); ok {
		assert.Contains(t, data, "original_text")
		assert.Contains(t, data, "words")
		assert.Contains(t, data, "language")
	}
}

// TestStreamingSpeechRecognition 测试流式语音识别API
func TestStreamingSpeechRecognition(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamingSpeechRecognition", api.StreamingSpeechRecognition)

	// 创建测试音频文件内容
	testAudioData := make([]byte, 1024)
	for i := range testAudioData {
		testAudioData[i] = byte(i % 256)
	}

	// 创建multipart表单
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	fileWriter, err := writer.CreateFormFile("file", "test_audio.wav")
	assert.NoError(t, err)
	_, err = fileWriter.Write(testAudioData)
	assert.NoError(t, err)

	// 添加源语言字段
	err = writer.WriteField("sourceLanguage", "zh")
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/v1/streamingSpeechRecognition", &buf)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 执行请求
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应头
	assert.Equal(t, "text/event-stream", w.Header().Get("Content-Type"))
	assert.Equal(t, "no-cache", w.Header().Get("Cache-Control"))
	assert.Equal(t, "keep-alive", w.Header().Get("Connection"))
}

// TestStreamASRResults 测试ASR结果流式处理
func TestStreamASRResults(t *testing.T) {
	// 创建测试结果通道
	resultChan := make(chan qwen.StreamingTranscriptionResult, 5)

	// 发送测试数据
	go func() {
		defer close(resultChan)

		// 发送部分结果
		resultChan <- qwen.StreamingTranscriptionResult{
			Text:      "你好",
			IsPartial: true,
			IsEnd:     false,
			Language:  "zh",
		}

		// 发送完整结果
		resultChan <- qwen.StreamingTranscriptionResult{
			Text:      "你好世界",
			IsPartial: false,
			IsEnd:     false,
			Language:  "zh",
		}

		// 发送结束标记
		resultChan <- qwen.StreamingTranscriptionResult{
			Text:      "",
			IsPartial: false,
			IsEnd:     true,
			Language:  "zh",
		}
	}()

	// 收集结果
	var results []qwen.StreamingTranscriptionResult
	timeout := time.After(5 * time.Second)

	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				goto done
			}
			results = append(results, result)
			if result.IsEnd {
				goto done
			}
		case <-timeout:
			t.Error("Test timeout")
			return
		}
	}

done:
	// 验证结果
	assert.Greater(t, len(results), 0, "Should receive at least one result")

	// 验证最后一个结果是结束标志
	if len(results) > 0 {
		lastResult := results[len(results)-1]
		assert.True(t, lastResult.IsEnd, "Last result should be end marker")
	}

	// 验证部分结果
	if len(results) >= 2 {
		partialResult := results[0]
		assert.True(t, partialResult.IsPartial, "First result should be partial")
		assert.Equal(t, "你好", partialResult.Text)
		assert.Equal(t, "zh", partialResult.Language)
	}
}

// TestSpeechRecognitionErrorHandling 测试错误处理
func TestSpeechRecognitionErrorHandling(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/speechRecognition", api.SpeechRecognition)

	// 测试没有文件的情况
	req, err := http.NewRequest("POST", "/v1/speechRecognition", nil)
	assert.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 应该返回错误
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证错误响应
	if code, ok := response["code"].(float64); ok {
		assert.NotEqual(t, 0, int(code), "Should return error code")
	}
}

// TestSpeechRecognitionResponseStructure 测试响应结构
func TestSpeechRecognitionResponseStructure(t *testing.T) {
	// 测试ASR响应结构
	asrResponse := map[string]interface{}{
		"original_text": "测试语音识别",
		"is_partial":    false,
		"is_end":        false,
		"language":      "zh",
	}

	// 验证字段存在
	assert.Contains(t, asrResponse, "original_text")
	assert.Contains(t, asrResponse, "is_partial")
	assert.Contains(t, asrResponse, "is_end")
	assert.Contains(t, asrResponse, "language")

	// 验证字段类型
	assert.IsType(t, "", asrResponse["original_text"])
	assert.IsType(t, false, asrResponse["is_partial"])
	assert.IsType(t, false, asrResponse["is_end"])
	assert.IsType(t, "", asrResponse["language"])

	// 验证original_text字段用于ASR结果
	assert.Equal(t, "测试语音识别", asrResponse["original_text"])
	assert.Equal(t, "zh", asrResponse["language"])
}

// TestPfRealtimeV2ModelUsage 测试PfRealtimeV2模型的使用
func TestPfRealtimeV2ModelUsage(t *testing.T) {
	// 验证PfRealtimeV2模型常量
	assert.Equal(t, "paraformer-realtime-v2", qwen.PfRealtimeV2)

	// 测试ASR配置
	config := qwen.AliYunConfig{
		ApiKey:         "test-api-key",
		Model:          qwen.PfRealtimeV2,
		SourceLanguage: "zh",
	}

	// 验证配置
	assert.Equal(t, qwen.PfRealtimeV2, config.Model)
	assert.Equal(t, "zh", config.SourceLanguage)
	assert.NotEmpty(t, config.ApiKey)
}

// TestSpeechRecognitionServiceMethods 测试语音识别服务方法
func TestSpeechRecognitionServiceMethods(t *testing.T) {
	// 这个测试验证服务方法的存在性和基本结构
	// 实际的ASR调用需要真实的API密钥和网络连接

	// 验证方法签名和参数
	testCases := []struct {
		name           string
		sourceLanguage string
		expectedModel  string
	}{
		{
			name:           "中文语音识别",
			sourceLanguage: "zh",
			expectedModel:  qwen.PfRealtimeV2,
		},
		{
			name:           "英文语音识别",
			sourceLanguage: "en",
			expectedModel:  qwen.PfRealtimeV2,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 验证参数有效性
			assert.NotEmpty(t, tc.sourceLanguage, "Source language should not be empty")
			assert.Equal(t, qwen.PfRealtimeV2, tc.expectedModel, "Should use PfRealtimeV2 model")
		})
	}
}

// TestAPIEndpointIntegration 测试API端点集成
func TestAPIEndpointIntegration(t *testing.T) {
	// 验证API端点路径
	endpoints := []struct {
		path   string
		method string
		desc   string
	}{
		{
			path:   "/v1/speechRecognition",
			method: "POST",
			desc:   "非流式语音识别",
		},
		{
			path:   "/v1/streamingSpeechRecognition",
			method: "POST",
			desc:   "流式语音识别",
		},
	}

	for _, endpoint := range endpoints {
		t.Run(endpoint.desc, func(t *testing.T) {
			assert.NotEmpty(t, endpoint.path, "Endpoint path should not be empty")
			assert.Equal(t, "POST", endpoint.method, "Should use POST method")
			assert.Contains(t, endpoint.path, "/v1/", "Should follow v1 API pattern")
		})
	}
}

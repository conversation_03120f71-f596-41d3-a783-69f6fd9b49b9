package ear

import (
	"airAi/service/airport"
	"airAi/utils"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestMeetingLanguageValidation 测试会议纪要语言验证功能
func TestMeetingLanguageValidation(t *testing.T) {
	tests := []struct {
		name        string
		language    string
		expectError bool
		description string
	}{
		{
			name:        "ValidChineseLang",
			language:    "zh",
			expectError: false,
			description: "测试有效的中文语言代码",
		},
		{
			name:        "ValidEnglishLang",
			language:    "en",
			expectError: false,
			description: "测试有效的英文语言代码",
		},
		{
			name:        "ValidJapaneseLang",
			language:    "ja",
			expectError: false,
			description: "测试有效的日语语言代码",
		},
		{
			name:        "InvalidLang",
			language:    "invalid",
			expectError: true,
			description: "测试无效的语言代码",
		},
		{
			name:        "EmptyLang",
			language:    "",
			expectError: true,
			description: "测试空语言代码",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := utils.ValidateMeetingLanguage(tt.language)
			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

// TestMeetingLanguageMapping 测试语言代码到自然语言名称的映射
func TestMeetingLanguageMapping(t *testing.T) {
	tests := []struct {
		languageCode string
		expectedName string
	}{
		{"zh", "中文"},
		{"en", "English"},
		{"ja", "日本語"},
		{"ko", "한국어"},
		{"es", "Español"},
		{"fr", "Français"},
	}

	for _, tt := range tests {
		t.Run("Language_"+tt.languageCode, func(t *testing.T) {
			name, err := utils.GetMeetingLanguageName(tt.languageCode)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedName, name)
		})
	}
}

// TestDefaultMeetingLanguage 测试默认会议纪要语言
func TestDefaultMeetingLanguage(t *testing.T) {
	defaultLang := utils.GetDefaultMeetingLanguage()
	assert.Equal(t, "zh", defaultLang, "默认语言应该是中文")
	
	// 验证默认语言是有效的
	err := utils.ValidateMeetingLanguage(defaultLang)
	assert.NoError(t, err, "默认语言应该是有效的")
}

// TestMeetingServiceWithDifferentLanguages 测试会议服务支持不同语言
func TestMeetingServiceWithDifferentLanguages(t *testing.T) {
	// 注意：这个测试需要真实的API调用，在实际环境中可能需要mock
	// 这里提供测试框架，实际使用时可以根据需要启用
	
	t.Skip("跳过需要真实API调用的测试，可在集成测试中启用")
	
	service := &airport.UserService{}
	testText := "今天我们讨论了项目进展。张三负责前端开发，李四负责后端开发。预计下周完成第一版。"
	
	languages := []string{"zh", "en", "ja"}
	
	for _, lang := range languages {
		t.Run("Language_"+lang, func(t *testing.T) {
			meeting, err := service.Meeting(lang, testText)
			assert.NoError(t, err, "会议纪要生成应该成功")
			assert.NotEmpty(t, meeting.Title, "会议标题不应为空")
			assert.NotEmpty(t, meeting.Summary, "会议摘要不应为空")
			
			// 验证返回的JSON结构
			jsonData, err := json.Marshal(meeting)
			assert.NoError(t, err, "会议纪要应该能够序列化为JSON")
			assert.NotEmpty(t, jsonData, "JSON数据不应为空")
		})
	}
}

// TestMeetingPromptFormatting 测试会议提示模板格式化
func TestMeetingPromptFormatting(t *testing.T) {
	// 测试提示模板是否正确格式化
	testCases := []struct {
		language     string
		expectedLang string
	}{
		{"zh", "中文"},
		{"en", "English"},
		{"ja", "日本語"},
	}
	
	for _, tc := range testCases {
		t.Run("PromptFormat_"+tc.language, func(t *testing.T) {
			languageName, err := utils.GetMeetingLanguageName(tc.language)
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedLang, languageName)
			
			// 验证语言名称不为空
			assert.NotEmpty(t, languageName, "语言名称不应为空")
		})
	}
}

// TestMeetingLanguageMapCompleteness 测试语言映射表的完整性
func TestMeetingLanguageMapCompleteness(t *testing.T) {
	// 验证语言映射表包含常用语言
	requiredLanguages := []string{
		"zh", "en", "ja", "ko", "es", "fr", "de", "it", "pt", "ru",
	}
	
	for _, lang := range requiredLanguages {
		t.Run("RequiredLang_"+lang, func(t *testing.T) {
			err := utils.ValidateMeetingLanguage(lang)
			assert.NoError(t, err, "常用语言 %s 应该被支持", lang)
			
			name, err := utils.GetMeetingLanguageName(lang)
			assert.NoError(t, err)
			assert.NotEmpty(t, name, "语言 %s 的名称不应为空", lang)
		})
	}
}

// TestMeetingErrorHandling 测试会议纪要错误处理
func TestMeetingErrorHandling(t *testing.T) {
	service := &airport.UserService{}
	
	// 测试无效语言代码
	_, err := service.Meeting("invalid_lang", "test content")
	assert.Error(t, err, "无效语言代码应该返回错误")
	assert.Contains(t, err.Error(), "语言验证失败", "错误信息应该包含语言验证失败")
	
	// 测试空语言代码
	_, err = service.Meeting("", "test content")
	assert.Error(t, err, "空语言代码应该返回错误")
	
	// 测试空文本内容（这个测试需要根据实际实现调整）
	// 注意：实际的AI服务可能对空文本有不同的处理方式
}

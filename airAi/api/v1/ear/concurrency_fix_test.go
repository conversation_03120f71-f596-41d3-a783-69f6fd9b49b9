package ear

import (
	"airAi/other_api/qwen"
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestChannelConcurrencySafety 测试通道并发安全性
func TestChannelConcurrencySafety(t *testing.T) {
	// 测试多个goroutine同时关闭通道的安全性
	resultChan := make(chan qwen.StreamingTranslateResult, 10)

	// 使用 sync.Once 确保通道只被关闭一次
	var closeOnce sync.Once
	closeResultChan := func() {
		closeOnce.Do(func() {
			close(resultChan)
		})
	}

	// 启动多个goroutine尝试关闭同一个通道
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer closeResultChan() // 这应该是安全的

			// 模拟一些工作
			time.Sleep(time.Millisecond * 10)
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 验证通道已关闭
	select {
	case _, ok := <-resultChan:
		assert.False(t, ok, "Channel should be closed")
	default:
		t.Error("Channel should be closed and readable")
	}
}

// TestSafeChannelSendPattern 测试安全的通道发送模式
func TestSafeChannelSendPattern(t *testing.T) {
	resultChan := make(chan qwen.StreamingTranslateResult, 10)
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// 使用 sync.Once 确保通道只被关闭一次
	var closeOnce sync.Once
	closeResultChan := func() {
		closeOnce.Do(func() {
			close(resultChan)
		})
	}

	// 启动一个goroutine使用安全的发送模式
	go func() {
		defer closeResultChan() // 确保通道最终被关闭

		for i := 0; i < 20; i++ {
			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				return
			default:
			}

			// 使用我们在实际代码中使用的安全模式
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content: "test",
			}:
				// 成功发送
			case <-ctx.Done():
				return
			default:
				// 通道可能已满或已关闭，安全跳过
				time.Sleep(time.Millisecond * 5)
			}
		}
	}()

	// 等待上下文超时
	<-ctx.Done()

	// 这个测试主要验证不会发生panic
	assert.True(t, true, "No panic should occur with safe send pattern")
}

// TestSyncOnceChannelClose 测试sync.Once确保通道只关闭一次
func TestSyncOnceChannelClose(t *testing.T) {
	resultChan := make(chan qwen.ComprehensiveVoiceResult, 10)

	// 使用 sync.Once 确保通道只被关闭一次
	var closeOnce sync.Once
	closeResultChan := func() {
		closeOnce.Do(func() {
			close(resultChan)
		})
	}

	// 启动多个goroutine尝试关闭同一个通道
	var wg sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer closeResultChan() // 这应该是安全的，只有第一个会真正关闭通道

			// 模拟一些工作
			time.Sleep(time.Millisecond * 10)
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 验证通道已关闭
	select {
	case _, ok := <-resultChan:
		assert.False(t, ok, "Channel should be closed")
	default:
		t.Error("Channel should be closed and readable")
	}
}

// TestContextCancellation 测试上下文取消的处理
func TestContextCancellation(t *testing.T) {
	resultChan := make(chan qwen.ComprehensiveVoiceResult, 10)
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	// 使用 sync.Once 确保通道只被关闭一次
	var closeOnce sync.Once
	closeResultChan := func() {
		closeOnce.Do(func() {
			close(resultChan)
		})
	}

	// 启动一个长时间运行的goroutine
	go func() {
		defer closeResultChan()

		for i := 0; i < 100; i++ {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "test",
				IsPartial:      true,
				IsEnd:          false,
			}:
			case <-ctx.Done():
				// 上下文取消，安全退出
				return
			default:
				// 通道可能已满，稍等再试
				time.Sleep(time.Millisecond * 5)
			}
		}
	}()

	// 等待上下文超时
	<-ctx.Done()

	// 验证goroutine能够正确响应上下文取消
	time.Sleep(time.Millisecond * 20)
	assert.True(t, true, "Context cancellation should be handled gracefully")
}

// TestPanicRecovery 测试panic恢复机制
func TestPanicRecovery(t *testing.T) {
	resultChan := make(chan qwen.StreamingTranslateResult, 10)

	// 使用 sync.Once 确保通道只被关闭一次
	var closeOnce sync.Once
	closeResultChan := func() {
		closeOnce.Do(func() {
			close(resultChan)
		})
	}

	// 启动一个会panic的goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 处理panic，确保通道被关闭
				closeResultChan()
			}
		}()

		// 故意触发panic
		panic("test panic")
	}()

	// 等待一段时间让goroutine执行
	time.Sleep(time.Millisecond * 50)

	// 验证通道已被正确关闭
	select {
	case _, ok := <-resultChan:
		assert.False(t, ok, "Channel should be closed after panic recovery")
	default:
		t.Error("Channel should be closed and readable")
	}
}

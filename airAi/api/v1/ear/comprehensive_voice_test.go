package ear

import (
	"airAi/other_api/qwen"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestComprehensiveVoiceResult 测试综合语音处理结果结构
func TestComprehensiveVoiceResult(t *testing.T) {
	// 测试ASR结果
	asrResult := qwen.ComprehensiveVoiceResult{
		OriginalText:   "Hello world",
		ASRPartial:     false,
		ProcessingType: "asr",
		Timestamp:      time.Now().Unix(),
		SourceLanguage: "en",
		TargetLanguage: "zh",
		IsPartial:      false,
		IsEnd:          false,
	}

	assert.Equal(t, "Hello world", asrResult.OriginalText)
	assert.False(t, asrResult.ASRPartial)
	assert.Equal(t, "asr", asrResult.ProcessingType)
	assert.Equal(t, "en", asrResult.SourceLanguage)
	assert.Equal(t, "zh", asrResult.TargetLanguage)
	assert.False(t, asrResult.IsPartial)
	assert.False(t, asrResult.IsEnd)

	// 测试翻译结果
	translationResult := qwen.ComprehensiveVoiceResult{
		TranslatedText: "你好世界",
		TransPartial:   false,
		ProcessingType: "translation",
		Timestamp:      time.Now().Unix(),
		SourceLanguage: "en",
		TargetLanguage: "zh",
		IsPartial:      false,
		IsEnd:          false,
	}

	assert.Equal(t, "你好世界", translationResult.TranslatedText)
	assert.False(t, translationResult.TransPartial)
	assert.Equal(t, "translation", translationResult.ProcessingType)

	// 测试TTS结果
	voiceResponse := &qwen.VoiceResponseData{
		AudioFormat: "mp3",
		SampleRate:  16000,
		Duration:    2.5,
		Metadata: map[string]string{
			"tts_model": "sambert-zhinan-v1",
			"voice":     "default",
		},
	}

	ttsResult := qwen.ComprehensiveVoiceResult{
		VoiceResponse:  voiceResponse,
		ProcessingType: "tts",
		Timestamp:      time.Now().Unix(),
		SourceLanguage: "en",
		TargetLanguage: "zh",
		IsPartial:      false,
		IsEnd:          false,
	}

	assert.NotNil(t, ttsResult.VoiceResponse)
	assert.Equal(t, "mp3", ttsResult.VoiceResponse.AudioFormat)
	assert.Equal(t, 16000, ttsResult.VoiceResponse.SampleRate)
	assert.Equal(t, 2.5, ttsResult.VoiceResponse.Duration)
	assert.Equal(t, "tts", ttsResult.ProcessingType)

	// 测试JSON序列化
	jsonData, err := json.Marshal(ttsResult)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "voice_response")
	assert.Contains(t, string(jsonData), "processing_type")
	assert.Contains(t, string(jsonData), "tts")
}

// TestVoiceResponseData 测试语音响应数据结构
func TestVoiceResponseData(t *testing.T) {
	voiceData := qwen.VoiceResponseData{
		AudioData:   []byte("mock audio data"),
		AudioFormat: "mp3",
		Duration:    3.2,
		SampleRate:  22050,
		Metadata: map[string]string{
			"model":   "cosyvoice-v1",
			"voice":   "female",
			"quality": "high",
		},
	}

	assert.Equal(t, "mp3", voiceData.AudioFormat)
	assert.Equal(t, 3.2, voiceData.Duration)
	assert.Equal(t, 22050, voiceData.SampleRate)
	assert.Equal(t, "cosyvoice-v1", voiceData.Metadata["model"])
	assert.Equal(t, "female", voiceData.Metadata["voice"])
	assert.Equal(t, "high", voiceData.Metadata["quality"])
	assert.NotEmpty(t, voiceData.AudioData)
}

// TestStreamingTTS 测试流式TTS功能
func TestStreamingTTS(t *testing.T) {
	client := qwen.NewQwenClient()

	// 测试空文本
	voiceResponse, err := client.StreamingTTS("")
	assert.NoError(t, err)
	assert.Nil(t, voiceResponse)

	// 测试正常文本
	voiceResponse, err = client.StreamingTTS("Hello world")
	assert.NoError(t, err)
	assert.NotNil(t, voiceResponse)
	assert.Equal(t, "mp3", voiceResponse.AudioFormat)
	assert.Equal(t, 16000, voiceResponse.SampleRate)
	assert.Greater(t, voiceResponse.Duration, 0.0)
	assert.Contains(t, voiceResponse.Metadata, "tts_model")
}

// TestComprehensiveResultProcessingTypes 测试不同处理类型
func TestComprehensiveResultProcessingTypes(t *testing.T) {
	timestamp := time.Now().Unix()

	// 测试所有处理类型
	processingTypes := []string{"asr", "translation", "tts", "complete", "error", "asr_error", "translation_error", "tts_error"}

	for _, pType := range processingTypes {
		result := qwen.ComprehensiveVoiceResult{
			ProcessingType: pType,
			Timestamp:      timestamp,
			SourceLanguage: "zh",
			TargetLanguage: "en",
			IsPartial:      false,
			IsEnd:          pType == "complete" || pType == "error",
		}

		assert.Equal(t, pType, result.ProcessingType)
		assert.Equal(t, timestamp, result.Timestamp)
		assert.Equal(t, "zh", result.SourceLanguage)
		assert.Equal(t, "en", result.TargetLanguage)

		if pType == "complete" || pType == "error" {
			assert.True(t, result.IsEnd)
		} else {
			assert.False(t, result.IsEnd)
		}
	}
}

// TestComprehensiveResultJSONSerialization 测试JSON序列化和反序列化
func TestComprehensiveResultJSONSerialization(t *testing.T) {
	original := qwen.ComprehensiveVoiceResult{
		OriginalText:   "Hello",
		ASRPartial:     true,
		TranslatedText: "你好",
		TransPartial:   false,
		VoiceResponse: &qwen.VoiceResponseData{
			AudioFormat: "wav",
			SampleRate:  44100,
			Duration:    1.5,
			Metadata: map[string]string{
				"quality": "high",
			},
		},
		ProcessingType: "complete",
		Timestamp:      1234567890,
		SourceLanguage: "en",
		TargetLanguage: "zh",
		IsPartial:      false,
		IsEnd:          true,
	}

	// 序列化
	jsonData, err := json.Marshal(original)
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// 反序列化
	var deserialized qwen.ComprehensiveVoiceResult
	err = json.Unmarshal(jsonData, &deserialized)
	assert.NoError(t, err)

	// 验证数据完整性
	assert.Equal(t, original.OriginalText, deserialized.OriginalText)
	assert.Equal(t, original.ASRPartial, deserialized.ASRPartial)
	assert.Equal(t, original.TranslatedText, deserialized.TranslatedText)
	assert.Equal(t, original.TransPartial, deserialized.TransPartial)
	assert.Equal(t, original.ProcessingType, deserialized.ProcessingType)
	assert.Equal(t, original.Timestamp, deserialized.Timestamp)
	assert.Equal(t, original.SourceLanguage, deserialized.SourceLanguage)
	assert.Equal(t, original.TargetLanguage, deserialized.TargetLanguage)
	assert.Equal(t, original.IsPartial, deserialized.IsPartial)
	assert.Equal(t, original.IsEnd, deserialized.IsEnd)

	// 验证语音响应数据
	assert.NotNil(t, deserialized.VoiceResponse)
	assert.Equal(t, original.VoiceResponse.AudioFormat, deserialized.VoiceResponse.AudioFormat)
	assert.Equal(t, original.VoiceResponse.SampleRate, deserialized.VoiceResponse.SampleRate)
	assert.Equal(t, original.VoiceResponse.Duration, deserialized.VoiceResponse.Duration)
	assert.Equal(t, original.VoiceResponse.Metadata["quality"], deserialized.VoiceResponse.Metadata["quality"])
}

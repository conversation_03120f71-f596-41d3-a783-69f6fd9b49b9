package ear

import (
	"airAi/other_api/qwen"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestStreamingVoiceTranslate 测试流式语音翻译API
func TestStreamingVoiceTranslate(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamingVoiceTranslate", api.StreamingVoiceTranslate)

	// 创建测试音频文件内容（模拟PCM音频数据）
	testAudioData := make([]byte, 1024)
	for i := range testAudioData {
		testAudioData[i] = byte(i % 256)
	}

	// 创建multipart表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	part, err := writer.CreateFormFile("file", "test.pcm")
	assert.NoError(t, err)
	_, err = part.Write(testAudioData)
	assert.NoError(t, err)

	// 添加其他表单字段
	err = writer.WriteField("sourceLanguage", "zh")
	assert.NoError(t, err)
	err = writer.WriteField("targetLanguages", "en")
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/v1/streamingVoiceTranslate", &buf)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头
	assert.Equal(t, "text/event-stream", w.Header().Get("Content-Type"))
	assert.Equal(t, "no-cache", w.Header().Get("Cache-Control"))
	assert.Equal(t, "keep-alive", w.Header().Get("Connection"))

	// 验证响应体包含SSE格式的数据
	responseBody := w.Body.String()
	assert.Contains(t, responseBody, "data:")
}

// TestQwenStreamingTranslate 测试Qwen流式翻译客户端
func TestQwenStreamingTranslate(t *testing.T) {
	client := qwen.NewQwenClient()

	// 创建结果通道
	resultChan := make(chan qwen.StreamingTranslateResult, 10)

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 启动流式翻译
	go func() {
		err := client.StreamingTranslate(ctx, "zh", "en", "你好，世界！", resultChan)
		if err != nil {
			t.Logf("StreamingTranslate error: %v", err)
		}
	}()

	// 收集结果
	var results []qwen.StreamingTranslateResult
	timeout := time.After(10 * time.Second)

	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				// 通道已关闭
				goto done
			}
			results = append(results, result)
			t.Logf("Received result: %+v", result)

			if result.IsEnd {
				goto done
			}
		case <-timeout:
			t.Log("Test timeout")
			goto done
		}
	}

done:
	// 验证至少收到了一些结果
	assert.Greater(t, len(results), 0, "Should receive at least one result")

	// 验证最后一个结果是结束标志
	if len(results) > 0 {
		lastResult := results[len(results)-1]
		assert.True(t, lastResult.IsEnd, "Last result should be end marker")
	}
}

// TestStreamingTranslateResult 测试流式翻译结果结构
func TestStreamingTranslateResult(t *testing.T) {
	// 测试部分结果
	partialResult := qwen.StreamingTranslateResult{
		Content:   "Hello",
		IsPartial: true,
		IsEnd:     false,
	}

	assert.Equal(t, "Hello", partialResult.Content)
	assert.True(t, partialResult.IsPartial)
	assert.False(t, partialResult.IsEnd)
	assert.Empty(t, partialResult.Error)

	// 测试结束结果
	endResult := qwen.StreamingTranslateResult{
		Content:   "",
		IsPartial: false,
		IsEnd:     true,
	}

	assert.Empty(t, endResult.Content)
	assert.False(t, endResult.IsPartial)
	assert.True(t, endResult.IsEnd)
	assert.Empty(t, endResult.Error)

	// 测试错误结果
	errorResult := qwen.StreamingTranslateResult{
		Content:   "",
		IsPartial: false,
		IsEnd:     true,
		Error:     "test error",
	}

	assert.Empty(t, errorResult.Content)
	assert.False(t, errorResult.IsPartial)
	assert.True(t, errorResult.IsEnd)
	assert.Equal(t, "test error", errorResult.Error)
}

// TestSSEFormat 测试SSE格式输出
func TestSSEFormat(t *testing.T) {
	// 模拟SSE数据格式
	testData := `data: {"content":"Hello","is_partial":true,"is_end":false}

data: {"content":" world","is_partial":true,"is_end":false}

data: {"content":"","is_partial":false,"is_end":true}

`

	lines := strings.Split(testData, "\n")
	dataLines := []string{}

	for _, line := range lines {
		if strings.HasPrefix(line, "data: ") {
			dataLines = append(dataLines, strings.TrimPrefix(line, "data: "))
		}
	}

	assert.Equal(t, 3, len(dataLines), "Should have 3 data lines")
	assert.Contains(t, dataLines[0], `"content":"Hello"`)
	assert.Contains(t, dataLines[1], `"content":" world"`)
	assert.Contains(t, dataLines[2], `"is_end":true`)
}

// TestStreamingVoiceTranslate1 测试StreamingVoiceTranslate1 API
func TestStreamingVoiceTranslate1(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamingVoiceTranslate1", api.StreamingVoiceTranslate)

	// 创建测试音频文件内容（模拟PCM音频数据）
	testAudioData := make([]byte, 1024)
	for i := range testAudioData {
		testAudioData[i] = byte(i % 256)
	}

	// 创建multipart表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	part, err := writer.CreateFormFile("file", "test.pcm")
	assert.NoError(t, err)
	_, err = part.Write(testAudioData)
	assert.NoError(t, err)

	// 添加其他表单字段
	err = writer.WriteField("sourceLanguage", "zh")
	assert.NoError(t, err)
	err = writer.WriteField("targetLanguages", "en")
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/v1/streamingVoiceTranslate1", &buf)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头
	assert.Equal(t, "text/event-stream", w.Header().Get("Content-Type"))
	assert.Equal(t, "no-cache", w.Header().Get("Cache-Control"))
	assert.Equal(t, "keep-alive", w.Header().Get("Connection"))

	// 验证响应体包含SSE格式的数据
	responseBody := w.Body.String()
	assert.Contains(t, responseBody, "data:")

	// 验证响应包含JSON格式的数据
	lines := strings.Split(responseBody, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "data: ") {
			jsonData := strings.TrimPrefix(line, "data: ")
			if jsonData != "" {
				var result map[string]interface{}
				err := json.Unmarshal([]byte(jsonData), &result)
				assert.NoError(t, err, "Response should contain valid JSON: %s", jsonData)
			}
		}
	}
}

// BenchmarkStreamingVoiceTranslate 性能测试
func BenchmarkStreamingVoiceTranslate(b *testing.B) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamingVoiceTranslate", api.StreamingVoiceTranslate)

	// 创建测试音频数据
	testAudioData := make([]byte, 1024)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		writer := multipart.NewWriter(&buf)

		part, _ := writer.CreateFormFile("file", "test.pcm")
		part.Write(testAudioData)
		writer.WriteField("sourceLanguage", "zh")
		writer.WriteField("targetLanguages", "en")
		writer.Close()

		req, _ := http.NewRequest("POST", "/v1/streamingVoiceTranslate", &buf)
		req.Header.Set("Content-Type", writer.FormDataContentType())

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

// TestConcurrentStreamingVoiceTranslate 测试并发流式语音翻译API
func TestConcurrentStreamingVoiceTranslate(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/concurrentStreamingVoiceTranslate", api.ConcurrentStreamingVoiceTranslate)

	// 创建测试音频文件内容
	testAudioData := make([]byte, 2048)
	for i := range testAudioData {
		testAudioData[i] = byte(i % 256)
	}

	// 创建multipart表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加音频文件
	part, err := writer.CreateFormFile("file", "test.pcm")
	assert.NoError(t, err)
	_, err = part.Write(testAudioData)
	assert.NoError(t, err)

	// 添加其他表单字段
	err = writer.WriteField("sourceLanguage", "zh")
	assert.NoError(t, err)
	err = writer.WriteField("targetLanguages", "en")
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "/v1/concurrentStreamingVoiceTranslate", &buf)
	assert.NoError(t, err)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 执行请求
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头
	assert.Equal(t, "text/event-stream", w.Header().Get("Content-Type"))
	assert.Equal(t, "no-cache", w.Header().Get("Cache-Control"))
	assert.Equal(t, "keep-alive", w.Header().Get("Connection"))

	// 验证响应体包含SSE格式的数据
	responseBody := w.Body.String()
	assert.Contains(t, responseBody, "data:")
}

// TestStreamingResultAggregator 测试流式结果聚合器
func TestStreamingResultAggregator(t *testing.T) {
	// 注意：这里我们需要通过service包来访问聚合器
	// 由于聚合器是在service包中定义的，我们创建一个简单的测试

	// 测试聚合器的概念
	var transcriptionBuffer strings.Builder
	var translationBuffer strings.Builder

	// 模拟添加转录结果
	transcriptionBuffer.WriteString("Hello")
	transcriptionBuffer.WriteString(" world")

	assert.Equal(t, "Hello world", transcriptionBuffer.String())

	// 模拟添加翻译结果
	translationBuffer.WriteString("你好")
	translationBuffer.WriteString("世界")

	assert.Equal(t, "你好世界", translationBuffer.String())
}

// TestStreamingTranscriptionResult 测试流式转录结果
func TestStreamingTranscriptionResult(t *testing.T) {
	// 测试部分转录结果
	partialResult := qwen.StreamingTranscriptionResult{
		Text:      "Hello",
		IsPartial: true,
		IsEnd:     false,
	}

	assert.Equal(t, "Hello", partialResult.Text)
	assert.True(t, partialResult.IsPartial)
	assert.False(t, partialResult.IsEnd)
	assert.Empty(t, partialResult.Error)

	// 测试完整转录结果
	completeResult := qwen.StreamingTranscriptionResult{
		Text:      "Hello world",
		IsPartial: false,
		IsEnd:     false,
	}

	assert.Equal(t, "Hello world", completeResult.Text)
	assert.False(t, completeResult.IsPartial)
	assert.False(t, completeResult.IsEnd)
	assert.Empty(t, completeResult.Error)

	// 测试结束结果
	endResult := qwen.StreamingTranscriptionResult{
		Text:      "",
		IsPartial: false,
		IsEnd:     true,
	}

	assert.Empty(t, endResult.Text)
	assert.False(t, endResult.IsPartial)
	assert.True(t, endResult.IsEnd)
	assert.Empty(t, endResult.Error)
}

// TestConcurrentPipelineFlow 测试并发管道流程
func TestConcurrentPipelineFlow(t *testing.T) {
	// 模拟转录结果通道
	transcriptionChan := make(chan qwen.StreamingTranscriptionResult, 5)

	// 发送模拟转录结果
	go func() {
		defer close(transcriptionChan)

		// 发送部分结果
		transcriptionChan <- qwen.StreamingTranscriptionResult{
			Text:      "你好",
			IsPartial: false,
			IsEnd:     false,
		}

		// 发送另一个完整结果
		transcriptionChan <- qwen.StreamingTranscriptionResult{
			Text:      "世界",
			IsPartial: false,
			IsEnd:     false,
		}

		// 发送结束标记
		transcriptionChan <- qwen.StreamingTranscriptionResult{
			Text:      "",
			IsPartial: false,
			IsEnd:     true,
		}
	}()

	// 收集结果
	var results []qwen.StreamingTranslateResult
	timeout := time.After(5 * time.Second)

	// 注意：这里我们不能直接测试实际的翻译管道，因为它需要真实的API调用
	// 所以我们只测试数据结构和流程

	for {
		select {
		case result, ok := <-transcriptionChan:
			if !ok {
				goto done
			}

			// 模拟处理转录结果
			if result.IsEnd {
				// 模拟发送结束标记
				mockResult := qwen.StreamingTranslateResult{
					Content:   "",
					IsPartial: false,
					IsEnd:     true,
				}
				results = append(results, mockResult)
				goto done
			} else if result.Text != "" {
				// 模拟翻译结果
				mockResult := qwen.StreamingTranslateResult{
					Content:   "Translated: " + result.Text,
					IsPartial: result.IsPartial,
					IsEnd:     false,
				}
				results = append(results, mockResult)
			}
		case <-timeout:
			t.Log("Test timeout")
			goto done
		}
	}

done:
	// 验证结果
	assert.Greater(t, len(results), 0, "Should receive at least one result")

	// 验证最后一个结果是结束标志
	if len(results) > 0 {
		lastResult := results[len(results)-1]
		assert.True(t, lastResult.IsEnd, "Last result should be end marker")
	}
}

// BenchmarkConcurrentStreamingVoiceTranslate 并发流式语音翻译性能测试
func BenchmarkConcurrentStreamingVoiceTranslate(b *testing.B) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/concurrentStreamingVoiceTranslate", api.ConcurrentStreamingVoiceTranslate)

	// 创建测试音频数据
	testAudioData := make([]byte, 2048)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		writer := multipart.NewWriter(&buf)

		part, _ := writer.CreateFormFile("file", "test.pcm")
		part.Write(testAudioData)
		writer.WriteField("sourceLanguage", "zh")
		writer.WriteField("targetLanguages", "en")
		writer.Close()

		req, _ := http.NewRequest("POST", "/v1/concurrentStreamingVoiceTranslate", &buf)
		req.Header.Set("Content-Type", writer.FormDataContentType())

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

// Helper function to create test audio file
func createTestAudioFile(t *testing.T) *os.File {
	tmpFile, err := os.CreateTemp("", "test_audio_*.pcm")
	assert.NoError(t, err)

	// Write some test audio data
	testData := make([]byte, 1024)
	for i := range testData {
		testData[i] = byte(i % 256)
	}

	_, err = tmpFile.Write(testData)
	assert.NoError(t, err)

	_, err = tmpFile.Seek(0, io.SeekStart)
	assert.NoError(t, err)

	return tmpFile
}

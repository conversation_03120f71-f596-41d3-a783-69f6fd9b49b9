package ear

import (
	"airAi/common/requst"
	airRsq "airAi/common/response"
	"airAi/core/consts"
	"airAi/global"
	"airAi/other_api/deepseek"
	"airAi/other_api/qwen"
	"airAi/utils"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"model/common/response"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// 升级HTTP连接到WebSocket
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有跨域请求
	},
}

type ControlMessage struct {
	Action  string          `json:"action"`  // start, stop, config
	Payload json.RawMessage `json:"payload"` // 根据不同action有不同的结构
}

type AudioConfig struct {
	SampleRate  int    `json:"sample_rate"`
	Channels    int    `json:"channels"`
	Language    string `json:"language"`
	TranslateTo string `json:"translate_to"`
}

// Chat
// @Tags     Ear
// @Summary  聊天推送 websocket
// @Produce   application/json
// @Success  200   {object}  response.Response{data=string,msg=string}  "返回消息返回数据"
// @Router   /v1/chat [get]
func (b *AirportApi) Chat(c *gin.Context) {
	// 升级HTTP连接到WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket upgrade failed:", zap.Error(err))
		return
	}
	defer func() {
		global.WsClient.Delete(conn)
		conn.Close()
	}()
	global.WsClient.Store(conn, true)
	// WebSocket通信循环
	for {
		// 读取客户端发送的消息
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			global.GVA_LOG.Error("WebSocket read failed:", zap.Any("err", err))
			break
		}
		switch messageType {
		case websocket.TextMessage:
			// 处理文本控制消息
			//if err := handleTextMessage(conn, asrEngine, message); err != nil {
			//	log.Println("处理控制消息错误:", err)
			//	continue
			//}
			err = handleChat(conn, messageType, message)
			if err != nil {
				log.Println("处理控制消息错误:", err)
				continue
			}

		case websocket.BinaryMessage:
			// 处理二进制音频数据
			//if err := handleAudioData(conn, asrEngine, message); err != nil {
			//	log.Println("处理音频数据错误:", err)
			//	continue
			//}

		default:
			log.Println("收到不支持的消息类型:", messageType)
		}
		//var writeErr error
		//if string(message) == "ping" {
		//	writeErr = handlePing(conn, messageType)
		//} else {
		//	writeErr = handleChat(conn, messageType, message)
		//}
		//
		//if writeErr != nil {
		//	global.GVA_LOG.Error("WebSocket write failed", zap.Error(writeErr))
		//	break
		//}

	}
}

//func handleTextMessage(conn *websocket.Conn, asr *ASREngine, data []byte) error {
//	var msg ControlMessage
//	if err := json.Unmarshal(data, &msg); err != nil {
//		return fmt.Errorf("JSON解析失败: %w", err)
//	}
//
//	switch msg.Action {
//	case "start":
//		var config AudioConfig
//		if err := json.Unmarshal(msg.Payload, &config); err != nil {
//			return fmt.Errorf("配置解析失败: %w", err)
//		}
//		return asr.StartSession(config)
//
//	case "stop":
//		return asr.StopSession()
//
//	case "config":
//		var config AudioConfig
//		if err := json.Unmarshal(msg.Payload, &config); err != nil {
//			return fmt.Errorf("配置解析失败: %w", err)
//		}
//		return asr.UpdateConfig(config)
//
//	default:
//		return fmt.Errorf("未知操作类型: %s", msg.Action)
//	}
//}

//func handleAudioData(conn *websocket.Conn, asr *ASREngine, data []byte) error {
//	if !asr.IsSessionActive() {
//		return errors.New("会话未激活，请先发送start命令")
//	}
//
//	// 处理音频数据
//	result, err := asr.ProcessAudioChunk(data)
//	if err != nil {
//		return fmt.Errorf("音频处理失败: %w", err)
//	}
//
//	// 返回识别结果
//	if result != nil {
//		return sendResult(conn, result)
//	}
//	return nil
//}

//func sendResult(conn *websocket.Conn, result *ASRResult) error {
//	response := map[string]interface{}{
//		"type": "result",
//		"data": map[string]string{
//			"transcript":  result.Text,
//			"translation": result.Translation,
//		},
//	}
//
//	jsonData, err := json.Marshal(response)
//	if err != nil {
//		return fmt.Errorf("JSON编码失败: %w", err)
//	}
//
//	return conn.WriteMessage(websocket.TextMessage, jsonData)
//}

func handlePing(conn *websocket.Conn, messageType int) error {
	return conn.WriteMessage(messageType, []byte("pong"))
}

func handleChat(conn *websocket.Conn, messageType int, message []byte) error {
	client := deepseek.NewClient()

	chatReq := deepseek.ChatRequest{
		Model: "deepseek-chat",
		Messages: []deepseek.Message{
			{Role: "user", Content: string(message)},
		},
		MaxTokens: 2048,
	}

	rsp, err := client.Chat(chatReq)
	if err != nil {
		return conn.WriteMessage(messageType, []byte(err.Error()))
	}

	if rsp.Error != nil {
		return conn.WriteMessage(messageType, []byte(rsp.Error.Message))
	}

	if len(rsp.Choices) > 0 {
		return conn.WriteMessage(messageType, []byte(rsp.Choices[0].Message.Content))
	}

	return conn.WriteMessage(messageType, []byte("No response from model"))
}

// WsAudioRecognize WebSocket音频识别接口（千问AI）
// @Tags     Ear
// @Summary  WebSocket音频识别（千问AI）
// @Description  前端通过WebSocket发送音频数据，服务端实时识别并返回文本和翻译结果
// @Router   /ws/audioRecognize [get]
func (b *AirportApi) WsAudioRecognize(c *gin.Context) {
	// 1. 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket连接升级失败", zap.Error(err))
		return
	}

	// 使用改进的WebSocket处理器
	handler := NewWebSocketAudioHandler(conn, c.Request.Context())
	defer handler.Close()

	// 启动音频识别处理
	if err := handler.HandleAudioRecognition(); err != nil {
		global.GVA_LOG.Error("WebSocket音频识别处理失败", zap.Error(err))
	}
}

// WsAudio WebSocket音频识别接口（千问AI）
// @Tags     Ear
// @Summary  WebSocket音频识别（千问AI）
// @Description  前端通过WebSocket发送音频数据，服务端实时识别返回文结果
// @Router   /ws/audio [get]
func (b *AirportApi) WsAudio(c *gin.Context) {
	// 1. 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		global.GVA_LOG.Error("WebSocket连接升级失败", zap.Error(err))
		return
	}

	// 使用改进的WebSocket处理器
	handler := NewWebSocketAudioHandler(conn, c.Request.Context())
	defer handler.Close()

	// 启动音频识别处理
	if err := handler.HandleAudioRecognition(); err != nil {
		global.GVA_LOG.Error("WebSocket音频识别处理失败", zap.Error(err))
	}
}

// SyncChat
// @Tags     Ear
// @Summary  http 请求对话
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.ChatRequest           true  "account"                                                            true
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "返回消息返回数据"
// @Router   /v1/syncChat [post]
func (b *AirportApi) SyncChat(c *gin.Context) {
	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.ChatVerify)
	if err != nil {
		response.FailWithValidationErrorFromUtils(c, err)
		return
	}
	client := deepseek.NewClient()

	chatReq := deepseek.ChatRequest{
		Model: "deepseek-chat",
		Messages: []deepseek.Message{
			{Role: "user", Content: l.Text},
		},
		MaxTokens: 2048,
	}

	rsp, err := client.Chat(chatReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if rsp.Error != nil {
		response.FailWithMessage(rsp.Error.Message, c)
		return
	}

	if len(rsp.Choices) <= 0 {
		response.FailWithMessage("No response from model", c)
		return
	}
	date := make(map[string]interface{})
	date["info"] = rsp.Choices[0].Message.Content
	response.OkWithData(date, c)
}

// Meeting
// @Tags     Ear
// @Summary  http 会议纪要/语音翻译
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "返回消息返回数据"
// @Router   /v1/meeting [post]
func (b *AirportApi) Meeting(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
		return
	}
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_OPEN, "file_open_failed")
		return
	}
	defer src.Close()
	asr, err := airportService.Asr(sourceLanguage, targetLanguages, src)
	if err != nil {
		response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")
		return
	}
	if asr == nil {
		response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")
		return
	}
	recognized, ok := asr.Texts["recognized"]
	if !ok {
		response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")
		return
	}
	// 验证语言参数
	if err := utils.ValidateMeetingLanguage(targetLanguages); err != nil {
		response.FailWithValidationError(c, "invalid_language")
		return
	}

	meeting, err := airportService.Meeting(targetLanguages, recognized)
	if err != nil {
		response.FailWithMeetingError(c, consts.ERROR_MEETING_FAILED, "meeting_failed")
		return
	}
	data := make(map[string]interface{})
	data["meeting"] = meeting
	data["recognized"] = recognized
	response.OkWithI18n(c, data)
}

// TextMeeting
// @Tags     Ear
// @Summary  http 文字转会议纪要（支持多语言）
// @Description 根据输入的文字内容生成结构化的会议纪要，支持多种语言输出
// @accept    multipart/form-data
// @Produce   application/json
// @Param     language  formData  string false "会议纪要输出语言，如:zh(中文),en(英文),ja(日语),ko(韩语)等，默认zh"
// @Param     text  formData  string true "需要转换为会议纪要的文字内容"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "返回消息返回数据"
// @Router   /v1/textMeeting [post]
func (b *AirportApi) TextMeeting(c *gin.Context) {
	// 获取语言参数，优先使用新的language参数，兼容旧的targetLanguages参数
	language := c.DefaultPostForm("language", "")
	if language == "" {
		// 兼容性处理：如果没有language参数，使用targetLanguages参数
		language = c.DefaultPostForm("targetLanguages", utils.GetDefaultMeetingLanguage())
	}

	text := c.DefaultPostForm("text", "")
	if text == "" {
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 验证语言参数
	if err := utils.ValidateMeetingLanguage(language); err != nil {
		response.FailWithValidationError(c, "invalid_language")
		return
	}

	// 生成会议纪要
	meeting, err := airportService.Meeting(language, text)
	if err != nil {
		response.FailWithMeetingError(c, consts.ERROR_MEETING_FAILED, "meeting_failed")
		return
	}

	data := make(map[string]interface{})
	data["meeting"] = meeting
	data["language"] = language // 返回使用的语言参数
	response.OkWithI18n(c, data)
}

// AudioTranslate
// @Tags     Ear
// @Summary  http 语音翻译
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "返回消息返回数据"
// @Router   /v1/audioTranslate [post]
func (b *AirportApi) AudioTranslate(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
		return
	}
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_OPEN, "file_open_failed")
		return
	}
	defer src.Close()
	data, err := airportService.Asr(sourceLanguage, targetLanguages, src)
	if err != nil {
		response.FailWithTranslateError(c, consts.ERROR_TRANSLATE_FAILED, "translate_failed")
		return
	}

	response.OkWithI18n(c, data)
}

// Translate
// @Tags     Ear
// @Summary  文字翻译
// @accept    multipart/form-data
// @Produce   application/json
// @Param     text  formData  string true "需要翻译的文字"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {object}  response.Response{data=map[string]interface{},msg=string}  "返回消息返回数据"
// @Router   /v1/translate [post]
func (b *AirportApi) Translate(c *gin.Context) {
	text := c.PostForm("text")
	if text == "" {
		response.FailWithValidationError(c, "missing_params")
		return
	}
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	data, err := airportService.Translate(sourceLanguage, targetLanguages, text)
	if err != nil {
		response.FailWithTranslateError(c, consts.ERROR_TRANSLATE_FAILED, "translate_failed")
		return
	}

	response.OkWithI18n(c, data)
}

// TextToSpeech
// @Tags     Ear
// @Summary  讯飞文字转语音
// @Produce   application/json
// @Param     data  body    requst.ChatRequest           true  "text,speechType"
// @Success  200   {object}  response.Response{data=airRsq.TtsResponse,msg=string}  "返回消息返回数据"
// @Router   /v1/xfTtsChat [post]
func (b *AirportApi) TextToSpeech(c *gin.Context) {
	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithValidationError(c, "missing_params")
		return
	}
	err := utils.Verify(l, utils.ChatVerify)
	if err != nil {
		response.FailWithValidationError(c, "invalid_params")
		return
	}
	audioData, err := airportService.TextToSpeech(l.Text)
	if err != nil {
		response.FailWithTTSError(c, 1600, "tts_failed")
		return
	}
	// 3. 保存音频文件
	//outputFile := filepath.Join(".", "output.mp3")
	//if err := os.WriteFile(outputFile, audioData, 0644); err != nil {
	//	fmt.Printf("保存音频文件失败: %v\n", err)
	//	return
	//}
	response.OkWithData(airRsq.TtsResponse{Output: audioData}, c)
}

// TToSpeech
// @Tags     Ear
// @Summary  阿里文字转语音
// @Produce   application/json
// @Param     data  body    requst.ChatRequest           true  "text,speechType,speechRate"
// @Success  200   {object}  response.Response{data=airRsq.TtsResponse,msg=string}  "返回消息返回数据"
// @Router   /v1/tts [post]
func (b *AirportApi) TToSpeech(c *gin.Context) {
	start := time.Now()
	global.GVA_LOG.Info("接收到TTS消息",
		zap.String("time", start.Format("2006-01-02 15:04:05.000")),
	)
	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		global.GVA_LOG.Error("TToSpeech error", zap.Error(err))
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 设置默认语音速率
	if l.SpeechRate == 0 {
		l.SpeechRate = 1.0 // 默认正常语速
	}

	// 使用TTS专用验证规则
	err := utils.Verify(l, utils.TtsVerify)
	if err != nil {
		// 检查是否为语音速率验证错误，提供特定的国际化错误消息
		if strings.Contains(err.Error(), "SpeechRate") {
			if strings.Contains(err.Error(), "ge=0.5") {
				response.FailWithValidationError(c, "speech_rate_too_slow")
			} else if strings.Contains(err.Error(), "le=2.0") {
				response.FailWithValidationError(c, "speech_rate_too_fast")
			} else {
				response.FailWithValidationError(c, "speech_rate_invalid")
			}
		} else {
			// 其他验证错误使用统一的验证错误处理器
			response.FailWithValidationErrorFromUtils(c, err)
		}
		return
	}

	global.GVA_LOG.Info("TTS参数",
		zap.String("text", l.Text),
		zap.Float64("speechRate", l.SpeechRate),
	)

	audioData, err := airportService.TToSpeechWithRate(l.Text, l.SpeechRate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(airRsq.TtsResponse{Output: audioData}, c)
	duration := time.Since(start)
	global.GVA_LOG.Info("TTS接口处理完毕",
		zap.Duration("耗时", duration),
		zap.Float64("语音速率", l.SpeechRate),
	)
}

// TextChat
// @Tags     Ear
// @Summary  文字聊天
// @Produce   application/json
// @Param data body requst.ChatRequest true  "text,speechType"
// @Success  200   {object}  response.Response{data=string,msg=string}  "返回消息返回数据"
// @Router   /v1/textChat [post]
func (b *AirportApi) TextChat(c *gin.Context) {
	start := time.Now()

	global.GVA_LOG.Info("接收到TextChat消息",
		zap.String("time", start.Format("2006-01-02 15:04:05.000")),
	)
	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithValidationError(c, "missing_params")
		return
	}
	err := utils.Verify(l, utils.ChatVerify)
	if err != nil {
		response.FailWithValidationError(c, "invalid_params")
		return
	}
	text, err := airportService.TextChat(l.SpeechType, l.Text)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(airRsq.RealTime{Text: text}, c)
	duration := time.Since(start)
	global.GVA_LOG.Info("接口处理完毕",
		zap.Duration("耗时", duration),
	)
}

// StreamTextChat
// @Tags     Ear
// @Summary  智能文字聊天接口 - 支持流式和非流式响应，带对话上下文功能
// @Description 根据请求参数中的stream字段决定返回方式：stream=true时返回流式响应，stream=false时返回普通响应。支持对话上下文功能，可传入历史消息和会话ID实现多轮对话。
// @Produce   application/json
// @Produce   text/event-stream
// @Param data body requst.ChatRequest true  "text,speechType,stream,conversationId,messages,contextWindow,systemPrompt"
// @Success  200   {object}  response.Response{data=string,msg=string}  "返回消息返回数据"
// @Router   /v1/streamTextChat [post]
func (b *AirportApi) StreamTextChat(c *gin.Context) {
	start := time.Now()

	global.GVA_LOG.Info("接收到StreamTextChat消息",
		zap.String("time", start.Format("2006-01-02 15:04:05.000")),
	)

	// 解析请求参数
	var l requst.ChatRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 检测是否使用对话上下文功能
	// 如果提供了对话上下文相关字段，则使用上下文验证规则
	useContext := l.ConversationID != "" || len(l.Messages) > 0 || l.ContextWindow > 0 || l.SystemPrompt != ""

	// 根据是否使用上下文选择相应的验证规则
	if useContext {
		// 使用上下文聊天验证规则
		err := utils.Verify(l, utils.ContextChatVerify)
		if err != nil {
			response.FailWithValidationErrorFromUtils(c, err)
			return
		}

		// 验证通过后，设置默认上下文窗口大小
		if l.ContextWindow <= 0 {
			l.ContextWindow = 10 // 默认保留最近10条消息
		}
	} else {
		// 使用传统聊天验证规则
		err := utils.Verify(l, utils.ChatVerify)
		if err != nil {
			response.FailWithValidationErrorFromUtils(c, err)
			return
		}
	}

	// 根据stream参数决定响应方式
	if l.Stream {
		// 流式响应模式：设置SSE响应头并进行流式处理
		global.GVA_LOG.Info("启用流式响应模式",
			zap.String("speechType", l.SpeechType),
			zap.String("text", l.Text),
			zap.Bool("useContext", useContext),
			zap.String("conversationId", l.ConversationID),
		)

		// 设置SSE响应头
		c.Header("Content-Type", "text/event-stream")
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Headers", "Cache-Control")

		// 检查是否支持流式传输
		flusher, ok := c.Writer.(http.Flusher)
		if !ok {
			response.FailWithMessage("Streaming unsupported", c)
			return
		}

		// 创建结果通道
		resultChan := make(chan interface{}, 10)

		// 创建带超时的上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
		defer cancel()

		// 启动流式文本聊天
		go func() {
			defer func() {
				defer func() {
					if r := recover(); r != nil {
						// 通道已经关闭，忽略 panic
					}
				}()
				close(resultChan)
			}()

			var err error
			var actualConversationID string

			if useContext {
				// 使用带上下文的流式聊天
				actualConversationID, err = airportService.StreamingTextChatWithContext(
					l.SpeechType,
					l.Text,
					l.ConversationID,
					l.Messages,
					l.ContextWindow,
					l.SystemPrompt,
					resultChan,
				)

				// 如果成功获取到对话ID，发送包含对话ID的初始响应
				if err == nil && actualConversationID != "" {
					select {
					case resultChan <- map[string]interface{}{
						"conversation_id": actualConversationID,
						"content":         "",
						"is_partial":      true,
						"is_end":          false,
						"context_info":    true, // 标识这是上下文信息
					}:
					case <-ctx.Done():
						return
					default:
					}
				}
			} else {
				// 使用传统的流式聊天（向后兼容）
				err = airportService.StreamingTextChat(l.SpeechType, l.Text, resultChan)
			}

			if err != nil {
				global.GVA_LOG.Error("StreamingTextChat error", zap.Error(err))
				// 发送错误信息到结果通道
				select {
				case resultChan <- map[string]interface{}{
					"content":    "",
					"is_partial": false,
					"is_end":     true,
					"error":      err.Error(),
				}:
				case <-ctx.Done():
					return
				default:
				}
			}
		}()

		// 发送流式SSE结果
		b.streamTextChatSSEResults(c, flusher, resultChan, ctx)

	} else {
		// 普通响应模式：直接返回完整结果
		global.GVA_LOG.Info("使用普通响应模式",
			zap.String("speechType", l.SpeechType),
			zap.String("text", l.Text),
			zap.Bool("useContext", useContext),
		)

		if useContext {
			// 使用带上下文的聊天（非流式）
			// 创建临时结果通道收集完整响应
			resultChan := make(chan interface{}, 10)
			var fullResponse strings.Builder
			var actualConversationID string

			// 启动上下文聊天
			go func() {
				defer close(resultChan)
				var err error
				actualConversationID, err = airportService.StreamingTextChatWithContext(
					l.SpeechType,
					l.Text,
					l.ConversationID,
					l.Messages,
					l.ContextWindow,
					l.SystemPrompt,
					resultChan,
				)
				if err != nil {
					select {
					case resultChan <- map[string]interface{}{
						"error":  err.Error(),
						"is_end": true,
					}:
					default:
					}
				}
			}()

			// 收集所有响应片段
			for result := range resultChan {
				if resultMap, ok := result.(map[string]interface{}); ok {
					if errorMsg, hasError := resultMap["error"]; hasError {
						response.FailWithMessage(fmt.Sprintf("%v", errorMsg), c)
						return
					}
					if content, hasContent := resultMap["content"]; hasContent {
						if contentStr, ok := content.(string); ok && contentStr != "" {
							fullResponse.WriteString(contentStr)
						}
					}
					if isEnd, hasEnd := resultMap["is_end"]; hasEnd && isEnd == true {
						break
					}
				}
			}

			// 构建响应数据
			responseData := airRsq.RealTime{Text: fullResponse.String()}
			if actualConversationID != "" {
				// 如果有对话ID，添加到响应中
				data := map[string]interface{}{
					"text":            fullResponse.String(),
					"conversation_id": actualConversationID,
				}
				response.OkWithData(data, c)
			} else {
				response.OkWithData(responseData, c)
			}
		} else {
			// 使用传统聊天（向后兼容）
			text, err := airportService.TextChat(l.SpeechType, l.Text)
			if err != nil {
				response.FailWithMessage(err.Error(), c)
				return
			}

			response.OkWithData(airRsq.RealTime{Text: text}, c)
		}
	}

	duration := time.Since(start)
	global.GVA_LOG.Info("StreamTextChat接口处理完毕",
		zap.Duration("耗时", duration),
		zap.Bool("stream", l.Stream),
	)
}

// StreamingVoiceTranslate
// @Tags     Ear
// @Summary  流式语音翻译 - 返回ASR和翻译结果（无TTS音频生成）
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {string}  string  "Server-Sent Events流式返回ASR转录和翻译结果"
// @Router   /v1/streamingVoiceTranslate [post]
func (b *AirportApi) StreamingVoiceTranslate(c *gin.Context) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 检查是否支持流式传输
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithMessage("Streaming unsupported", c)
		return
	}

	b.handleTraditionalUpload(c, flusher)
}

// handleStreamingMultipartUpload 处理流式多部分上传
func (b *AirportApi) handleStreamingMultipartUpload(c *gin.Context, flusher http.Flusher) error {
	// 解析多部分表单
	reader, err := c.Request.MultipartReader()
	if err != nil {
		return fmt.Errorf("failed to create multipart reader: %v", err)
	}

	var sourceLanguage = "zh"  // 默认值
	var targetLanguages = "en" // 默认值
	var audioReader *io.PipeReader
	var audioWriter *io.PipeWriter

	// 创建结果通道
	resultChan := make(chan qwen.StreamingTranslateResult, 100)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 处理多部分表单的各个部分
	for {
		part, err := reader.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read multipart: %v", err)
		}

		switch part.FormName() {
		case "sourceLanguage":
			buf := make([]byte, 1024)
			n, _ := part.Read(buf)
			if n > 0 {
				sourceLanguage = strings.TrimSpace(string(buf[:n]))
			}

		case "targetLanguages":
			buf := make([]byte, 1024)
			n, _ := part.Read(buf)
			if n > 0 {
				targetLanguages = strings.TrimSpace(string(buf[:n]))
			}

		case "file":
			// 创建管道用于流式处理音频数据
			audioReader, audioWriter = io.Pipe()

			// 启动音频流式处理
			go func() {
				// 使用 sync.Once 确保 resultChan 只被关闭一次
				var closeOnce sync.Once
				closeResultChan := func() {
					closeOnce.Do(func() {
						close(resultChan)
					})
				}

				defer func() {
					if r := recover(); r != nil {
						// 处理 panic，确保通道被关闭
						closeResultChan()
					}
				}()

				// 启动基础的流式语音翻译（仅ASR和翻译，无TTS）
				go func() {
					defer closeResultChan()
					err := airportService.StreamingVoiceTranslate(sourceLanguage, targetLanguages, audioReader, resultChan)
					if err != nil {
						select {
						case resultChan <- qwen.StreamingTranslateResult{
							Content:      "",
							OriginalText: "",
							IsPartial:    false,
							IsEnd:        true,
							Error:        err.Error(),
						}:
						case <-ctx.Done():
							return
						default:
						}
					}
				}()
			}()

			// 启动SSE结果发送
			go b.streamSSEResults(c, flusher, resultChan, ctx)

			// 流式读取并处理音频数据
			return b.streamAudioData(part, audioWriter, ctx)
		}

		part.Close()
	}

	return fmt.Errorf("no audio file found in multipart form")
}

func (b *AirportApi) handleTraditionalUpload(c *gin.Context, flusher http.Flusher) {
	file, err := c.FormFile("file")
	if err != nil {
		fmt.Fprintf(c.Writer, "data: %s\n\n",
			fmt.Sprintf(`{"error":"Failed to upload: %s","is_end":true}`, err.Error()))
		flusher.Flush()
		return
	}

	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		fmt.Fprintf(c.Writer, "data: %s\n\n",
			fmt.Sprintf(`{"error":"Failed to open: %s","is_end":true}`, err.Error()))
		flusher.Flush()
		return
	}
	defer src.Close()

	// 创建结果通道
	resultChan := make(chan qwen.Event, 100)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 启动基础的流式语音翻译（仅ASR和翻译，无TTS）
	go func() {
		defer func() {
			defer func() {
				if r := recover(); r != nil {
					// 通道已经关闭，忽略 panic
				}
			}()
			close(resultChan)
		}()

		err := airportService.StreamingVoiceTranslate1(sourceLanguage, targetLanguages, src, resultChan)
		if err != nil {
			fmt.Println("StreamingVoiceTranslate1 error:", err.Error())
			//select {
			//case resultChan <- qwen.StreamingTranslateResult{
			//	Content:      "",
			//	OriginalText: "",
			//	IsPartial:    false,
			//	IsEnd:        true,
			//	Error:        err.Error(),
			//}:
			//case <-ctx.Done():
			//	return
			//default:
			//}
		}
	}()

	// 发送SSE结果
	b.streamSSEResults1(c, flusher, resultChan, ctx)
}

// streamSSEResults 流式发送SSE结果
func (b *AirportApi) streamSSEResults(c *gin.Context, flusher http.Flusher, resultChan <-chan qwen.StreamingTranslateResult, ctx context.Context) {
	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				return
			}
			fmt.Println("result = ", result)

			// 将结果转换为JSON
			jsonData, err := json.Marshal(result)
			if err != nil {
				continue
			}

			// 发送SSE事件
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			flusher.Flush()

			// 如果是结束事件，退出循环
			if result.IsEnd {
				return
			}

		case <-ctx.Done():
			// 请求被取消或超时
			fmt.Fprintf(c.Writer, "data: %s\n\n", `{"error":"Request timeout or cancelled","is_end":true}`)
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

func (b *AirportApi) streamSSEResults1(c *gin.Context, flusher http.Flusher, resultChan <-chan qwen.Event, ctx context.Context) {
	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				return
			}
			fmt.Println("result = ", result)

			// 将结果转换为JSON
			jsonData, err := json.Marshal(result)
			if err != nil {
				continue
			}

			// 发送SSE事件
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			flusher.Flush()

			// 如果是结束事件，退出循环
			if result.IsEnd {
				return
			}

		case <-ctx.Done():
			// 请求被取消或超时
			fmt.Fprintf(c.Writer, "data: %s\n\n", `{"error":"Request timeout or cancelled","is_end":true}`)
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

// streamAudioData 流式处理音频数据
func (b *AirportApi) streamAudioData(part *multipart.Part, audioWriter *io.PipeWriter, ctx context.Context) error {
	defer audioWriter.Close()

	// 使用缓冲区进行分块读取
	buffer := make([]byte, 8192) // 8KB 缓冲区

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 读取音频数据块
		n, err := part.Read(buffer)
		if n > 0 {
			// 立即写入管道，触发ASR处理
			_, writeErr := audioWriter.Write(buffer[:n])
			if writeErr != nil {
				return fmt.Errorf("failed to write audio data: %v", writeErr)
			}
		}

		if err == io.EOF {
			// 音频数据读取完成
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read audio data: %v", err)
		}
	}

	return nil
}

// LegacyStreamingVoiceTranslate
// @Tags     Ear
// @Summary  传统流式语音翻译（等待完整上传后处理）
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {string}  string  "Server-Sent Events流式返回翻译结果"
// @Router   /v1/legacyStreamingVoiceTranslate [post]
func (b *AirportApi) LegacyStreamingVoiceTranslate(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
		return
	}
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_OPEN, "file_open_failed")
		return
	}
	defer src.Close()

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建结果通道
	resultChan := make(chan qwen.StreamingTranslateResult, 100)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 启动流式翻译
	go func() {
		defer func() {
			defer func() {
				if r := recover(); r != nil {
					// 通道已经关闭，忽略 panic
				}
			}()
			close(resultChan)
		}()

		err := airportService.StreamingVoiceTranslate(sourceLanguage, targetLanguages, src, resultChan)
		if err != nil {
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			case <-ctx.Done():
				return
			default:
			}
		}
	}()

	// 发送SSE事件
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithStreamError(c, consts.ERROR_STREAM_FAILED, "stream_failed")
		return
	}

	b.streamSSEResults(c, flusher, resultChan, ctx)
}

// ConcurrentStreamingVoiceTranslate
// @Tags     Ear
// @Summary  并发流式语音翻译（实时ASR+翻译）
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {string}  string  "Server-Sent Events流式返回实时翻译结果"
// @Router   /v1/concurrentStreamingVoiceTranslate [post]
func (b *AirportApi) ConcurrentStreamingVoiceTranslate(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithMessage("Failed to upload", c)
		return
	}
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithMessage("Failed to open", c)
		return
	}
	defer src.Close()

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建结果通道
	resultChan := make(chan qwen.StreamingTranslateResult, 10)

	// 启动并发流式翻译
	go func() {
		defer close(resultChan)
		err := airportService.ConcurrentStreamingVoiceTranslate(sourceLanguage, targetLanguages, src, resultChan)
		if err != nil {
			// 发送错误信息
			select {
			case resultChan <- qwen.StreamingTranslateResult{
				Content:   "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			default:
			}
		}
	}()

	// 发送SSE事件
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithMessage("Streaming unsupported", c)
		return
	}

	for result := range resultChan {
		// 将结果转换为JSON
		jsonData, err := json.Marshal(result)
		if err != nil {
			continue
		}

		// 发送SSE事件
		fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
		flusher.Flush()

		// 如果是结束事件，退出循环
		if result.IsEnd {
			break
		}
	}
}

// ComprehensiveStreamingVoiceTranslate
// @Tags     Ear
// @Summary  综合流式语音翻译 - 返回ASR、翻译和TTS结果
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {string}  string  "Server-Sent Events流式返回综合语音处理结果"
// @Router   /v1/comprehensiveStreamingVoiceTranslate [post]
func (b *AirportApi) ComprehensiveStreamingVoiceTranslate(c *gin.Context) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 获取参数
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithMessage("Failed to upload", c)
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithMessage("Failed to open", c)
		return
	}
	defer src.Close()

	// 创建结果通道
	resultChan := make(chan qwen.ComprehensiveVoiceResult, 100)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 启动流式翻译
	go func() {
		// 使用 sync.Once 确保 resultChan 只被关闭一次
		var closeOnce sync.Once
		closeResultChan := func() {
			closeOnce.Do(func() {
				close(resultChan)
			})
		}

		defer func() {
			if r := recover(); r != nil {
				// 处理 panic，确保通道被关闭
				closeResultChan()
			} else {
				closeResultChan()
			}
		}()

		err := airportService.ComprehensiveStreamingVoiceTranslate(sourceLanguage, targetLanguages, src, resultChan)
		if err != nil {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "error",
				IsPartial:      false,
				IsEnd:          true,
				Error:          err.Error(),
			}:
			case <-ctx.Done():
				return
			default:
			}
		}
	}()

	// 发送SSE结果
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithMessage("Streaming unsupported", c)
		return
	}

	b.streamComprehensiveSSEResults(c, flusher, resultChan, ctx)
}

// IncrementalStreamingVoiceTranslate
// @Tags     Ear
// @Summary  增量流式语音翻译 - 实时增量处理每个语音段
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频"
// @Param     sourceLanguage  formData  string false "来源语言，如:zh，默认zh"
// @Param     targetLanguages  formData  string false "需要翻译的语言，如:en，默认en"
// @Success  200   {string}  string  "Server-Sent Events流式返回增量语音处理结果"
// @Router   /v1/incrementalStreamingVoiceTranslate [post]
func (b *AirportApi) IncrementalStreamingVoiceTranslate(c *gin.Context) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 获取参数
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")
	targetLanguages := c.DefaultPostForm("targetLanguages", "en")

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithMessage("Failed to upload", c)
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithMessage("Failed to open", c)
		return
	}
	defer src.Close()

	// 创建结果通道
	resultChan := make(chan qwen.ComprehensiveVoiceResult, 100)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 启动增量流式翻译
	go func() {
		// 使用 sync.Once 确保 resultChan 只被关闭一次
		var closeOnce sync.Once
		closeResultChan := func() {
			closeOnce.Do(func() {
				close(resultChan)
			})
		}

		defer func() {
			if r := recover(); r != nil {
				// 处理 panic，确保通道被关闭
				closeResultChan()
			} else {
				closeResultChan()
			}
		}()

		err := airportService.IncrementalVoiceProcessing(sourceLanguage, targetLanguages, src, resultChan)
		if err != nil {
			select {
			case resultChan <- qwen.ComprehensiveVoiceResult{
				ProcessingType: "error",
				IsPartial:      false,
				IsEnd:          true,
				Error:          err.Error(),
			}:
			case <-ctx.Done():
				return
			default:
			}
		}
	}()

	// 发送SSE结果
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithMessage("Streaming unsupported", c)
		return
	}

	b.streamComprehensiveSSEResults(c, flusher, resultChan, ctx)
}

// streamComprehensiveSSEResults 流式发送综合语音处理SSE结果
func (b *AirportApi) streamComprehensiveSSEResults(c *gin.Context, flusher http.Flusher, resultChan <-chan qwen.ComprehensiveVoiceResult, ctx context.Context) {
	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				return
			}

			// 将结果转换为JSON
			jsonData, err := json.Marshal(result)
			if err != nil {
				continue
			}

			// 发送SSE事件
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			flusher.Flush()

			// 如果是结束事件，退出循环
			if result.IsEnd {
				return
			}

		case <-ctx.Done():
			// 请求被取消或超时
			fmt.Fprintf(c.Writer, "data: %s\n\n", `{"processing_type":"error","error":"Request timeout or cancelled","is_end":true}`)
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

// streamTextChatSSEResults 流式发送文本聊天SSE结果
// 用于处理AI文本聊天的流式响应，将结果以SSE格式发送给客户端
// 参数：
//   - c: Gin上下文对象
//   - flusher: HTTP响应刷新器，用于立即发送数据到客户端
//   - resultChan: 接收流式结果的通道
//   - ctx: 上下文对象，用于超时控制
func (b *AirportApi) streamTextChatSSEResults(c *gin.Context, flusher http.Flusher, resultChan <-chan interface{}, ctx context.Context) {
	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				// 通道已关闭，发送结束事件
				fmt.Fprintf(c.Writer, "data: %s\n\n", `{"content":"","is_partial":false,"is_end":true}`)
				flusher.Flush()
				return
			}

			// 将结果转换为JSON
			jsonData, err := json.Marshal(result)
			if err != nil {
				global.GVA_LOG.Error("Failed to marshal streaming result", zap.Error(err))
				continue
			}

			// 发送SSE事件
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			flusher.Flush()

			// 检查是否为结束事件
			if resultMap, ok := result.(map[string]interface{}); ok {
				if isEnd, exists := resultMap["is_end"]; exists && isEnd == true {
					return
				}
			}

			// 检查其他类型的结束标记
			switch v := result.(type) {
			case qwen.StreamingChatResult:
				if v.IsEnd {
					return
				}
			case deepseek.StreamingChatResult:
				if v.IsEnd {
					return
				}
			}

		case <-ctx.Done():
			// 请求被取消或超时
			global.GVA_LOG.Info("StreamTextChat request cancelled or timeout")
			fmt.Fprintf(c.Writer, "data: %s\n\n", `{"content":"","is_partial":false,"is_end":true,"error":"Request timeout or cancelled"}`)
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			global.GVA_LOG.Info("StreamTextChat client disconnected")
			return
		}
	}
}

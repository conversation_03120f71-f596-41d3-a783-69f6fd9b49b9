package ear

import (
	"strconv"
	"time"

	"airAi/common/requst"
	airRsq "airAi/common/response"
	"airAi/core/i18n"
	"airAi/global"
	"airAi/middleware"
	"airAi/service"
	"airAi/service/airport"
	"airAi/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var quotaService = service.ServiceGroupApp.AirportServiceGroup.QuotaService

// GetQuotaInfo
// @Tags     Quota
// @Summary  获取用户配额信息
// @Produce  application/json
// @Success  200  {object}  response.Response{data=object,msg=string}  "获取配额信息成功"
// @Router   /v1/quota/info [get]
func (b *AirportApi) GetQuotaInfo(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.TSimple(lang, "auth.unauthorized"), c)
		return
	}

	lang := i18n.GetLangFromContext(c)

	// 获取配额信息
	quota, err := quotaService.GetQuotaInfo(userID)
	if err != nil {
		global.GVA_LOG.Error("获取配额信息失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		errorMessage := i18n.T(lang, "quota.info_get_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 构建响应数据
	data := map[string]interface{}{
		"total_quota":         quota.GetTotalQuota(),
		"current_usage":       quota.CurrentUsage,
		"remaining":           quota.GetRemainingQuota(),
		"subscription_active": quota.IsSubscriptionActive(),
		"subscription_expiry": quota.SubscriptionExpiry,
		"last_reset_date":     quota.LastResetDate,
		"monthly_quota":       quota.MonthlyQuota,
		"extra_purchases":     quota.ExtraPurchases,
		"pricing_info": map[string]interface{}{
			"monthly_subscription_fee": airport.MonthlySubscriptionFee,
			"extra_quota_price":        airport.ExtraQuotaPrice,
			"extra_quota_unit":         airport.ExtraQuotaUnit,
		},
	}

	// 添加配额信息到响应中
	middleware.AddQuotaInfoToResponse(c, data)

	// 设置配额信息到响应头
	middleware.GetQuotaInfoHeader(c)

	global.GVA_LOG.Debug("获取配额信息成功",
		zap.Uint("user_id", userID),
		zap.Int("total_quota", quota.GetTotalQuota()),
		zap.Int("remaining", quota.GetRemainingQuota()))

	airRsq.OkWithData(data, c)
}

// PurchaseExtraQuota
// @Tags     Quota
// @Summary  购买额外配额
// @Accept   application/json
// @Produce  application/json
// @Param    data  body      requst.PurchaseQuotaRequest  true  "购买配额请求"
// @Success  200   {object}  response.Response{data=object,msg=string}  "购买配额成功"
// @Router   /v1/quota/purchase [post]
func (b *AirportApi) PurchaseExtraQuota(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.TSimple(lang, "auth.unauthorized"), c)
		return
	}

	var req requst.PurchaseQuotaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.T(lang, "validation.bind_failed", map[string]interface{}{
			"Error": err.Error(),
		}), c)
		return
	}

	lang := i18n.GetLangFromContext(c)

	// 验证购买单位数
	if req.Units <= 0 {
		errorMessage := i18n.TSimple(lang, "quota.invalid_units")
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 购买额外配额
	record, err := quotaService.PurchaseExtraQuota(userID, req.Units, c.ClientIP())
	if err != nil {
		global.GVA_LOG.Error("购买额外配额失败",
			zap.Uint("user_id", userID),
			zap.Int("units", req.Units),
			zap.Error(err))
		errorMessage := i18n.T(lang, "quota.purchase_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 获取更新后的配额信息
	quota, err := quotaService.GetQuotaInfo(userID)
	if err != nil {
		global.GVA_LOG.Error("获取更新后的配额信息失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		// 购买成功但获取配额信息失败，不影响主流程
	}

	// 构建响应数据
	data := map[string]interface{}{
		"purchase_record": map[string]interface{}{
			"order_id":      record.OrderID,
			"units":         record.PurchaseUnits,
			"amount":        record.Amount,
			"purchase_date": record.PurchaseDate,
			"description":   record.Description,
		},
		"message": i18n.TSimple(lang, "quota.purchase_success"),
	}

	if quota != nil {
		data["quota_info"] = map[string]interface{}{
			"total_quota":     quota.GetTotalQuota(),
			"current_usage":   quota.CurrentUsage,
			"remaining":       quota.GetRemainingQuota(),
			"extra_purchases": quota.ExtraPurchases,
		}
	}

	global.GVA_LOG.Info("购买额外配额成功",
		zap.Uint("user_id", userID),
		zap.Int("units", req.Units),
		zap.Float64("amount", record.Amount),
		zap.String("order_id", record.OrderID))

	airRsq.OkWithData(data, c)
}

// SubscribeMonthly
// @Tags     Quota
// @Summary  订阅月度服务
// @Produce  application/json
// @Success  200  {object}  response.Response{data=object,msg=string}  "订阅成功"
// @Router   /v1/quota/subscribe [post]
func (b *AirportApi) SubscribeMonthly(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.TSimple(lang, "auth.unauthorized"), c)
		return
	}

	lang := i18n.GetLangFromContext(c)

	// 订阅月度服务
	record, err := quotaService.SubscribeMonthly(userID, c.ClientIP())
	if err != nil {
		global.GVA_LOG.Error("订阅月度服务失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		errorMessage := i18n.T(lang, "quota.subscription_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 获取更新后的配额信息
	quota, err := quotaService.GetQuotaInfo(userID)
	if err != nil {
		global.GVA_LOG.Error("获取更新后的配额信息失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		// 订阅成功但获取配额信息失败，不影响主流程
	}

	// 构建响应数据
	data := map[string]interface{}{
		"subscription_record": map[string]interface{}{
			"order_id":      record.OrderID,
			"amount":        record.Amount,
			"purchase_date": record.PurchaseDate,
			"description":   record.Description,
		},
		"message": i18n.TSimple(lang, "quota.subscription_success"),
	}

	if quota != nil {
		data["quota_info"] = map[string]interface{}{
			"total_quota":         quota.GetTotalQuota(),
			"current_usage":       quota.CurrentUsage,
			"remaining":           quota.GetRemainingQuota(),
			"subscription_active": quota.IsSubscriptionActive(),
			"subscription_expiry": quota.SubscriptionExpiry,
		}
	}

	global.GVA_LOG.Info("订阅月度服务成功",
		zap.Uint("user_id", userID),
		zap.Float64("amount", record.Amount),
		zap.String("order_id", record.OrderID))

	airRsq.OkWithData(data, c)
}

// GetPurchaseHistory
// @Tags     Quota
// @Summary  获取购买历史
// @Produce  application/json
// @Param    page      query     int  false  "页码"  default(1)
// @Param    page_size query     int  false  "每页数量"  default(10)
// @Success  200       {object}  response.Response{data=object,msg=string}  "获取购买历史成功"
// @Router   /v1/quota/purchase/history [get]
func (b *AirportApi) GetPurchaseHistory(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.TSimple(lang, "auth.unauthorized"), c)
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	lang := i18n.GetLangFromContext(c)

	// 获取购买历史
	records, total, err := quotaService.GetPurchaseHistory(userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("获取购买历史失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		errorMessage := i18n.T(lang, "quota.purchase_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 构建响应数据
	data := map[string]interface{}{
		"records":     records,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	}

	global.GVA_LOG.Debug("获取购买历史成功",
		zap.Uint("user_id", userID),
		zap.Int64("total", total),
		zap.Int("page", page))

	airRsq.OkWithData(data, c)
}

// GetUsageHistory
// @Tags     Quota
// @Summary  获取使用历史
// @Produce  application/json
// @Param    page      query     int  false  "页码"  default(1)
// @Param    page_size query     int  false  "每页数量"  default(10)
// @Success  200       {object}  response.Response{data=object,msg=string}  "获取使用历史成功"
// @Router   /v1/quota/usage/history [get]
func (b *AirportApi) GetUsageHistory(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		lang := i18n.GetLangFromContext(c)
		airRsq.FailWithMessage(i18n.TSimple(lang, "auth.unauthorized"), c)
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	lang := i18n.GetLangFromContext(c)

	// 获取使用历史
	logs, total, err := quotaService.GetUsageHistory(userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("获取使用历史失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		errorMessage := i18n.T(lang, "quota.usage_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 构建响应数据
	data := map[string]interface{}{
		"logs":        logs,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	}

	global.GVA_LOG.Debug("获取使用历史成功",
		zap.Uint("user_id", userID),
		zap.Int64("total", total),
		zap.Int("page", page))

	airRsq.OkWithData(data, c)
}

// ForceResetQuota
// @Tags     Quota
// @Summary  强制重置月度配额（管理员功能）
// @Produce  application/json
// @Success  200  {object}  response.Response{data=object,msg=string}  "重置成功"
// @Router   /v1/quota/admin/reset [post]
func (b *AirportApi) ForceResetQuota(c *gin.Context) {
	// 这里应该添加管理员权限检查
	// TODO: 添加管理员权限验证

	lang := i18n.GetLangFromContext(c)

	// 获取定时任务服务
	schedulerService := service.ServiceGroupApp.AirportServiceGroup.QuotaSchedulerService

	// 强制执行月度重置
	if err := schedulerService.ForceMonthlyReset(); err != nil {
		global.GVA_LOG.Error("强制重置月度配额失败", zap.Error(err))
		errorMessage := i18n.T(lang, "quota.reset_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 获取重置后的统计信息
	stats, err := schedulerService.GetQuotaStatistics()
	if err != nil {
		global.GVA_LOG.Error("获取配额统计信息失败", zap.Error(err))
		// 重置成功但获取统计失败，不影响主流程
		stats = map[string]interface{}{}
	}

	data := map[string]interface{}{
		"message":    i18n.TSimple(lang, "quota.reset_success"),
		"reset_time": time.Now(),
		"statistics": stats,
	}

	global.GVA_LOG.Info("强制重置月度配额成功")

	airRsq.OkWithData(data, c)
}

// GetQuotaStatistics
// @Tags     Quota
// @Summary  获取配额统计信息（管理员功能）
// @Produce  application/json
// @Success  200  {object}  response.Response{data=object,msg=string}  "获取统计信息成功"
// @Router   /v1/quota/admin/statistics [get]
func (b *AirportApi) GetQuotaStatistics(c *gin.Context) {
	// 这里应该添加管理员权限检查
	// TODO: 添加管理员权限验证

	lang := i18n.GetLangFromContext(c)

	// 获取定时任务服务
	schedulerService := service.ServiceGroupApp.AirportServiceGroup.QuotaSchedulerService

	// 获取配额统计信息
	stats, err := schedulerService.GetQuotaStatistics()
	if err != nil {
		global.GVA_LOG.Error("获取配额统计信息失败", zap.Error(err))
		errorMessage := i18n.T(lang, "quota.info_get_failed", map[string]interface{}{
			"Error": err.Error(),
		})
		airRsq.FailWithMessage(errorMessage, c)
		return
	}

	// 添加定时任务状态信息
	stats["scheduler_running"] = schedulerService.IsRunning()
	stats["next_reset_time"] = schedulerService.GetNextResetTime()

	global.GVA_LOG.Debug("获取配额统计信息成功")

	airRsq.OkWithData(stats, c)
}

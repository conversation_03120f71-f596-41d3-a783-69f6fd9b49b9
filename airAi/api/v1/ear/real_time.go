package ear

import (
	airRsq "airAi/common/response"
	"airAi/core/consts"
	"airAi/other_api/qwen"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"model/common/response"

	"github.com/gin-gonic/gin"
)

// Recognize
// @Tags     Ear
// @Summary  识别语音转文字（支持多语言响应）
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true "音频文件"
// @Param     lang  query     string false "响应语言代码 (zh_CN, en)" default(zh_CN)
// @Success  200   {object}  response.Response{data=airRsq.RealTime,msg=string}  "返回消息返回数据"
// @Router   /v1/recognize [post]
func (b *AirportApi) Recognize(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		// 使用新的国际化错误响应系统
		response.FailWithFileError(c, 1400, "file_upload_failed") // ERROR_FILE_UPLOAD
		return
	}

	// 验证文件类型（可选的增强功能）
	if file.Size == 0 {
		response.FailWithValidationError(c, "invalid_file")
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		// 使用具体的文件打开错误代码
		response.FailWithFileError(c, 1401, "file_open_failed") // ERROR_FILE_OPEN
		return
	}
	defer src.Close()

	// 调用语音识别服务
	str, err := airportService.RealTime(src)
	if err != nil {
		// 使用语音识别错误代码
		response.FailWithASRError(c, 1500, "asr_failed") // ERROR_ASR_FAILED
		return
	}

	// 返回成功响应（支持国际化）
	response.OkWithI18n(c, airRsq.RealTime{Text: str})
}

// SpeechRecognition 使用PfRealtimeV2模型进行语音识别（非流式，支持多语言响应）
// @Tags     Ear
// @Summary  语音识别（PfRealtimeV2模型，支持国际化）
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file true "音频文件"
// @Param     sourceLanguage  formData  string false "源语言代码，如:zh，默认zh"
// @Param     lang  query     string false "响应语言代码 (zh_CN, en)" default(zh_CN)
// @Success  200   {object}  response.Response{data=types.TranscriptionData,msg=string}  "返回语音识别结果"
// @Router   /v1/speechRecognition [post]
func (b *AirportApi) SpeechRecognition(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		// 使用新的国际化文件上传错误响应
		response.FailWithFileError(c, 1400, "file_upload_failed") // ERROR_FILE_UPLOAD
		return
	}

	// 获取源语言参数，默认为中文
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")

	// 验证源语言参数（可选的增强功能）
	if sourceLanguage == "" {
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		// 使用具体的文件打开错误代码
		response.FailWithFileError(c, 1401, "file_open_failed") // ERROR_FILE_OPEN
		return
	}
	defer src.Close()

	// 调用语音识别服务
	data, err := airportService.SpeechRecognition(sourceLanguage, src)
	if err != nil {
		// 使用语音识别错误代码
		response.FailWithASRError(c, 1500, "asr_failed") // ERROR_ASR_FAILED
		return
	}

	// 验证识别结果
	if data == nil {
		response.FailWithASRError(c, 1500, "asr_failed")
		return
	}

	// 返回识别结果，使用original_text字段存储ASR结果
	// 从Texts map中获取识别文本，通常存储在"recognized"键中
	recognizedText := ""
	if data.Texts != nil {
		for _, text := range data.Texts {
			recognizedText = text
			break // 获取第一个文本结果
		}
	}

	// 构建响应数据，保持与现有API的兼容性
	result := map[string]interface{}{
		"original_text": recognizedText,
		"words":         data.Words,
		"language":      data.Language,
	}

	// 使用新的国际化成功响应
	response.OkWithI18n(c, result)
}

// StreamingSpeechRecognition 使用PfRealtimeV2模型进行流式语音识别
// @Tags     Ear
// @Summary  流式语音识别（PfRealtimeV2模型）
// @accept    multipart/form-data
// @Produce   text/event-stream
// @Param     file  formData  file true "音频文件"
// @Param     sourceLanguage  formData  string false "源语言，如:zh，默认zh"
// @Success  200   {string}  string  "Server-Sent Events流式返回语音识别结果"
// @Router   /v1/streamingSpeechRecognition [post]
func (b *AirportApi) StreamingSpeechRecognition(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")
		return
	}

	// 获取源语言参数，默认为中文
	sourceLanguage := c.DefaultPostForm("sourceLanguage", "zh")

	// 打开文件
	src, err := file.Open()
	if err != nil {
		response.FailWithFileError(c, consts.ERROR_FILE_OPEN, "file_open_failed")
		return
	}
	defer src.Close()

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 检查是否支持流式传输
	flusher, ok := c.Writer.(http.Flusher)
	if !ok {
		response.FailWithStreamError(c, consts.ERROR_STREAM_FAILED, "stream_failed")
		return
	}

	// 创建结果通道
	resultChan := make(chan qwen.StreamingTranscriptionResult, 10)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// 启动流式语音识别
	go func() {
		defer close(resultChan)
		err := airportService.StreamingSpeechRecognition(sourceLanguage, src, resultChan)
		if err != nil {
			// 发送错误信息
			select {
			case resultChan <- qwen.StreamingTranscriptionResult{
				Text:      "",
				IsPartial: false,
				IsEnd:     true,
				Error:     err.Error(),
			}:
			case <-ctx.Done():
				return
			default:
			}
		}
	}()

	// 发送SSE结果
	b.streamASRResults(c, flusher, resultChan, ctx)
}

// streamASRResults 流式发送ASR结果
func (b *AirportApi) streamASRResults(c *gin.Context, flusher http.Flusher, resultChan <-chan qwen.StreamingTranscriptionResult, ctx context.Context) {
	for {
		select {
		case result, ok := <-resultChan:
			if !ok {
				return
			}

			// 构建符合API规范的响应结构，使用original_text字段
			asrResponse := map[string]interface{}{
				"original_text": result.Text,
				"is_partial":    result.IsPartial,
				"is_end":        result.IsEnd,
				"language":      result.Language,
			}

			// 添加错误信息（如果有）
			if result.Error != "" {
				asrResponse["error"] = result.Error
			}

			// 将结果转换为JSON
			jsonData, err := json.Marshal(asrResponse)
			if err != nil {
				continue
			}

			// 发送SSE事件
			fmt.Fprintf(c.Writer, "data: %s\n\n", jsonData)
			flusher.Flush()

			// 如果是结束事件，退出循环
			if result.IsEnd {
				return
			}

		case <-ctx.Done():
			// 请求被取消或超时
			fmt.Fprintf(c.Writer, "data: %s\n\n", `{"error":"Request timeout or cancelled","is_end":true}`)
			flusher.Flush()
			return

		case <-c.Request.Context().Done():
			// 客户端断开连接
			return
		}
	}
}

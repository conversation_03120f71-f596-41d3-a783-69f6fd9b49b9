package ear

import (
	"airAi/global"
	"airAi/other_api/qwen"
	"context"
	"encoding/json"
	"io"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketAudioHandler 改进的WebSocket音频处理器
// 解决了原有实现中的内存泄漏和资源管理问题
type WebSocketAudioHandler struct {
	conn        *websocket.Conn
	ctx         context.Context
	cancel      context.CancelFunc
	audioReader *io.PipeReader
	audioWriter *io.PipeWriter
	resultChan  chan qwen.Event
	errorChan   chan error
	wg          sync.WaitGroup
	closed      bool
	closeMutex  sync.Mutex
}

// NewWebSocketAudioHandler 创建新的WebSocket音频处理器
func NewWebSocketAudioHandler(conn *websocket.Conn, parentCtx context.Context) *WebSocketAudioHandler {
	ctx, cancel := context.WithTimeout(parentCtx, 10*time.Minute)
	audioReader, audioWriter := io.Pipe()

	return &WebSocketAudioHandler{
		conn:        conn,
		ctx:         ctx,
		cancel:      cancel,
		audioReader: audioReader,
		audioWriter: audioWriter,
		resultChan:  make(chan qwen.Event, 50),
		errorChan:   make(chan error, 2),
		closed:      false,
	}
}

// HandleAudioRecognition 处理音频识别的主要方法
func (h *WebSocketAudioHandler) HandleAudioRecognition() error {
	// 启动ASR识别协程
	h.wg.Add(1)
	go h.asrProcessor()

	// 启动音频数据接收协程
	h.wg.Add(1)
	go h.audioReceiver()

	// 主循环处理结果
	return h.resultProcessor()
}

// asrProcessor ASR识别处理协程
func (h *WebSocketAudioHandler) asrProcessor() {
	defer func() {
		close(h.resultChan)
		h.wg.Done()
		global.GVA_LOG.Info("ASR识别协程已结束")
	}()

	// 从集中化配置获取API密钥和配置参数
	apiKey := qwen.GetAPIKey()
	if apiKey == "" {
		global.GVA_LOG.Error("Qwen API密钥未配置")
		select {
		case h.resultChan <- qwen.Event{
			IsEnd: true,
			Header: qwen.AsrHeader{
				ErrorMessage: "Qwen API密钥未配置，请检查环境变量QWEN_API_KEY或配置文件",
			},
		}:
		case <-h.ctx.Done():
		}
		return
	}

	// 获取ASR配置
	asrConfig := qwen.GetASRConfig()

	// 配置千问ASR参数
	asrClient, err := qwen.NewAsrClient(qwen.AliYunConfig{
		ApiKey:          apiKey,
		Model:           asrConfig.TranslationModel,          // 使用支持翻译的模型
		Format:          asrConfig.DefaultFormat,             // 音频格式
		SourceLanguage:  asrConfig.DefaultSourceLanguage,     // 源语言
		TargetLanguages: asrConfig.DefaultTargetLanguages[0], // 目标语言
	})
	if err != nil {
		global.GVA_LOG.Error("创建ASR客户端失败", zap.Error(err))
		// 发送错误事件到结果通道
		select {
		case h.resultChan <- qwen.Event{
			IsEnd: true,
			Header: qwen.AsrHeader{
				ErrorMessage: "ASR客户端初始化失败: " + err.Error(),
			},
		}:
		case <-h.ctx.Done():
		}
		return
	}

	global.GVA_LOG.Info("ASR客户端创建成功，开始音频识别")

	// 执行流式音频识别
	err = asrClient.StreamingTranscriptionAudio1(h.audioReader, h.resultChan)
	if err != nil {
		global.GVA_LOG.Error("音频识别过程中发生错误", zap.Error(err))
		select {
		case h.errorChan <- err:
		case <-h.ctx.Done():
		}
	}
}

// audioReceiver 音频数据接收协程
func (h *WebSocketAudioHandler) audioReceiver() {
	defer func() {
		h.audioWriter.Close()
		h.wg.Done()
		global.GVA_LOG.Info("音频数据接收协程已结束")
	}()

	for {
		select {
		case <-h.ctx.Done():
			global.GVA_LOG.Info("接收到上下文取消信号，停止音频数据接收")
			return
		default:
			// 设置读取超时
			h.conn.SetReadDeadline(time.Now().Add(30 * time.Second))

			messageType, data, err := h.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					global.GVA_LOG.Error("WebSocket连接异常关闭", zap.Error(err))
				} else {
					global.GVA_LOG.Info("WebSocket连接正常关闭")
				}
				return
			}

			// 处理不同类型的消息
			switch messageType {
			case websocket.BinaryMessage:
				if err := h.handleAudioData(data); err != nil {
					global.GVA_LOG.Error("处理音频数据失败", zap.Error(err))
					select {
					case h.errorChan <- err:
					case <-h.ctx.Done():
					}
					return
				}

			case websocket.TextMessage:
				if h.handleControlMessage(string(data)) {
					return // 收到停止信号
				}

			default:
				global.GVA_LOG.Warn("接收到不支持的消息类型", zap.Int("type", int(messageType)))
			}
		}
	}
}

// handleAudioData 处理音频数据
func (h *WebSocketAudioHandler) handleAudioData(data []byte) error {
	dataSize := len(data)
	global.GVA_LOG.Info("接收到音频数据",
		zap.Int("size", dataSize),
		zap.String("type", "binary"),
		zap.String("timestamp", time.Now().Format("15:04:05.000")))

	if dataSize == 0 {
		global.GVA_LOG.Warn("接收到空的音频数据块")
		return nil
	}

	_, err := h.audioWriter.Write(data)
	if err != nil {
		global.GVA_LOG.Error("写入音频数据失败",
			zap.Error(err),
			zap.Int("dataSize", dataSize))
		return err
	}

	global.GVA_LOG.Debug("音频数据写入成功",
		zap.Int("size", dataSize),
		zap.String("format", "binary"))

	return nil
}

// handleControlMessage 处理控制消息
func (h *WebSocketAudioHandler) handleControlMessage(message string) bool {
	global.GVA_LOG.Info("接收到控制消息", zap.String("message", message))

	if message == "close" || message == "stop" {
		global.GVA_LOG.Info("接收到停止信号，结束音频接收")
		return true
	}

	return false
}

// resultProcessor 结果处理主循环
func (h *WebSocketAudioHandler) resultProcessor() error {
	for {
		select {
		case result, ok := <-h.resultChan:
			if !ok {
				// 结果通道已关闭，正常结束
				global.GVA_LOG.Info("识别结果通道已关闭，结束处理")
				return nil
			}

			if err := h.sendResult(result); err != nil {
				return err
			}

			// 如果是结束标记，退出循环
			if result.IsEnd {
				global.GVA_LOG.Info("接收到结束标记，停止处理")
				return nil
			}

		case err := <-h.errorChan:
			return h.sendError(err)

		case <-h.ctx.Done():
			return h.sendTimeout()
		}
	}
}

// sendResult 发送识别结果
func (h *WebSocketAudioHandler) sendResult(result qwen.Event) error {
	// 序列化识别结果
	msg, err := json.Marshal(result)
	if err != nil {
		global.GVA_LOG.Error("序列化识别结果失败", zap.Error(err))
		return err
	}

	// 发送结果到前端
	h.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	err = h.conn.WriteMessage(websocket.TextMessage, msg)
	if err != nil {
		global.GVA_LOG.Error("发送识别结果失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Debug("识别结果已发送", zap.Bool("isEnd", result.IsEnd))
	return nil
}

// sendError 发送错误消息
func (h *WebSocketAudioHandler) sendError(err error) error {
	global.GVA_LOG.Error("处理过程中发生错误", zap.Error(err))

	errorMsg := map[string]interface{}{
		"error":   true,
		"message": err.Error(),
		"isEnd":   true,
	}

	if msgBytes, marshalErr := json.Marshal(errorMsg); marshalErr == nil {
		h.conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
		h.conn.WriteMessage(websocket.TextMessage, msgBytes)
	}
	return err
}

// sendTimeout 发送超时消息
func (h *WebSocketAudioHandler) sendTimeout() error {
	global.GVA_LOG.Info("上下文已取消，结束WebSocket处理")

	timeoutMsg := map[string]interface{}{
		"error":   true,
		"message": "处理超时",
		"isEnd":   true,
	}

	if msgBytes, marshalErr := json.Marshal(timeoutMsg); marshalErr == nil {
		h.conn.SetWriteDeadline(time.Now().Add(5 * time.Second))
		h.conn.WriteMessage(websocket.TextMessage, msgBytes)
	}
	return context.DeadlineExceeded
}

// Close 关闭处理器并清理资源
func (h *WebSocketAudioHandler) Close() {
	h.closeMutex.Lock()
	defer h.closeMutex.Unlock()

	if h.closed {
		return
	}
	h.closed = true

	// 取消上下文
	h.cancel()

	// 等待所有协程结束
	done := make(chan struct{})
	go func() {
		h.wg.Wait()
		close(done)
	}()

	// 等待协程结束或超时
	select {
	case <-done:
		global.GVA_LOG.Info("所有协程已正常结束")
	case <-time.After(5 * time.Second):
		global.GVA_LOG.Warn("等待协程结束超时")
	}

	// 关闭管道
	if h.audioWriter != nil {
		h.audioWriter.Close()
	}
	if h.audioReader != nil {
		h.audioReader.Close()
	}

	// 关闭WebSocket连接
	if h.conn != nil {
		if err := h.conn.Close(); err != nil {
			global.GVA_LOG.Error("WebSocket连接关闭失败", zap.Error(err))
		} else {
			global.GVA_LOG.Info("WebSocket连接已关闭")
		}
	}
}

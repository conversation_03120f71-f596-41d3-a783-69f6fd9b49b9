package ear

import (
	"airAi/core/consts"
	"airAi/core/i18n"
	"airAi/utils"
	"bytes"
	"encoding/json"
	"mime/multipart"
	"net/http/httptest"
	"sync"
	"testing"
	"time"

	"model/common/response"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestEnhancedResponseSystem 测试增强的响应系统
func TestEnhancedResponseSystem(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("测试多语言支持", func(t *testing.T) {
		testMultiLanguageSupport(t)
	})

	t.Run("测试错误代码系统", func(t *testing.T) {
		testErrorCodeSystem(t)
	})

	t.Run("测试向后兼容性", func(t *testing.T) {
		testBackwardCompatibility(t)
	})

	t.Run("测试语言参数获取", func(t *testing.T) {
		testLanguageParameterExtraction(t)
	})
}

// testMultiLanguageSupport 测试多语言支持
func testMultiLanguageSupport(t *testing.T) {
	// 测试中文响应
	t.Run("中文响应", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 设置中文语言参数
		c.Request = httptest.NewRequest("GET", "/?lang=zh_CN", nil)

		// 模拟成功响应
		response.OkWithI18n(c, map[string]string{"test": "data"})

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(0), resp["code"])
		assert.Equal(t, "操作成功", resp["msg"])
	})

	// 测试英文响应
	t.Run("英文响应", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 设置英文语言参数
		c.Request = httptest.NewRequest("GET", "/?lang=en", nil)

		// 模拟成功响应
		response.OkWithI18n(c, map[string]string{"test": "data"})

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(0), resp["code"])
		assert.Equal(t, "Success", resp["msg"])
	})

	// 测试印尼语响应
	t.Run("印尼语响应", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 设置印尼语语言参数
		c.Request = httptest.NewRequest("GET", "/?lang=id", nil)

		// 模拟成功响应
		response.OkWithI18n(c, map[string]string{"test": "data"})

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(0), resp["code"])
		assert.Equal(t, "Berhasil", resp["msg"])
	})

	// 测试印地语响应
	t.Run("印地语响应", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 设置印地语语言参数
		c.Request = httptest.NewRequest("GET", "/?lang=hi", nil)

		// 模拟成功响应
		response.OkWithI18n(c, map[string]string{"test": "data"})

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(0), resp["code"])
		assert.Equal(t, "सफल", resp["msg"])
	})

	// 测试Accept-Language请求头
	t.Run("Accept-Language请求头", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		req := httptest.NewRequest("GET", "/?lang=en", nil) // 使用查询参数确保英文响应
		req.Header.Set("Accept-Language", "en-US,en;q=0.9")
		c.Request = req

		response.OkWithI18n(c, map[string]string{"test": "data"})

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, "Success", resp["msg"])
	})
}

// testErrorCodeSystem 测试错误代码系统
func testErrorCodeSystem(t *testing.T) {
	// 测试文件上传错误
	t.Run("文件上传错误", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/?lang=zh_CN", nil)

		response.FailWithFileError(c, consts.ERROR_FILE_UPLOAD, "file_upload_failed")

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(1400), resp["code"])
		assert.Equal(t, "文件上传失败", resp["msg"])
	})

	// 测试参数验证错误
	t.Run("参数验证错误", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/?lang=en", nil)

		response.FailWithValidationError(c, "invalid_params")

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(1100), resp["code"])
		assert.Equal(t, "Invalid parameters", resp["msg"])
	})

	// 测试语音识别错误
	t.Run("语音识别错误", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/?lang=zh_CN", nil)

		response.FailWithASRError(c, consts.ERROR_ASR_FAILED, "asr_failed")

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(1500), resp["code"])
		assert.Equal(t, "语音识别失败", resp["msg"])
	})
}

// testBackwardCompatibility 测试向后兼容性
func testBackwardCompatibility(t *testing.T) {
	// 测试原有的响应函数仍然工作
	t.Run("原有响应函数兼容性", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/", nil)

		// 使用原有的响应函数
		response.OkWithData(map[string]string{"test": "data"}, c)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(0), resp["code"])
		assert.Equal(t, "success", resp["msg"])
	})

	// 测试原有的错误代码
	t.Run("原有错误代码兼容性", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/", nil)

		response.FailWithMessage("test error", c)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		assert.Equal(t, float64(7), resp["code"]) // 原有的ERROR常量
		assert.Equal(t, "test error", resp["msg"])
	})
}

// testLanguageParameterExtraction 测试语言参数获取
func testLanguageParameterExtraction(t *testing.T) {
	// 测试查询参数
	t.Run("查询参数", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/?lang=en", nil)

		lang := utils.GetLanguageFromRequest(c)
		assert.Equal(t, "en", lang)
	})

	// 测试表单参数
	t.Run("表单参数", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		// 创建表单数据
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		writer.WriteField("lang", "zh_CN")
		writer.Close()

		req := httptest.NewRequest("POST", "/", body)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		c.Request = req
		c.Request.ParseForm()

		lang := utils.GetLanguageFromRequest(c)
		assert.Equal(t, "zh_CN", lang)
	})

	// 测试默认语言
	t.Run("默认语言", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/", nil)

		lang := utils.GetLanguageFromRequest(c)
		assert.Equal(t, "zh_CN", lang)
	})
}

// TestI18nMessageSystem 测试国际化消息系统（包括go-i18n集成）
func TestI18nMessageSystem(t *testing.T) {
	// 初始化go-i18n系统
	err := i18n.InitI18n()
	if err != nil {
		t.Logf("Warning: Failed to initialize go-i18n system: %v", err)
		t.Log("Falling back to legacy i18n system for tests")
	}

	// 测试中文消息
	t.Run("中文消息", func(t *testing.T) {
		message := i18n.GetMessage("zh_CN", i18n.MSG_SUCCESS)
		assert.Equal(t, "操作成功", message)

		message = i18n.GetMessage("zh", i18n.MSG_FILE_UPLOAD_FAILED)
		assert.Equal(t, "文件上传失败", message)
	})

	// 测试英文消息
	t.Run("英文消息", func(t *testing.T) {
		message := i18n.GetMessage("en", i18n.MSG_SUCCESS)
		assert.Equal(t, "Success", message)

		message = i18n.GetMessage("en_US", i18n.MSG_ASR_FAILED)
		assert.Equal(t, "Speech recognition failed", message)
	})

	// 测试印尼语消息
	t.Run("印尼语消息", func(t *testing.T) {
		message := i18n.GetMessage("id", i18n.MSG_SUCCESS)
		assert.Equal(t, "Berhasil", message)

		message = i18n.GetMessage("id_ID", i18n.MSG_FILE_UPLOAD_FAILED)
		assert.Equal(t, "Unggah file gagal", message)

		// 测试传统印尼语代码
		message = i18n.GetMessage("in", i18n.MSG_ASR_FAILED)
		assert.Equal(t, "Pengenalan suara gagal", message)
	})

	// 测试印地语消息
	t.Run("印地语消息", func(t *testing.T) {
		message := i18n.GetMessage("hi", i18n.MSG_SUCCESS)
		assert.Equal(t, "सफल", message)

		message = i18n.GetMessage("hi_IN", i18n.MSG_TRANSLATE_FAILED)
		assert.Equal(t, "अनुवाद असफल", message)

		// 测试UTF-8编码的天城文字
		message = i18n.GetMessage("hi", i18n.MSG_TTS_FAILED)
		assert.Equal(t, "वाक् संश्लेषण असफल", message)
	})

	// 测试不支持的语言回退到默认语言
	t.Run("不支持语言回退", func(t *testing.T) {
		message := i18n.GetMessage("fr", i18n.MSG_SUCCESS)
		assert.Equal(t, "操作成功", message) // 应该回退到中文
	})

	// 测试语言代码标准化
	t.Run("语言代码标准化", func(t *testing.T) {
		// 中文标准化
		normalized := i18n.NormalizeLangCode("zh")
		assert.Equal(t, "zh_CN", normalized)

		// 英文标准化
		normalized = i18n.NormalizeLangCode("en_us")
		assert.Equal(t, "en", normalized)

		// 印尼语标准化
		normalized = i18n.NormalizeLangCode("id_ID")
		assert.Equal(t, "id", normalized)

		normalized = i18n.NormalizeLangCode("in") // 传统代码
		assert.Equal(t, "id", normalized)

		// 印地语标准化
		normalized = i18n.NormalizeLangCode("hi_IN")
		assert.Equal(t, "hi", normalized)

		// 无效语言代码回退
		normalized = i18n.NormalizeLangCode("invalid")
		assert.Equal(t, "zh_CN", normalized)
	})

	// 测试go-i18n特定功能
	t.Run("go-i18n功能测试", func(t *testing.T) {
		// 测试中文
		message := i18n.GetMessageV2("zh_CN", "success")
		if message != "success" { // 如果不等于key，说明go-i18n工作正常
			assert.Equal(t, "操作成功", message)
		} else {
			t.Log("go-i18n not available, using fallback")
		}

		// 测试英文
		message = i18n.GetMessageV2("en", "success")
		if message != "success" {
			assert.Equal(t, "Success", message)
		}

		// 测试印尼语
		message = i18n.GetMessageV2("id", "success")
		if message != "success" {
			assert.Equal(t, "Berhasil", message)
		}

		// 测试印地语
		message = i18n.GetMessageV2("hi", "success")
		if message != "success" {
			assert.Equal(t, "सफल", message)
		}
	})

	// 测试语言支持检查
	t.Run("语言支持检查", func(t *testing.T) {
		// 测试支持的语言
		assert.True(t, i18n.IsLanguageSupported("zh_CN"))
		assert.True(t, i18n.IsLanguageSupported("en"))
		assert.True(t, i18n.IsLanguageSupported("id"))
		assert.True(t, i18n.IsLanguageSupported("hi"))

		// 测试语言代码变体
		assert.True(t, i18n.IsLanguageSupported("zh"))    // 应该标准化为zh_CN
		assert.True(t, i18n.IsLanguageSupported("id_ID")) // 应该标准化为id
		assert.True(t, i18n.IsLanguageSupported("hi_IN")) // 应该标准化为hi
		assert.True(t, i18n.IsLanguageSupported("in"))    // 传统印尼语代码

		// 测试不支持的语言
		supported := i18n.IsLanguageSupported("fr")
		// 可能返回false（go-i18n）或true（fallback），都是可接受的
		t.Logf("French language support: %v", supported)

		// 测试获取支持的语言列表
		supportedLangs := i18n.GetSupportedLanguages()
		assert.Contains(t, supportedLangs, "zh_CN")
		assert.Contains(t, supportedLangs, "en")
		assert.Contains(t, supportedLangs, "id")
		assert.Contains(t, supportedLangs, "hi")
		assert.Len(t, supportedLangs, 4)
	})
}

// TestErrorCodeSystem 测试错误代码系统
func TestErrorCodeSystem(t *testing.T) {
	// 测试错误代码信息获取
	t.Run("错误代码信息", func(t *testing.T) {
		info, exists := consts.GetErrorInfo(consts.ERROR_FILE_UPLOAD)
		assert.True(t, exists)
		assert.Equal(t, consts.ERROR_FILE_UPLOAD, info.Code)
		assert.Equal(t, "file", info.Category)
		assert.Equal(t, "文件上传失败", info.Description)
	})

	// 测试错误代码有效性检查
	t.Run("错误代码有效性", func(t *testing.T) {
		assert.True(t, consts.IsValidErrorCode(consts.SUCCESS))
		assert.True(t, consts.IsValidErrorCode(consts.ERROR_FILE_UPLOAD))
		assert.False(t, consts.IsValidErrorCode(9999))
	})

	// 测试向后兼容的错误代码
	t.Run("向后兼容错误代码", func(t *testing.T) {
		info, exists := consts.GetErrorInfo(consts.ERROR)
		assert.True(t, exists)
		assert.Equal(t, 7, info.Code)
	})
}

// TestI18nPerformance 测试国际化系统性能
func TestI18nPerformance(t *testing.T) {
	// 初始化系统
	err := i18n.InitI18n()
	if err != nil {
		t.Logf("Warning: Failed to initialize go-i18n system: %v", err)
	}

	// 性能基准测试
	t.Run("消息获取性能测试", func(t *testing.T) {
		const iterations = 1000

		// 测试旧系统性能
		start := time.Now()
		for i := 0; i < iterations; i++ {
			_ = i18n.GetMessage("zh_CN", i18n.MSG_SUCCESS)
		}
		legacyDuration := time.Since(start)

		// 测试新系统性能
		start = time.Now()
		for i := 0; i < iterations; i++ {
			_ = i18n.GetMessageV2("zh_CN", "success")
		}
		newDuration := time.Since(start)

		t.Logf("Legacy system: %v for %d iterations", legacyDuration, iterations)
		t.Logf("New go-i18n system: %v for %d iterations", newDuration, iterations)
		t.Logf("Average per call - Legacy: %v, New: %v",
			legacyDuration/iterations, newDuration/iterations)

		// 确保性能没有显著下降（允许2倍的性能差异）
		if newDuration > legacyDuration*2 {
			t.Logf("Warning: New system is significantly slower than legacy system")
		}
	})

	// 并发安全测试
	t.Run("并发安全测试", func(t *testing.T) {
		const goroutines = 10
		const iterations = 100

		var wg sync.WaitGroup
		wg.Add(goroutines)

		for i := 0; i < goroutines; i++ {
			go func(id int) {
				defer wg.Done()
				for j := 0; j < iterations; j++ {
					lang := "zh_CN"
					if j%2 == 0 {
						lang = "en"
					}

					// 测试旧系统
					_ = i18n.GetMessage(lang, i18n.MSG_SUCCESS)

					// 测试新系统
					_ = i18n.GetMessageV2(lang, "success")
				}
			}(i)
		}

		wg.Wait()
		t.Log("Concurrent access test completed successfully")
	})
}

package ear

import (
	"airAi/core/consts"
	"bytes"
	"encoding/json"
	"mime/multipart"
	"model/common/response"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestAPIEndpointsI18nIntegration 测试API端点的国际化集成
func TestAPIEndpointsI18nIntegration(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 测试文件上传错误的多语言响应
	t.Run("文件上传错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "文件上传失败"},
			{"en", "File upload failed"},
			{"id", "Unggah file gagal"},
			{"hi", "फ़ाइल अपलोड असफल"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 模拟没有文件的请求
				c.Request = httptest.NewRequest("POST", "/v1/recognize?lang="+tc.lang, nil)
				c.Request.Header.Set("Content-Type", "multipart/form-data")

				api := &AirportApi{}
				api.Recognize(c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1400), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试参数验证错误的多语言响应
	t.Run("参数验证错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "缺少必需参数"},
			{"en", "Missing required parameters"},
			{"id", "Parameter yang diperlukan hilang"},
			{"hi", "आवश्यक पैरामीटर गुम"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 创建空的表单数据
				body := &bytes.Buffer{}
				writer := multipart.NewWriter(body)
				writer.WriteField("text", "") // 空文本
				writer.Close()

				c.Request = httptest.NewRequest("POST", "/v1/textMeeting?lang="+tc.lang, body)
				c.Request.Header.Set("Content-Type", writer.FormDataContentType())

				api := &AirportApi{}
				api.TextMeeting(c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1100), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试Accept-Language请求头
	t.Run("Accept-Language请求头支持", func(t *testing.T) {
		testCases := []struct {
			acceptLang string
			expected   string
		}{
			{"zh-CN", "文件上传失败"},
			{"en", "File upload failed"},
			{"id", "Unggah file gagal"},
			{"hi", "फ़ाइल अपलोड असफल"},
		}

		for _, tc := range testCases {
			t.Run("Accept-Language_"+tc.acceptLang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = httptest.NewRequest("POST", "/v1/recognize", nil)
				c.Request.Header.Set("Content-Type", "multipart/form-data")
				c.Request.Header.Set("Accept-Language", tc.acceptLang)

				api := &AirportApi{}
				api.Recognize(c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1400), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试表单参数语言支持
	t.Run("表单参数语言支持", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "缺少必需参数"},
			{"en", "Missing required parameters"},
			{"id", "Parameter yang diperlukan hilang"},
			{"hi", "आवश्यक पैरामीटर गुम"},
		}

		for _, tc := range testCases {
			t.Run("表单参数_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				// 创建表单数据，包含语言参数
				body := &bytes.Buffer{}
				writer := multipart.NewWriter(body)
				writer.WriteField("lang", tc.lang)
				writer.WriteField("text", "") // 空文本触发验证错误
				writer.Close()

				c.Request = httptest.NewRequest("POST", "/v1/translate", body)
				c.Request.Header.Set("Content-Type", writer.FormDataContentType())

				api := &AirportApi{}
				api.Translate(c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1100), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})
}

// TestAPIErrorCodeConsistency 测试API错误代码一致性
func TestAPIErrorCodeConsistency(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("文件错误代码一致性", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Request = httptest.NewRequest("POST", "/v1/recognize", nil)
		c.Request.Header.Set("Content-Type", "multipart/form-data")

		api := &AirportApi{}
		api.Recognize(c)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		// 验证文件上传错误使用正确的错误代码
		assert.Equal(t, float64(1400), resp["code"])
	})

	t.Run("参数验证错误代码一致性", func(t *testing.T) {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		writer.WriteField("text", "")
		writer.Close()

		c.Request = httptest.NewRequest("POST", "/v1/translate", body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		api := &AirportApi{}
		api.Translate(c)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		// 验证参数验证错误使用正确的错误代码
		assert.Equal(t, float64(1100), resp["code"])
	})
}

// TestLanguageCodeNormalization 测试语言代码标准化
func TestLanguageCodeNormalization(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testCases := []struct {
		inputLang string
		expected  string
	}{
		{"zh", "文件上传失败"},
		{"zh_cn", "文件上传失败"},
		{"en_US", "File upload failed"},
		{"en_us", "File upload failed"},
		{"id_ID", "Unggah file gagal"},
		{"in", "Unggah file gagal"}, // 传统印尼语代码
		{"hi_IN", "फ़ाइल अपलोड असफल"},
		{"invalid", "文件上传失败"}, // 回退到中文
	}

	for _, tc := range testCases {
		t.Run("标准化_"+tc.inputLang, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Request = httptest.NewRequest("POST", "/v1/recognize?lang="+tc.inputLang, nil)
			c.Request.Header.Set("Content-Type", "multipart/form-data")

			api := &AirportApi{}
			api.Recognize(c)

			var resp map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &resp)
			assert.NoError(t, err)

			assert.Equal(t, tc.expected, resp["msg"])
		})
	}
}

// TestSuccessResponseI18n 测试成功响应的国际化
func TestSuccessResponseI18n(t *testing.T) {
	gin.SetMode(gin.TestMode)

	testCases := []struct {
		lang     string
		expected string
	}{
		{"zh_CN", "操作成功"},
		{"en", "Success"},
		{"id", "Berhasil"},
		{"hi", "सफल"},
	}

	for _, tc := range testCases {
		t.Run("成功响应_"+tc.lang, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// 创建有效的表单数据
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			writer.WriteField("text", "Hello world")
			writer.WriteField("language", "en")
			writer.Close()

			c.Request = httptest.NewRequest("POST", "/v1/textMeeting?lang="+tc.lang, body)
			c.Request.Header.Set("Content-Type", writer.FormDataContentType())

			api := &AirportApi{}
			api.TextMeeting(c)

			var resp map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &resp)
			assert.NoError(t, err)

			// 注意：这个测试可能会因为实际的服务调用而失败
			// 在实际环境中，我们需要模拟服务层
			if resp["code"] == float64(0) {
				assert.Equal(t, tc.expected, resp["msg"])
			}
		})
	}
}

// TestAuthenticationErrorsI18n 测试认证错误的国际化
func TestAuthenticationErrorsI18n(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 测试未授权访问错误的多语言响应
	t.Run("未授权访问错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "未授权访问"},
			{"en", "Unauthorized access"},
			{"id", "Akses tidak sah"},
			{"hi", "अनधिकृत पहुंच"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = httptest.NewRequest("GET", "/?lang="+tc.lang, nil)

				// 直接调用NoAuth函数测试
				response.NoAuth("test message", c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1200), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试Token无效错误的多语言响应
	t.Run("Token无效错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "访问令牌无效"},
			{"en", "Invalid access token"},
			{"id", "Token akses tidak valid"},
			{"hi", "अमान्य पहुंच टोकन"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = httptest.NewRequest("GET", "/?lang="+tc.lang, nil)

				// 测试Token无效错误
				response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "token_invalid")

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1201), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试Token过期错误的多语言响应
	t.Run("Token过期错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "访问令牌已过期"},
			{"en", "Access token expired"},
			{"id", "Token akses kedaluwarsa"},
			{"hi", "पहुंच टोकन समाप्त हो गया"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = httptest.NewRequest("GET", "/?lang="+tc.lang, nil)

				// 测试Token过期错误
				response.FailWithAuthError(c, consts.ERROR_TOKEN_EXPIRED, "token_expired")

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1202), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})

	// 测试余额不足错误的多语言响应
	t.Run("余额不足错误多语言响应", func(t *testing.T) {
		testCases := []struct {
			lang     string
			expected string
		}{
			{"zh_CN", "账户余额不足"},
			{"en", "Insufficient account balance"},
			{"id", "Saldo akun tidak mencukupi"},
			{"hi", "खाता शेष अपर्याप्त"},
		}

		for _, tc := range testCases {
			t.Run("语言_"+tc.lang, func(t *testing.T) {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = httptest.NewRequest("GET", "/?lang="+tc.lang, nil)

				// 直接调用NoInsufficientBalance函数测试
				response.NoInsufficientBalance("test message", c)

				var resp map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &resp)
				assert.NoError(t, err)

				assert.Equal(t, float64(1203), resp["code"])
				assert.Equal(t, tc.expected, resp["msg"])
			})
		}
	})
}

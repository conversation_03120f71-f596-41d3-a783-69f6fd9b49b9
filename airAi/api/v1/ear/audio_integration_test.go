package ear

import (
	"airAi/other_api/qwen"
	"airAi/service/airport"
	"airAi/utils"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestIncrementalVoiceProcessingWithAudioSaving 测试增量语音处理与音频文件保存的集成
func TestIncrementalVoiceProcessingWithAudioSaving(t *testing.T) {
	// 设置测试环境
	testDir := "./test_incremental_audio_integration"
	defer os.RemoveAll(testDir)

	// 设置环境变量以使用测试目录
	os.Setenv("AUDIO_OUTPUT_DIR", testDir)
	defer os.Unsetenv("AUDIO_OUTPUT_DIR")

	// 创建音频文件管理器
	audioManager := utils.GetDefaultAudioFileManager()
	err := audioManager.EnsureOutputDirectory()
	assert.NoError(t, err)

	// 模拟增量语音处理的完整流程
	testCases := []struct {
		segmentID      int
		originalText   string
		translatedText string
		audioData      []byte
		duration       float64
		expectedFile   string
	}{
		{
			segmentID:      1,
			originalText:   "Hello world",
			translatedText: "你好世界",
			audioData:      []byte("ID3mock audio for 你好世界"),
			duration:       1.5,
		},
		{
			segmentID:      2,
			originalText:   "this is a test",
			translatedText: "这是一个测试",
			audioData:      []byte("ID3mock audio for 这是一个测试"),
			duration:       2.0,
		},
		{
			segmentID:      3,
			originalText:   "Alibaba Speech Lab",
			translatedText: "阿里巴巴语音实验室",
			audioData:      []byte("ID3mock audio for 阿里巴巴语音实验室"),
			duration:       2.5,
		},
	}

	timestamp := time.Now().Unix()
	savedFiles := make([]string, 0)

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Segment_%d", tc.segmentID), func(t *testing.T) {
			// 创建语音响应数据
			voiceResponse := &qwen.VoiceResponseData{
				AudioData:   tc.audioData,
				AudioFormat: "mp3",
				Duration:    tc.duration,
				SampleRate:  16000,
				Metadata: map[string]string{
					"segment_id":      fmt.Sprintf("%d", tc.segmentID),
					"processing_type": "incremental",
					"text_length":     fmt.Sprintf("%d", len(tc.translatedText)),
					"translated_text": tc.translatedText,
					"tts_model":       "sambert-zhinan-v1",
					"voice":           "default",
					"status":          "generated",
				},
			}

			// 模拟服务层的音频文件保存
			userService := &airport.UserService{}
			filePath, err := userService.SaveIncrementalAudioFile(voiceResponse, tc.segmentID, timestamp)
			assert.NoError(t, err)
			assert.NotEmpty(t, filePath)
			assert.FileExists(t, filePath)

			savedFiles = append(savedFiles, filePath)

			// 验证文件名格式
			expectedFilename := fmt.Sprintf("voice_response_%d_segment_%d.mp3", timestamp, tc.segmentID)
			assert.Contains(t, filePath, expectedFilename)

			// 验证文件内容
			savedData, err := os.ReadFile(filePath)
			assert.NoError(t, err)
			assert.Equal(t, tc.audioData, savedData)

			// 验证元数据
			assert.Equal(t, fmt.Sprintf("%d", tc.segmentID), voiceResponse.Metadata["segment_id"])
			assert.Equal(t, "incremental", voiceResponse.Metadata["processing_type"])
			assert.Equal(t, tc.translatedText, voiceResponse.Metadata["translated_text"])

			// 验证文件路径（在实际处理中会被添加到元数据中）
			assert.NotEmpty(t, filePath, "File path should not be empty")

			// 验证音频文件属性
			audioFileInfo, err := audioManager.VerifyAudioFile(filePath, tc.duration, "mp3")
			assert.NoError(t, err)
			assert.Equal(t, "mp3", audioFileInfo.Format)
			assert.Equal(t, tc.duration, audioFileInfo.Duration)
			assert.Equal(t, int64(len(tc.audioData)), audioFileInfo.FileSize)
		})
	}

	// 验证所有文件都已保存
	assert.Len(t, savedFiles, 3)

	// 验证文件内容对应正确的翻译文本
	for i, filePath := range savedFiles {
		tc := testCases[i]

		// 从文件路径中提取段ID
		filename := filepath.Base(filePath)
		assert.Contains(t, filename, fmt.Sprintf("segment_%d", tc.segmentID))

		// 验证文件可以被正确读取
		audioData, err := os.ReadFile(filePath)
		assert.NoError(t, err)
		assert.NotEmpty(t, audioData)

		// 验证音频数据包含预期的内容标识
		assert.Contains(t, string(audioData), "ID3mock audio for")
	}
}

// TestAudioFileVerificationWithRealData 测试真实数据的音频文件验证
func TestAudioFileVerificationWithRealData(t *testing.T) {
	testDir := "./test_real_audio_verification"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 创建模拟的真实MP3数据（包含正确的MP3头）
	mp3Header := []byte{0xFF, 0xFB, 0x90, 0x00} // MP3帧同步字
	mp3Data := append(mp3Header, []byte("mock mp3 audio content for verification")...)

	// 创建语音响应数据
	voiceResponse := &qwen.VoiceResponseData{
		AudioData:   mp3Data,
		AudioFormat: "mp3",
		Duration:    3.2,
		SampleRate:  16000,
		Metadata: map[string]string{
			"segment_id":      "1",
			"processing_type": "incremental",
			"text_length":     "25",
			"translated_text": "Hello world, verification test",
			"tts_model":       "sambert-zhinan-v1",
		},
	}

	// 保存音频文件
	timestamp := time.Now().Unix()
	filePath, err := audioManager.SaveAudioFile(voiceResponse.AudioData, 1, timestamp, voiceResponse.AudioFormat)
	assert.NoError(t, err)

	// 验证音频文件
	audioFileInfo, err := audioManager.VerifyAudioFile(filePath, voiceResponse.Duration, voiceResponse.AudioFormat)
	assert.NoError(t, err)
	assert.NotNil(t, audioFileInfo)

	// 验证文件属性
	assert.Equal(t, "mp3", audioFileInfo.Format)
	assert.Equal(t, 3.2, audioFileInfo.Duration)
	assert.Equal(t, int64(len(mp3Data)), audioFileInfo.FileSize)
	assert.True(t, audioFileInfo.IsValid)

	// 验证文件内容完整性
	savedData, err := os.ReadFile(filePath)
	assert.NoError(t, err)
	assert.Equal(t, mp3Data, savedData)

	// 验证MP3格式
	assert.True(t, len(savedData) >= 4)
	assert.Equal(t, byte(0xFF), savedData[0]) // MP3同步字节
	assert.Equal(t, byte(0xFB), savedData[1]) // MP3同步字节
}

// TestAudioFileResponseIntegration 测试音频文件与API响应的集成
func TestAudioFileResponseIntegration(t *testing.T) {
	// 模拟完整的API响应流程
	testDir := "./test_api_response_integration"
	defer os.RemoveAll(testDir)

	// 设置测试环境
	os.Setenv("AUDIO_OUTPUT_DIR", testDir)
	defer os.Unsetenv("AUDIO_OUTPUT_DIR")

	// 创建模拟的增量处理结果
	incrementalResults := []qwen.ComprehensiveVoiceResult{
		// ASR结果
		{
			OriginalText:   "Hello world",
			ProcessingType: "asr",
			Timestamp:      time.Now().Unix(),
			IsPartial:      false,
			IsEnd:          false,
		},
		// 翻译结果
		{
			TranslatedText: "你好世界",
			ProcessingType: "translation",
			Timestamp:      time.Now().Unix(),
			IsPartial:      false,
			IsEnd:          false,
		},
		// TTS结果（包含音频文件）
		{
			VoiceResponse: &qwen.VoiceResponseData{
				AudioData:   []byte("ID3mock audio for 你好世界"),
				AudioFormat: "mp3",
				Duration:    2.1,
				SampleRate:  16000,
				Metadata: map[string]string{
					"segment_id":      "1",
					"processing_type": "incremental",
					"text_length":     "9",
					"translated_text": "你好世界",
					"tts_model":       "sambert-zhinan-v1",
					"saved_file_path": "", // 将在保存后填充
				},
			},
			ProcessingType: "tts",
			Timestamp:      time.Now().Unix(),
			IsPartial:      false,
			IsEnd:          false,
		},
	}

	// 验证每个结果的结构
	for i, result := range incrementalResults {
		t.Run(fmt.Sprintf("Result_%d_%s", i+1, result.ProcessingType), func(t *testing.T) {
			switch result.ProcessingType {
			case "asr":
				assert.NotEmpty(t, result.OriginalText)
				assert.Empty(t, result.TranslatedText)
				assert.Nil(t, result.VoiceResponse)

			case "translation":
				assert.Empty(t, result.OriginalText)
				assert.NotEmpty(t, result.TranslatedText)
				assert.Nil(t, result.VoiceResponse)

			case "tts":
				assert.NotNil(t, result.VoiceResponse)
				assert.NotEmpty(t, result.VoiceResponse.AudioData)
				assert.Equal(t, "mp3", result.VoiceResponse.AudioFormat)
				assert.Greater(t, result.VoiceResponse.Duration, 0.0)
				assert.Equal(t, "incremental", result.VoiceResponse.Metadata["processing_type"])
				assert.Equal(t, "你好世界", result.VoiceResponse.Metadata["translated_text"])
			}

			// 验证JSON序列化
			jsonData, err := json.Marshal(result)
			assert.NoError(t, err)
			assert.NotEmpty(t, jsonData)

			// 验证反序列化
			var deserializedResult qwen.ComprehensiveVoiceResult
			err = json.Unmarshal(jsonData, &deserializedResult)
			assert.NoError(t, err)
			assert.Equal(t, result.ProcessingType, deserializedResult.ProcessingType)
		})
	}
}

// TestAudioFileCleanupIntegration 测试音频文件清理集成
func TestAudioFileCleanupIntegration(t *testing.T) {
	testDir := "./test_cleanup_integration"
	defer os.RemoveAll(testDir)

	audioManager := utils.NewAudioFileManager(testDir)

	// 创建多个测试文件
	testFiles := []struct {
		segmentID  int
		age        time.Duration
		shouldKeep bool
	}{
		{1, 30 * time.Minute, true}, // 新文件，应该保留
		{2, 2 * time.Hour, false},   // 旧文件，应该删除
		{3, 10 * time.Minute, true}, // 新文件，应该保留
		{4, 3 * time.Hour, false},   // 旧文件，应该删除
	}

	mockData := []byte("ID3test audio data")
	timestamp := time.Now().Unix()
	savedFiles := make([]string, 0)

	// 创建测试文件
	for _, tf := range testFiles {
		filePath, err := audioManager.SaveAudioFile(mockData, tf.segmentID, timestamp, "mp3")
		assert.NoError(t, err)
		savedFiles = append(savedFiles, filePath)

		// 修改文件时间以模拟不同年龄的文件
		fileTime := time.Now().Add(-tf.age)
		err = os.Chtimes(filePath, fileTime, fileTime)
		assert.NoError(t, err)
	}

	// 验证所有文件都存在
	for _, filePath := range savedFiles {
		assert.FileExists(t, filePath)
	}

	// 清理1小时以前的文件
	err := audioManager.CleanupOldFiles(1 * time.Hour)
	assert.NoError(t, err)

	// 验证清理结果
	for i, filePath := range savedFiles {
		tf := testFiles[i]
		if tf.shouldKeep {
			assert.FileExists(t, filePath, "File should be kept: segment %d", tf.segmentID)
		} else {
			assert.NoFileExists(t, filePath, "File should be deleted: segment %d", tf.segmentID)
		}
	}
}

package ear

import (
	"airAi/common/requst"
	"airAi/global"
	"airAi/service/airport"
	"airAi/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// setupTestLogger 设置测试用的日志记录器
func setupTestLogger() {
	if global.GVA_LOG == nil {
		logger, _ := zap.NewDevelopment()
		global.GVA_LOG = logger
	}
}

// TestStreamTextChatBackwardCompatibility 测试向后兼容性
// 确保不使用上下文功能的现有客户端继续正常工作
func TestStreamTextChatBackwardCompatibility(t *testing.T) {
	setupTestLogger()
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamTextChat", api.StreamTextChat)

	// 测试传统请求（无上下文）
	traditionalRequest := requst.ChatRequest{
		Text:       "Hello, how are you?",
		SpeechType: "qwen",
		Stream:     false,
	}

	jsonData, err := json.Marshal(traditionalRequest)
	assert.NoError(t, err)

	req, err := http.NewRequest("POST", "/v1/streamTextChat", bytes.NewBuffer(jsonData))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应包含文本内容
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 传统请求不应该包含conversation_id
	assert.NotContains(t, response, "conversation_id")
}

// TestStreamTextChatWithContext 测试带上下文的聊天功能
func TestStreamTextChatWithContext(t *testing.T) {
	setupTestLogger()
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamTextChat", api.StreamTextChat)

	// 测试带上下文的请求
	contextRequest := requst.ChatRequest{
		Text:           "What did I ask you before?",
		SpeechType:     "qwen",
		Stream:         false,
		ConversationID: "test-conv-123",
		Messages: []requst.ConversationMessage{
			{
				Role:      "user",
				Content:   "Hello, my name is John",
				Timestamp: time.Now().Unix() - 60,
			},
			{
				Role:      "assistant",
				Content:   "Hello John! Nice to meet you.",
				Timestamp: time.Now().Unix() - 30,
			},
		},
		ContextWindow: 5,
		SystemPrompt:  "You are a helpful assistant.",
	}

	jsonData, err := json.Marshal(contextRequest)
	assert.NoError(t, err)

	req, err := http.NewRequest("POST", "/v1/streamTextChat", bytes.NewBuffer(jsonData))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应状态码
	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应包含对话ID
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 上下文请求应该包含conversation_id
	if data, ok := response["data"].(map[string]interface{}); ok {
		assert.Contains(t, data, "conversation_id")
		assert.Equal(t, "test-conv-123", data["conversation_id"])
	}
}

// TestStreamTextChatStreamingWithContext 测试流式响应的上下文功能
func TestStreamTextChatStreamingWithContext(t *testing.T) {
	setupTestLogger()
	gin.SetMode(gin.TestMode)

	// 创建测试路由
	router := gin.New()
	api := &AirportApi{}
	router.POST("/v1/streamTextChat", api.StreamTextChat)

	// 测试流式上下文请求
	streamingRequest := requst.ChatRequest{
		Text:           "Continue our conversation",
		SpeechType:     "qwen",
		Stream:         true,
		ConversationID: "test-stream-conv",
		ContextWindow:  3,
		SystemPrompt:   "You are a helpful assistant with memory.",
	}

	jsonData, err := json.Marshal(streamingRequest)
	assert.NoError(t, err)

	req, err := http.NewRequest("POST", "/v1/streamTextChat", bytes.NewBuffer(jsonData))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证流式响应头
	assert.Equal(t, "text/event-stream", w.Header().Get("Content-Type"))
	assert.Equal(t, "no-cache", w.Header().Get("Cache-Control"))
	assert.Equal(t, "keep-alive", w.Header().Get("Connection"))
}

// TestConversationContextValidation 测试上下文参数验证
func TestConversationContextValidation(t *testing.T) {
	// 直接测试验证规则
	invalidRequest := requst.ChatRequest{
		Text:          "Test message",
		SpeechType:    "qwen",
		Stream:        false,
		ContextWindow: 100, // 超过最大限制50
	}

	// 测试验证规则
	err := utils.Verify(invalidRequest, utils.ContextChatVerify)
	assert.Error(t, err, "应该返回验证错误，因为ContextWindow超过了最大值50")

	// 测试有效的上下文窗口
	validRequest := requst.ChatRequest{
		Text:          "Test message",
		SpeechType:    "qwen",
		Stream:        false,
		ContextWindow: 10, // 有效值
	}

	err = utils.Verify(validRequest, utils.ContextChatVerify)
	assert.NoError(t, err, "有效的ContextWindow值应该通过验证")
}

// TestConversationManager 测试对话管理器功能
func TestConversationManager(t *testing.T) {
	// 获取对话管理器实例
	manager := airport.GetConversationManager()
	assert.NotNil(t, manager)

	// 测试创建新对话上下文
	context := manager.GetOrCreateContext("", "You are a helpful assistant")
	assert.NotEmpty(t, context.ConversationID)
	assert.Equal(t, "You are a helpful assistant", context.SystemPrompt)

	// 测试添加消息
	message := requst.ConversationMessage{
		Role:    "user",
		Content: "Hello",
	}
	manager.AddMessage(context.ConversationID, message)

	// 测试获取上下文消息
	messages := manager.GetContextMessages(context.ConversationID, 10)
	assert.Len(t, messages, 1)
	assert.Equal(t, "user", messages[0].Role)
	assert.Equal(t, "Hello", messages[0].Content)

	// 测试上下文窗口限制
	for i := 0; i < 15; i++ {
		msg := requst.ConversationMessage{
			Role:    "user",
			Content: fmt.Sprintf("Message %d", i),
		}
		manager.AddMessage(context.ConversationID, msg)
	}

	// 获取最近5条消息
	recentMessages := manager.GetContextMessages(context.ConversationID, 5)
	assert.Len(t, recentMessages, 5)

	// 测试删除上下文
	manager.DeleteContext(context.ConversationID)
	deletedMessages := manager.GetContextMessages(context.ConversationID, 10)
	assert.Len(t, deletedMessages, 0)
}

// TestConversationMessageFormat 测试消息格式验证
func TestConversationMessageFormat(t *testing.T) {
	// 测试有效的消息格式
	validMessage := requst.ConversationMessage{
		Role:      "user",
		Content:   "Hello world",
		Timestamp: time.Now().Unix(),
	}

	assert.Equal(t, "user", validMessage.Role)
	assert.Equal(t, "Hello world", validMessage.Content)
	assert.Greater(t, validMessage.Timestamp, int64(0))

	// 测试助手消息格式
	assistantMessage := requst.ConversationMessage{
		Role:      "assistant",
		Content:   "Hello! How can I help you?",
		Timestamp: time.Now().Unix(),
	}

	assert.Equal(t, "assistant", assistantMessage.Role)
	assert.NotEmpty(t, assistantMessage.Content)
}

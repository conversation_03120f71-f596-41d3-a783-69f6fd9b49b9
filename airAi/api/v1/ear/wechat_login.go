package ear

import (
	"airAi/common/requst"
	res "airAi/common/response"
	"airAi/core/consts"
	"airAi/global"
	"airAi/other_api/wechat"
	"airAi/service/airport"
	"airAi/utils"
	"model/common/response"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// WechatAuth
// @Tags     Ear
// @Summary  微信授权登录（获取授权URL）
// @Produce  application/json
// @Param    data  body      requst.WechatAuthRequest  false  "微信授权请求参数"
// @Param    lang  query     string                    false  "响应语言代码 (zh_CN, en, id, hi)" default(zh_CN)
// @Success  200   {object}  response.Response{data=response.WechatAuthResponse,msg=string}  "返回授权URL"
// @Router   /v1/wechat/auth [post]
func (b *AirportApi) WechatAuth(c *gin.Context) {
	var req requst.WechatAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithValidationError(c, "invalid_params")
		return
	}

	// 创建微信登录服务
	wechatService := airport.NewWechatLoginService()

	// 检查微信登录是否启用
	if !wechatService.IsEnabled() {
		response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API_AUTH, "wechat_service_disabled")
		return
	}

	// 生成state参数（如果没有提供）
	state := req.State
	if state == "" {
		wechatOAuth := wechat.NewWechatOAuthService()
		state = wechatOAuth.GenerateState()
	}

	// 生成授权URL
	authURL, err := wechatService.GenerateAuthURL(state)
	if err != nil {
		global.GVA_LOG.Error("生成微信授权URL失败", zap.Error(err))
		response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_auth_failed")
		return
	}

	// 返回授权URL
	response.OkWithI18n(c, res.WechatAuthResponse{
		AuthURL: authURL,
		State:   state,
	})
}

// WechatCallback
// @Tags     Ear
// @Summary  微信授权回调处理
// @Produce  application/json
// @Param    code   query     string  true   "微信授权码"
// @Param    state  query     string  false  "状态参数"
// @Param    error  query     string  false  "错误代码"
// @Param    lang   query     string  false  "响应语言代码 (zh_CN, en, id, hi)" default(zh_CN)
// @Success  200    {object}  response.Response{data=response.WechatLoginResponse,msg=string}  "登录成功响应"
// @Router   /v1/wechat/callback [get]
func (b *AirportApi) WechatCallback(c *gin.Context) {
	var req requst.WechatCallbackRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithValidationError(c, "invalid_params")
		return
	}

	// 创建微信登录服务
	wechatService := airport.NewWechatLoginService()

	// 检查微信登录是否启用
	if !wechatService.IsEnabled() {
		response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API_AUTH, "wechat_service_disabled")
		return
	}

	// 处理微信回调
	user, isNewUser, err := wechatService.ProcessWechatCallback(req)
	if err != nil {
		global.GVA_LOG.Error("处理微信回调失败", zap.Error(err))

		// 根据错误类型返回不同的错误代码
		if err.Error() == "用户取消微信授权" {
			response.FailWithAuthError(c, consts.ERROR_UNAUTHORIZED, "wechat_auth_cancelled")
		} else if err.Error() == "微信授权码无效" {
			response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "wechat_code_invalid")
		} else if err.Error() == "微信授权状态参数无效" {
			response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "wechat_state_invalid")
		} else if err.Error() == "获取微信访问令牌失败" {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_token_failed")
		} else if err.Error() == "获取微信用户信息失败" {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_userinfo_failed")
		} else {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_auth_failed")
		}
		return
	}

	// 生成JWT token
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("生成JWT token失败", zap.Error(err))
		response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "get_token_fail")
		return
	}

	// 设置token到cookie
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))

	// 构建微信用户信息响应
	wechatUserResp := res.WechatUserResponse{
		OpenID:     user.WechatOpenID,
		UnionID:    user.WechatUnionID,
		Nickname:   user.NickName,
		HeadImgURL: user.HeaderImg,
	}

	// 返回登录成功响应
	response.OkWithI18n(c, res.WechatLoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		WechatUser: wechatUserResp,
		Token:      token,
		ExpiresAt:  claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		IsNewUser:  isNewUser,
	})
}

// WechatLogin
// @Tags     Ear
// @Summary  微信登录（直接提供授权码）
// @Produce  application/json
// @Param    data  body      requst.WechatLoginRequest  true  "微信登录请求参数"
// @Param    lang  query     string                     false "响应语言代码 (zh_CN, en, id, hi)" default(zh_CN)
// @Success  200   {object}  response.Response{data=response.WechatLoginResponse,msg=string}  "登录成功响应"
// @Router   /v1/wechat/login [post]
func (b *AirportApi) WechatLogin(c *gin.Context) {
	var req requst.WechatLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithValidationError(c, "invalid_params")
		return
	}

	// 验证必需参数
	if req.Code == "" {
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 创建微信登录服务
	wechatService := airport.NewWechatLoginService()

	// 检查微信登录是否启用
	if !wechatService.IsEnabled() {
		response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API_AUTH, "wechat_service_disabled")
		return
	}

	// 处理微信登录
	user, isNewUser, err := wechatService.ProcessWechatLogin(req)
	if err != nil {
		global.GVA_LOG.Error("处理微信登录失败", zap.Error(err))

		// 根据错误类型返回不同的错误代码
		if err.Error() == "微信授权码无效" {
			response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "wechat_code_invalid")
		} else if err.Error() == "微信授权状态参数无效" {
			response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "wechat_state_invalid")
		} else if err.Error() == "获取微信访问令牌失败" {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_token_failed")
		} else if err.Error() == "获取微信用户信息失败" {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_userinfo_failed")
		} else {
			response.FailWithAuthError(c, consts.ERROR_EXTERNAL_API, "wechat_auth_failed")
		}
		return
	}

	// 生成JWT token
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("生成JWT token失败", zap.Error(err))
		response.FailWithAuthError(c, consts.ERROR_TOKEN_INVALID, "get_token_fail")
		return
	}

	// 设置token到cookie
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))

	// 构建微信用户信息响应
	wechatUserResp := res.WechatUserResponse{
		OpenID:     user.WechatOpenID,
		UnionID:    user.WechatUnionID,
		Nickname:   user.NickName,
		HeadImgURL: user.HeaderImg,
	}

	// 返回登录成功响应
	response.OkWithI18n(c, res.WechatLoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		WechatUser: wechatUserResp,
		Token:      token,
		ExpiresAt:  claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		IsNewUser:  isNewUser,
	})
}

// WechatRedirect
// @Tags     Ear
// @Summary  微信授权重定向（用于网页授权）
// @Produce  text/html
// @Param    state  query  string  false  "状态参数"
// @Param    lang   query  string  false  "响应语言代码 (zh_CN, en, id, hi)" default(zh_CN)
// @Success  302    "重定向到微信授权页面"
// @Router   /v1/wechat/redirect [get]
func (b *AirportApi) WechatRedirect(c *gin.Context) {
	state := c.Query("state")

	// 创建微信登录服务
	wechatService := airport.NewWechatLoginService()

	// 检查微信登录是否启用
	if !wechatService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "微信登录服务未启用",
		})
		return
	}

	// 生成授权URL
	authURL, err := wechatService.GenerateAuthURL(state)
	if err != nil {
		global.GVA_LOG.Error("生成微信授权URL失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成授权URL失败",
		})
		return
	}

	// 重定向到微信授权页面
	c.Redirect(http.StatusFound, authURL)
}

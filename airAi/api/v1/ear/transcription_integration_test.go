package ear

import (
	"airAi/other_api/qwen"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestEnhancedStreamingTranscriptionResult 测试增强的流式转录结果结构
func TestEnhancedStreamingTranscriptionResult(t *testing.T) {
	// 测试ASR结果
	asrResult := qwen.StreamingTranscriptionResult{
		Text:         "hello world,这里是阿里巴巴语音实验室。",
		IsPartial:    false,
		IsEnd:        false,
		IsTranslated: false,
		Language:     "zh",
	}

	assert.Equal(t, "hello world,这里是阿里巴巴语音实验室。", asrResult.Text)
	assert.False(t, asrResult.IsPartial)
	assert.False(t, asrResult.IsEnd)
	assert.False(t, asrResult.IsTranslated)
	assert.Equal(t, "zh", asrResult.Language)

	// 测试翻译结果
	translationResult := qwen.StreamingTranscriptionResult{
		Text:         "Hello world, this is Alibaba Speech Lab.",
		IsPartial:    false,
		IsEnd:        false,
		IsTranslated: true,
		Language:     "en",
	}

	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", translationResult.Text)
	assert.False(t, translationResult.IsPartial)
	assert.False(t, translationResult.IsEnd)
	assert.True(t, translationResult.IsTranslated)
	assert.Equal(t, "en", translationResult.Language)
}

// TestTranscriptionIntegrationJSONSerialization 测试转录集成的JSON序列化
func TestTranscriptionIntegrationJSONSerialization(t *testing.T) {
	// 测试ASR结果序列化
	asrResult := qwen.StreamingTranscriptionResult{
		Text:         "测试语音识别",
		IsPartial:    true,
		IsEnd:        false,
		IsTranslated: false,
		Language:     "zh",
	}

	jsonData, err := json.Marshal(asrResult)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "text")
	assert.Contains(t, string(jsonData), "is_partial")
	assert.Contains(t, string(jsonData), "is_translated")
	assert.Contains(t, string(jsonData), "language")

	// 测试翻译结果序列化
	translationResult := qwen.StreamingTranscriptionResult{
		Text:         "Test speech recognition",
		IsPartial:    false,
		IsEnd:        false,
		IsTranslated: true,
		Language:     "en",
	}

	jsonData, err = json.Marshal(translationResult)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "is_translated")

	// 反序列化测试
	var deserializedResult qwen.StreamingTranscriptionResult
	err = json.Unmarshal(jsonData, &deserializedResult)
	assert.NoError(t, err)
	assert.Equal(t, translationResult.Text, deserializedResult.Text)
	assert.Equal(t, translationResult.IsTranslated, deserializedResult.IsTranslated)
	assert.Equal(t, translationResult.Language, deserializedResult.Language)
}

// TestTranscriptionMethodCompatibility 测试与Transcription方法的兼容性
func TestTranscriptionMethodCompatibility(t *testing.T) {
	// 验证新的流式结果结构与Transcription方法返回的数据兼容
	
	// 模拟Transcription方法返回的数据结构
	transcriptionData := map[string]string{
		"recognized": "hello world,这里是阿里巴巴语音实验室。",
		"translated": "Hello world, this is Alibaba Speech Lab.",
	}

	// 验证可以从Transcription数据创建流式结果
	asrResult := qwen.StreamingTranscriptionResult{
		Text:         transcriptionData["recognized"],
		IsPartial:    false,
		IsEnd:        false,
		IsTranslated: false,
		Language:     "zh",
	}

	translationResult := qwen.StreamingTranscriptionResult{
		Text:         transcriptionData["translated"],
		IsPartial:    false,
		IsEnd:        false,
		IsTranslated: true,
		Language:     "en",
	}

	assert.Equal(t, transcriptionData["recognized"], asrResult.Text)
	assert.Equal(t, transcriptionData["translated"], translationResult.Text)
	assert.False(t, asrResult.IsTranslated)
	assert.True(t, translationResult.IsTranslated)
}

// TestStreamingVsTranscriptionMethodComparison 测试流式方法与Transcription方法的对比
func TestStreamingVsTranscriptionMethodComparison(t *testing.T) {
	// 对比流式方法和Transcription方法的特点
	
	// 流式方法特点
	streamingFeatures := map[string]bool{
		"real_time_results":    true,
		"partial_results":      true,
		"immediate_feedback":   true,
		"memory_efficient":     true,
		"blocking_operation":   false,
	}

	// Transcription方法特点
	transcriptionFeatures := map[string]bool{
		"real_time_results":    false,
		"partial_results":      false,
		"immediate_feedback":   false,
		"memory_efficient":     false,
		"blocking_operation":   true,
		"complete_results":     true,
		"built_in_translation": true,
	}

	// 验证流式方法的优势
	assert.True(t, streamingFeatures["real_time_results"])
	assert.True(t, streamingFeatures["partial_results"])
	assert.True(t, streamingFeatures["immediate_feedback"])
	assert.False(t, streamingFeatures["blocking_operation"])

	// 验证Transcription方法的特点
	assert.False(t, transcriptionFeatures["real_time_results"])
	assert.True(t, transcriptionFeatures["blocking_operation"])
	assert.True(t, transcriptionFeatures["complete_results"])
	assert.True(t, transcriptionFeatures["built_in_translation"])
}

// TestIntegrationBenefits 测试集成的好处
func TestIntegrationBenefits(t *testing.T) {
	// 测试集成后的好处
	integrationBenefits := []string{
		"leverages_transcription_infrastructure",
		"maintains_streaming_capability",
		"includes_built_in_translation",
		"preserves_real_time_performance",
		"unified_websocket_connection",
		"consistent_configuration",
	}

	// 验证所有好处都存在
	for _, benefit := range integrationBenefits {
		assert.NotEmpty(t, benefit, "Integration benefit should not be empty")
	}

	// 验证集成的技术优势
	technicalAdvantages := map[string]string{
		"websocket_reuse":     "Same WebSocket infrastructure as Transcription method",
		"translation_config":  "TranslationEnabled: true in ASR parameters",
		"streaming_delivery":  "Real-time result delivery via channels",
		"memory_efficiency":   "No accumulation of complete results",
		"error_handling":      "Consistent error handling across methods",
	}

	for advantage, description := range technicalAdvantages {
		assert.NotEmpty(t, advantage)
		assert.NotEmpty(t, description)
	}
}

// TestBackwardCompatibilityWithTranscription 测试与Transcription方法的向后兼容性
func TestBackwardCompatibilityWithTranscription(t *testing.T) {
	// 验证新的增强结构不会破坏现有的Transcription方法使用
	
	// 模拟现有的Transcription方法调用
	config := qwen.AliYunConfig{
		ApiKey:          "test-key",
		Model:           qwen.GummyRealtimeV1,
		SourceLanguage:  "zh",
		TargetLanguages: "en",
	}

	// 验证配置结构没有变化
	assert.Equal(t, "test-key", config.ApiKey)
	assert.Equal(t, qwen.GummyRealtimeV1, config.Model)
	assert.Equal(t, "zh", config.SourceLanguage)
	assert.Equal(t, "en", config.TargetLanguages)

	// 验证新字段是可选的
	result := qwen.StreamingTranscriptionResult{
		Text:      "test",
		IsPartial: false,
		IsEnd:     false,
		// IsTranslated 和 Language 字段是可选的
	}

	jsonData, err := json.Marshal(result)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "text")
	
	// 验证可选字段在JSON中正确处理
	var unmarshaled qwen.StreamingTranscriptionResult
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, "test", unmarshaled.Text)
	assert.False(t, unmarshaled.IsTranslated) // 默认值
	assert.Empty(t, unmarshaled.Language)     // 默认值
}

package ear

import (
	"airAi/common/requst"
	res "airAi/common/response"
	"airAi/global"
	"airAi/utils"
	"fmt"
	"model/airpods"
	"model/common/response"
	"model/system"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Login
// @Tags      Ear
// @Summary   登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.LoginRequest           true  "account"                                                            true  "上传文件示例"
// @Success   200   {object}  response.Response{data=res.LoginResponse,msg=string}  "login详情"
// @Router    /v1/login [post]
func (b *AirportApi) Login(c *gin.Context) {
	var l requst.LoginRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.LoginVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	user, err := airportService.Login(l) //
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	token, claims, err := utils.LoginToken(user)
	if err != nil {
		global.GVA_LOG.Error("get token fail!", zap.Error(err))
		response.FailWithValidationError(c, "get_token_fail")
		return
	}
	utils.SetToken(c, token, int(claims.RegisteredClaims.ExpiresAt.Unix()-time.Now().Unix()))
	if user.NickName == "" {
		user.NickName, _ = utils.RandomUsername(5)
	}
	response.OkWithI18n(c, res.LoginResponse{
		User: res.User{
			UUID:      user.UUID,
			Username:  user.Username,
			NickName:  user.NickName,
			HeaderImg: user.HeaderImg,
			Phone:     user.Phone,
			Email:     user.Email,
			Enable:    user.Enable,
		},
		Token:     token,
		ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
	})
}

// CheckCode
// @Tags      Ear
// @Summary   登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body    requst.CheckCodeRequest           true  "account,code"                                                            true  "上传文件示例"
// @Success   200   {object}  response.Response{data=res.LoginResponse,msg=string}  "login详情"
// @Router    /v1/checkCode [post]
func (b *AirportApi) CheckCode(c *gin.Context) {
	var l requst.CheckCodeRequest
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.CheckCodeVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	ok := airportService.CheckCode(l.Account, l.Code) //
	if !ok {
		response.FailWithMessage("invalid code", c)
		return
	}
	response.OkWithMessage("success", c)
}

// LogOut
// @Tags      Ear
// @Summary   退出登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=string,msg=string}  "上传文件示例,返回包括文件详情"
// @Router    /v1/logOut [get]
func (b *AirportApi) LogOut(c *gin.Context) {
	token := utils.GetToken(c)
	var jwtList system.JwtBlacklist
	err := global.GVA_DB.Create(&jwtList).Error
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	global.BlackCache.SetDefault(token, struct{}{})
	response.Ok(c)
}

// ResetPassword
// @Tags      Ear
// @Summary   重置密码
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param    data  body     requst.ResetPassword           true  "phone,code,password"                                                         true  "上传文件示例"
// @Success   200   {object}  response.Response{data=string,msg=string}  "绑定手机号"
// @Router    /v1/resetPassword [post]
func (b *AirportApi) ResetPassword(c *gin.Context) {
	var l requst.ResetPassword
	if err := c.ShouldBind(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := utils.Verify(l, utils.ResetPasswordVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = airportService.ResetPassword(l.Code, l.Phone, l.Password)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("reset password success", c)
}

// GoogleLogin
// @Tags      Ear
// @Summary   Google登陆
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body     airpods.AirpodsUser          true  "phone"                                                              true  "上传文件示例"
// @Success   200   {object}  response.Response{data=response.Response,msg=string}  "Google登陆"
// @Router    /v1/googleLogin [post]
func (b *AirportApi) GoogleLogin(c *gin.Context) {

}

// UpdateUser
// @Tags      Ear
// @Summary   更新用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body     airpods.AirpodsUser          true  "phone"                                                           true  "上传文件示例"
// @Success   200   {object}  response.Response{data=string,msg=string}  "更新用户信息"
// @Router    /v1/updateUser [post]
func (b *AirportApi) UpdateUser(c *gin.Context) {
	var l airpods.AirpodsUser
	if err := c.ShouldBindJSON(&l); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	l.ID = utils.GetUserID(c)
	err := airportService.UpdateUser(l)
	if err != nil {
		global.GVA_LOG.Error("update user fail!", zap.Error(err))
		response.FailWithValidationError(c, "update_user_fail")
		return
	}
	response.FailWithI18nCode(c, 0, "update_user_success")
}

// GetUserInfo
// @Tags      Ear
// @Summary  获取用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=airpods.AirpodsUser,msg=string}  "获取用户信息"
// @Router    /v1/getUserInfo [get]
func (b *AirportApi) GetUserInfo(c *gin.Context) {
	uid := utils.GetUserID(c)
	user, err := airportService.GetUserInfoById(int64(uid))
	if err != nil {
		global.GVA_LOG.Error("get user fail!", zap.Error(err))
		response.FailWithValidationError(c, "get_user_fail")
		return
	}
	response.OkWithI18n(c, user)
}

// Feedback
// @Tags      Ear
// @Summary  意见反馈
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body     requst.Feedback          true  "content"
// @Success   200   {object}  response.Response{data=string,msg=string}  "获取用户信息"
// @Router    /v1/feedback [post]
func (b *AirportApi) Feedback(c *gin.Context) {
	var feedback requst.Feedback
	if err := c.ShouldBind(&feedback); err != nil {
		global.GVA_LOG.Error("Feedback bind error", zap.Error(err))
		response.FailWithValidationError(c, "missing_params")
		return
	}

	// 使用国际化的反馈验证错误处理
	err := utils.Verify(feedback, utils.FeedbackVerify)
	if err != nil {
		global.GVA_LOG.Error("Feedback validation error", zap.Error(err))
		// 检查是否为Content字段的验证错误，提供特定的国际化错误消息
		if strings.Contains(err.Error(), "Content") {
			if strings.Contains(err.Error(), "值不能为空") {
				response.FailWithValidationError(c, "feedback_content_required")
			} else if strings.Contains(err.Error(), "格式校验不通过") {
				response.FailWithValidationError(c, "feedback_content_invalid")
			} else {
				response.FailWithValidationError(c, "feedback_content_invalid")
			}
		} else {
			// 其他验证错误使用统一的验证错误处理器
			response.FailWithValidationErrorFromUtils(c, err)
		}
		return
	}

	uid := utils.GetUserID(c)
	err = airportService.Feedback(uid, feedback.Content)
	if err != nil {
		global.GVA_LOG.Error("Feedback service error", zap.Error(err))
		response.FailWithValidationError(c, "feedback_submit_failed")
		return
	}

	global.GVA_LOG.Info("Feedback submitted successfully",
		zap.Uint("uid", uid),
		zap.String("content_length", fmt.Sprintf("%d", len(feedback.Content))),
	)
	response.FailWithI18nCode(c, 0, "feedback_submit_success")
}

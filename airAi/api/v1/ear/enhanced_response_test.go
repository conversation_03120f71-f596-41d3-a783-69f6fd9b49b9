package ear

import (
	"airAi/other_api/qwen"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestEnhancedStreamingTranslateResult 测试增强的流式翻译结果结构
func TestEnhancedStreamingTranslateResult(t *testing.T) {
	// 测试ASR结果
	asrResult := qwen.StreamingTranslateResult{
		Content:      "",
		OriginalText: "hello world,这里是阿里巴巴语音实验室。",
		IsPartial:    false,
		IsEnd:        false,
	}

	assert.Empty(t, asrResult.Content, "ASR result should have empty content")
	assert.Equal(t, "hello world,这里是阿里巴巴语音实验室。", asrResult.OriginalText)
	assert.False(t, asrResult.IsPartial)
	assert.False(t, asrResult.IsEnd)
	assert.Nil(t, asrResult.VoiceResponse)

	// 测试翻译结果
	translationResult := qwen.StreamingTranslateResult{
		Content:      "Hello world, this is Alibaba Speech Lab.",
		OriginalText: "hello world,这里是阿里巴巴语音实验室。",
		IsPartial:    false,
		IsEnd:        false,
	}

	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", translationResult.Content)
	assert.Equal(t, "hello world,这里是阿里巴巴语音实验室。", translationResult.OriginalText)
	assert.False(t, translationResult.IsPartial)
	assert.False(t, translationResult.IsEnd)

	// 测试TTS结果
	voiceResponse := &qwen.VoiceResponseData{
		AudioData:   []byte("mock_audio_data"),
		AudioFormat: "mp3",
		Duration:    2.5,
		SampleRate:  16000,
		Metadata: map[string]string{
			"tts_model": "sambert-zhinan-v1",
			"voice":     "default",
		},
	}

	ttsResult := qwen.StreamingTranslateResult{
		Content:       "Hello world, this is Alibaba Speech Lab.",
		OriginalText:  "hello world,这里是阿里巴巴语音实验室。",
		VoiceResponse: voiceResponse,
		IsPartial:     false,
		IsEnd:         false,
	}

	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", ttsResult.Content)
	assert.Equal(t, "hello world,这里是阿里巴巴语音实验室。", ttsResult.OriginalText)
	assert.NotNil(t, ttsResult.VoiceResponse)
	assert.Equal(t, "mp3", ttsResult.VoiceResponse.AudioFormat)
	assert.Equal(t, 2.5, ttsResult.VoiceResponse.Duration)
	assert.Equal(t, 16000, ttsResult.VoiceResponse.SampleRate)

	// 测试完整结果
	completeResult := qwen.StreamingTranslateResult{
		Content:       "Hello world, this is Alibaba Speech Lab.",
		OriginalText:  "hello world,这里是阿里巴巴语音实验室。",
		VoiceResponse: voiceResponse,
		IsPartial:     false,
		IsEnd:         true,
	}

	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", completeResult.Content)
	assert.Equal(t, "hello world,这里是阿里巴巴语音实验室。", completeResult.OriginalText)
	assert.NotNil(t, completeResult.VoiceResponse)
	assert.False(t, completeResult.IsPartial)
	assert.True(t, completeResult.IsEnd)
}

// TestResponseStructureJSONSerialization 测试响应结构的JSON序列化
func TestResponseStructureJSONSerialization(t *testing.T) {
	voiceResponse := &qwen.VoiceResponseData{
		AudioData:   []byte("test_audio_data"),
		AudioFormat: "mp3",
		Duration:    2.5,
		SampleRate:  16000,
		Metadata: map[string]string{
			"tts_model": "sambert-zhinan-v1",
			"voice":     "default",
		},
	}

	result := qwen.StreamingTranslateResult{
		Content:       "Hello world, this is Alibaba Speech Lab.",
		OriginalText:  "hello world,这里是阿里巴巴语音实验室。",
		VoiceResponse: voiceResponse,
		IsPartial:     false,
		IsEnd:         true,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(result)
	assert.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	// 验证JSON包含所有必要字段
	jsonStr := string(jsonData)
	assert.Contains(t, jsonStr, "content")
	assert.Contains(t, jsonStr, "original_text")
	assert.Contains(t, jsonStr, "voice_response")
	assert.Contains(t, jsonStr, "is_partial")
	assert.Contains(t, jsonStr, "is_end")

	// 反序列化
	var deserializedResult qwen.StreamingTranslateResult
	err = json.Unmarshal(jsonData, &deserializedResult)
	assert.NoError(t, err)

	// 验证数据完整性
	assert.Equal(t, result.Content, deserializedResult.Content)
	assert.Equal(t, result.OriginalText, deserializedResult.OriginalText)
	assert.Equal(t, result.IsPartial, deserializedResult.IsPartial)
	assert.Equal(t, result.IsEnd, deserializedResult.IsEnd)

	// 验证语音响应数据
	assert.NotNil(t, deserializedResult.VoiceResponse)
	assert.Equal(t, voiceResponse.AudioFormat, deserializedResult.VoiceResponse.AudioFormat)
	assert.Equal(t, voiceResponse.Duration, deserializedResult.VoiceResponse.Duration)
	assert.Equal(t, voiceResponse.SampleRate, deserializedResult.VoiceResponse.SampleRate)
	assert.Equal(t, voiceResponse.Metadata["tts_model"], deserializedResult.VoiceResponse.Metadata["tts_model"])
}

// TestDataSeparation 测试数据分离的正确性
func TestDataSeparation(t *testing.T) {
	// 测试ASR和翻译数据的分离
	result := qwen.StreamingTranslateResult{
		Content:      "Hello world",
		OriginalText: "你好世界",
		IsPartial:    false,
		IsEnd:        false,
	}

	// 验证ASR和翻译数据是分离的
	assert.NotEqual(t, result.Content, result.OriginalText, "Content and OriginalText should be different")
	assert.NotContains(t, result.Content, "[ASR]", "Content should not contain ASR prefix")
	assert.NotEmpty(t, result.OriginalText, "OriginalText should not be empty")
	assert.NotEmpty(t, result.Content, "Content should not be empty")
}

// TestBackwardCompatibility 测试向后兼容性
func TestBackwardCompatibility(t *testing.T) {
	// 测试旧格式的响应仍然可以工作
	oldFormatResult := qwen.StreamingTranslateResult{
		Content:   "Hello world",
		IsPartial: false,
		IsEnd:     false,
	}

	// 序列化
	jsonData, err := json.Marshal(oldFormatResult)
	assert.NoError(t, err)

	// 反序列化到新结构
	var newFormatResult qwen.StreamingTranslateResult
	err = json.Unmarshal(jsonData, &newFormatResult)
	assert.NoError(t, err)

	// 验证基本字段仍然工作
	assert.Equal(t, "Hello world", newFormatResult.Content)
	assert.False(t, newFormatResult.IsPartial)
	assert.False(t, newFormatResult.IsEnd)

	// 新字段应该为空值
	assert.Empty(t, newFormatResult.OriginalText)
	assert.Nil(t, newFormatResult.VoiceResponse)
}

// TestVoiceResponseDataStructure 测试语音响应数据结构
func TestVoiceResponseDataStructure(t *testing.T) {
	voiceData := qwen.VoiceResponseData{
		AudioData:   []byte("test_audio_bytes"),
		AudioFormat: "mp3",
		Duration:    3.5,
		SampleRate:  22050,
		Metadata: map[string]string{
			"tts_model": "sambert-zhinan-v1",
			"voice":     "female",
			"quality":   "high",
		},
	}

	// 验证所有字段
	assert.NotEmpty(t, voiceData.AudioData)
	assert.Equal(t, "mp3", voiceData.AudioFormat)
	assert.Equal(t, 3.5, voiceData.Duration)
	assert.Equal(t, 22050, voiceData.SampleRate)
	assert.Equal(t, "sambert-zhinan-v1", voiceData.Metadata["tts_model"])
	assert.Equal(t, "female", voiceData.Metadata["voice"])
	assert.Equal(t, "high", voiceData.Metadata["quality"])

	// 测试JSON序列化
	jsonData, err := json.Marshal(voiceData)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "audio_data")
	assert.Contains(t, string(jsonData), "audio_format")
	assert.Contains(t, string(jsonData), "duration")
	assert.Contains(t, string(jsonData), "sample_rate")
	assert.Contains(t, string(jsonData), "metadata")
}

// TestExpectedResponseFormat 测试期望的响应格式
func TestExpectedResponseFormat(t *testing.T) {
	// 创建符合期望格式的响应
	expectedResponse := qwen.StreamingTranslateResult{
		Content:      "Hello world, this is Alibaba Speech Lab.",
		OriginalText: "hello world,这里是阿里巴巴语音实验室。",
		VoiceResponse: &qwen.VoiceResponseData{
			AudioData:   []byte("base64_encoded_audio"),
			AudioFormat: "mp3",
			Duration:    2.5,
		},
		IsPartial: false,
		IsEnd:     false,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(expectedResponse)
	assert.NoError(t, err)

	// 验证JSON结构符合期望
	var jsonMap map[string]interface{}
	err = json.Unmarshal(jsonData, &jsonMap)
	assert.NoError(t, err)

	// 验证所有期望的字段都存在
	assert.Contains(t, jsonMap, "content")
	assert.Contains(t, jsonMap, "original_text")
	assert.Contains(t, jsonMap, "voice_response")
	assert.Contains(t, jsonMap, "is_partial")
	assert.Contains(t, jsonMap, "is_end")

	// 验证voice_response的子字段
	voiceResponse, ok := jsonMap["voice_response"].(map[string]interface{})
	assert.True(t, ok)
	assert.Contains(t, voiceResponse, "audio_data")
	assert.Contains(t, voiceResponse, "audio_format")
	assert.Contains(t, voiceResponse, "duration")

	// 验证数据类型
	assert.IsType(t, "", jsonMap["content"])
	assert.IsType(t, "", jsonMap["original_text"])
	assert.IsType(t, false, jsonMap["is_partial"])
	assert.IsType(t, false, jsonMap["is_end"])
}

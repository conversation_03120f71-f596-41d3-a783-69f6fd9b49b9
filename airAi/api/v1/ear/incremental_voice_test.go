package ear

import (
	"airAi/other_api/qwen"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestIncrementalVoiceProcessing 测试增量语音处理功能
func TestIncrementalVoiceProcessing(t *testing.T) {
	// 模拟增量语音处理的结果序列
	expectedSequence := []qwen.ComprehensiveVoiceResult{
		// 第一段：ASR结果
		{
			OriginalText:   "Hello world",
			ProcessingType: "asr",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 第一段：翻译结果
		{
			TranslatedText: "你好世界",
			ProcessingType: "translation",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 第一段：TTS结果
		{
			VoiceResponse: &qwen.VoiceResponseData{
				AudioFormat: "mp3",
				Duration:    1.5,
				Metadata: map[string]string{
					"segment_id":      "1",
					"processing_type": "incremental",
					"text_length":     "9",
				},
			},
			ProcessingType: "tts",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 第二段：ASR结果
		{
			OriginalText:   "this is a test",
			ProcessingType: "asr",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 第二段：翻译结果
		{
			TranslatedText: "这是一个测试",
			ProcessingType: "translation",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 第二段：TTS结果
		{
			VoiceResponse: &qwen.VoiceResponseData{
				AudioFormat: "mp3",
				Duration:    2.0,
				Metadata: map[string]string{
					"segment_id":      "2",
					"processing_type": "incremental",
					"text_length":     "14",
				},
			},
			ProcessingType: "tts",
			IsPartial:      false,
			IsEnd:          false,
		},
		// 完成标记
		{
			ProcessingType: "complete",
			IsPartial:      false,
			IsEnd:          true,
		},
	}

	// 验证序列的正确性
	assert.Len(t, expectedSequence, 7, "Expected sequence should have 7 results")

	// 验证增量处理的特点
	asrCount := 0
	translationCount := 0
	ttsCount := 0

	for _, result := range expectedSequence {
		switch result.ProcessingType {
		case "asr":
			asrCount++
			assert.NotEmpty(t, result.OriginalText, "ASR result should have original text")
		case "translation":
			translationCount++
			assert.NotEmpty(t, result.TranslatedText, "Translation result should have translated text")
		case "tts":
			ttsCount++
			assert.NotNil(t, result.VoiceResponse, "TTS result should have voice response")
			assert.Equal(t, "incremental", result.VoiceResponse.Metadata["processing_type"])
		}
	}

	assert.Equal(t, 2, asrCount, "Should have 2 ASR results")
	assert.Equal(t, 2, translationCount, "Should have 2 translation results")
	assert.Equal(t, 2, ttsCount, "Should have 2 TTS results")
}

// TestIncrementalProcessingFlow 测试增量处理流程
func TestIncrementalProcessingFlow(t *testing.T) {
	// 模拟用户说话："Hello world, this is a test"
	// 系统应该增量处理：
	// 1. "Hello world" → 翻译 → 生成语音
	// 2. "this is a test" → 翻译 → 生成语音

	inputSegments := []string{
		"Hello world",
		"this is a test",
	}

	expectedTranslations := []string{
		"你好世界",
		"这是一个测试",
	}

	// 验证每个段都会被独立处理
	for i, segment := range inputSegments {
		// 模拟ASR结果
		asrResult := qwen.StreamingTranscriptionResult{
			Text:         segment,
			IsPartial:    false,
			IsEnd:        false,
			IsTranslated: false,
		}

		// 模拟翻译结果
		translationResult := qwen.StreamingTranscriptionResult{
			Text:         expectedTranslations[i],
			IsPartial:    false,
			IsEnd:        false,
			IsTranslated: true,
		}

		// 验证结果
		assert.Equal(t, segment, asrResult.Text)
		assert.False(t, asrResult.IsTranslated)
		assert.Equal(t, expectedTranslations[i], translationResult.Text)
		assert.True(t, translationResult.IsTranslated)
	}
}

// TestIncrementalVsTraditionalProcessing 测试增量处理与传统处理的对比
func TestIncrementalVsTraditionalProcessing(t *testing.T) {
	// 传统处理：等待完整句子
	traditionalOutput := []string{
		"你好世界，这是一个测试", // 一次性完整翻译
	}

	// 增量处理：立即处理每个段
	incrementalOutput := []string{
		"你好世界",   // 第一段立即翻译
		"这是一个测试", // 第二段立即翻译
	}

	// 验证传统处理特点
	assert.Len(t, traditionalOutput, 1, "Traditional processing should have 1 complete result")
	assert.Contains(t, traditionalOutput[0], "你好世界")
	assert.Contains(t, traditionalOutput[0], "这是一个测试")

	// 验证增量处理特点
	assert.Len(t, incrementalOutput, 2, "Incremental processing should have 2 separate results")
	assert.Equal(t, "你好世界", incrementalOutput[0])
	assert.Equal(t, "这是一个测试", incrementalOutput[1])

	// 验证增量处理的优势
	incrementalAdvantages := map[string]bool{
		"immediate_feedback":     true, // 立即反馈
		"reduced_latency":        true, // 减少延迟
		"better_user_experience": true, // 更好的用户体验
		"real_time_interaction":  true, // 实时交互
	}

	for advantage, expected := range incrementalAdvantages {
		assert.True(t, expected, "Incremental processing should have advantage: %s", advantage)
	}
}

// TestChannelSafetyInIncrementalProcessing 测试增量处理中的通道安全性
func TestChannelSafetyInIncrementalProcessing(t *testing.T) {
	resultChan := make(chan qwen.ComprehensiveVoiceResult, 10)
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// 模拟多个并发的增量处理
	var wg sync.WaitGroup
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(segmentID int) {
			defer wg.Done()

			// 模拟安全发送函数
			safeSend := func(result qwen.ComprehensiveVoiceResult) bool {
				defer func() {
					if r := recover(); r != nil {
						// 捕获 "send on closed channel" panic
					}
				}()

				select {
				case resultChan <- result:
					return true
				case <-ctx.Done():
					return false
				default:
					return false
				}
			}

			// 发送增量结果
			safeSend(qwen.ComprehensiveVoiceResult{
				TranslatedText: "test segment",
				ProcessingType: "translation",
				VoiceResponse: &qwen.VoiceResponseData{
					Metadata: map[string]string{
						"segment_id": string(rune(segmentID + '0')),
					},
				},
			})
		}(i)
	}

	// 启动消费者
	go func() {
		for {
			select {
			case result, ok := <-resultChan:
				if !ok {
					return
				}
				assert.NotEmpty(t, result.ProcessingType)
			case <-ctx.Done():
				return
			}
		}
	}()

	// 等待所有goroutine完成
	wg.Wait()

	// 关闭通道
	close(resultChan)

	// 验证没有panic发生
	assert.True(t, true, "No panic should occur during incremental processing")
}

// TestIncrementalProcessingMetadata 测试增量处理的元数据
func TestIncrementalProcessingMetadata(t *testing.T) {
	// 测试增量处理的元数据结构
	voiceResponse := qwen.VoiceResponseData{
		AudioFormat: "mp3",
		Duration:    2.5,
		Metadata: map[string]string{
			"segment_id":      "1",
			"processing_type": "incremental",
			"text_length":     "12",
			"tts_model":       "sambert-zhinan-v1",
		},
	}

	// 验证增量处理特有的元数据
	assert.Equal(t, "1", voiceResponse.Metadata["segment_id"])
	assert.Equal(t, "incremental", voiceResponse.Metadata["processing_type"])
	assert.Equal(t, "12", voiceResponse.Metadata["text_length"])

	// 验证JSON序列化
	jsonData, err := json.Marshal(voiceResponse)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), "segment_id")
	assert.Contains(t, string(jsonData), "incremental")

	// 验证反序列化
	var deserializedResponse qwen.VoiceResponseData
	err = json.Unmarshal(jsonData, &deserializedResponse)
	assert.NoError(t, err)
	assert.Equal(t, voiceResponse.Metadata["segment_id"], deserializedResponse.Metadata["segment_id"])
}

// TestIncrementalProcessingPerformance 测试增量处理的性能特征
func TestIncrementalProcessingPerformance(t *testing.T) {
	// 模拟性能测试数据
	performanceMetrics := map[string]interface{}{
		"segments_processed":    5,
		"average_segment_time":  200,  // 毫秒
		"total_processing_time": 1000, // 毫秒
		"first_result_latency":  50,   // 毫秒
		"concurrent_segments":   true,
		"memory_efficient":      true,
	}

	// 验证性能特征
	assert.Equal(t, 5, performanceMetrics["segments_processed"])
	assert.Less(t, performanceMetrics["average_segment_time"], 500, "Average segment time should be less than 500ms")
	assert.Less(t, performanceMetrics["first_result_latency"], 100, "First result latency should be less than 100ms")
	assert.True(t, performanceMetrics["concurrent_segments"].(bool))
	assert.True(t, performanceMetrics["memory_efficient"].(bool))

	// 验证增量处理的性能优势
	incrementalBenefits := []string{
		"immediate_first_result",
		"parallel_segment_processing",
		"reduced_total_latency",
		"better_perceived_performance",
	}

	for _, benefit := range incrementalBenefits {
		assert.NotEmpty(t, benefit, "Incremental processing benefit should not be empty")
	}
}

// TestASRContentFieldEmpty 测试ASR结果的content字段为空
func TestASRContentFieldEmpty(t *testing.T) {
	// 模拟ASR结果
	asrResult := qwen.ComprehensiveVoiceResult{
		OriginalText:   "Hello world",
		ASRPartial:     false,
		ProcessingType: "asr",
		IsPartial:      false,
		IsEnd:          false,
	}

	// 验证ASR结果没有设置翻译内容
	assert.Empty(t, asrResult.TranslatedText, "ASR result should have empty TranslatedText")
	assert.NotEmpty(t, asrResult.OriginalText, "ASR result should have OriginalText")
	assert.Equal(t, "asr", asrResult.ProcessingType)

	// 模拟API层的转换
	apiResult := qwen.StreamingTranslateResult{
		Content:      "", // ASR结果的content应该为空
		OriginalText: asrResult.OriginalText,
		IsPartial:    asrResult.IsPartial,
		IsEnd:        false,
	}

	// 验证API层正确处理ASR结果
	assert.Empty(t, apiResult.Content, "ASR result should have empty Content field")
	assert.Equal(t, "Hello world", apiResult.OriginalText)
	assert.False(t, apiResult.IsPartial)
}

// TestTTSGenerationTiming 测试TTS生成时机
func TestTTSGenerationTiming(t *testing.T) {
	// 测试部分翻译结果不应该生成TTS
	partialTranslationResult := qwen.StreamingTranscriptionResult{
		Text:         "Hello",
		IsPartial:    true, // 部分结果
		IsEnd:        false,
		IsTranslated: true,
	}

	// 测试完整翻译结果应该生成TTS
	completeTranslationResult := qwen.StreamingTranscriptionResult{
		Text:         "Hello world",
		IsPartial:    false, // 完整结果
		IsEnd:        false,
		IsTranslated: true,
	}

	// 验证TTS生成逻辑
	shouldGenerateTTSForPartial := !partialTranslationResult.IsPartial &&
		len(strings.TrimSpace(partialTranslationResult.Text)) > 0
	shouldGenerateTTSForComplete := !completeTranslationResult.IsPartial &&
		len(strings.TrimSpace(completeTranslationResult.Text)) > 0

	assert.False(t, shouldGenerateTTSForPartial, "Should not generate TTS for partial translation")
	assert.True(t, shouldGenerateTTSForComplete, "Should generate TTS for complete translation")
}

// TestIncrementalProcessingDataSeparation 测试增量处理的数据分离
func TestIncrementalProcessingDataSeparation(t *testing.T) {
	// 模拟完整的增量处理序列
	processingSequence := []struct {
		resultType   string
		hasContent   bool
		hasOriginal  bool
		hasVoice     bool
		isPartial    bool
		shouldGenTTS bool
	}{
		// ASR结果
		{"asr", false, true, false, false, false},
		// 部分翻译结果
		{"translation", true, false, false, true, false},
		// 完整翻译结果
		{"translation", true, false, false, false, true},
		// TTS结果
		{"tts", false, false, true, false, false},
	}

	for i, step := range processingSequence {
		t.Run(fmt.Sprintf("Step_%d_%s", i+1, step.resultType), func(t *testing.T) {
			switch step.resultType {
			case "asr":
				result := qwen.ComprehensiveVoiceResult{
					OriginalText:   "test text",
					ProcessingType: "asr",
					IsPartial:      step.isPartial,
				}
				assert.Equal(t, step.hasOriginal, result.OriginalText != "")
				assert.Equal(t, step.hasContent, result.TranslatedText != "")
				assert.Equal(t, step.hasVoice, result.VoiceResponse != nil)

			case "translation":
				result := qwen.ComprehensiveVoiceResult{
					TranslatedText: "translated text",
					ProcessingType: "translation",
					IsPartial:      step.isPartial,
				}
				assert.Equal(t, step.hasContent, result.TranslatedText != "")
				assert.Equal(t, step.hasOriginal, result.OriginalText != "")
				assert.Equal(t, step.hasVoice, result.VoiceResponse != nil)

				// 验证TTS生成时机
				shouldGenTTS := !step.isPartial && len(strings.TrimSpace(result.TranslatedText)) > 0
				assert.Equal(t, step.shouldGenTTS, shouldGenTTS)

			case "tts":
				result := qwen.ComprehensiveVoiceResult{
					VoiceResponse:  &qwen.VoiceResponseData{AudioFormat: "mp3"},
					ProcessingType: "tts",
					IsPartial:      step.isPartial,
				}
				assert.Equal(t, step.hasVoice, result.VoiceResponse != nil)
				assert.Equal(t, step.hasContent, result.TranslatedText != "")
				assert.Equal(t, step.hasOriginal, result.OriginalText != "")
			}
		})
	}
}

// TestContentDuplicationFix 测试内容重复问题的修复
func TestContentDuplicationFix(t *testing.T) {
	// 模拟多个翻译段的处理
	translationSegments := []struct {
		text      string
		isPartial bool
		expected  string
	}{
		{"Hello", true, "Hello"},                               // 部分翻译
		{"Hello world", true, "Hello world"},                   // 部分翻译
		{"Hello world, this is", true, "Hello world, this is"}, // 部分翻译
		{"Hello world, this is Alibaba Speech Lab.", false, "Hello world, this is Alibaba Speech Lab."}, // 完整翻译
	}

	var currentTranslation string

	for i, segment := range translationSegments {
		t.Run(fmt.Sprintf("Segment_%d", i+1), func(t *testing.T) {
			// 模拟修复后的逻辑：不累积，直接使用当前段
			if segment.isPartial {
				// 部分翻译：直接使用当前段，不累积
				content := segment.text
				assert.Equal(t, segment.expected, content)
				assert.NotContains(t, content, "Hello,hello") // 确保没有重复
			} else {
				// 完整翻译：替换而不是累积
				currentTranslation = segment.text // 替换而不是累积
				content := currentTranslation
				assert.Equal(t, segment.expected, content)
				assert.NotContains(t, content, "Hello,hello")     // 确保没有重复
				assert.NotContains(t, content, "translate to en") // 确保没有重复
			}
		})
	}

	// 验证最终的翻译内容是干净的
	finalContent := currentTranslation
	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", finalContent)
	assert.NotContains(t, finalContent, "Hello,hello")
	assert.NotContains(t, finalContent, "translate to en")
	assert.NotContains(t, finalContent, "here is")
}

// TestCleanTranslationFlow 测试干净的翻译流程
func TestCleanTranslationFlow(t *testing.T) {
	// 模拟正确的翻译流程
	processingSteps := []struct {
		stepType      string
		content       string
		originalText  string
		isPartial     bool
		shouldHaveTTS bool
	}{
		// ASR结果
		{"asr", "", "hello world,这里是阿里巴巴语音实验室。", false, false},
		// 部分翻译结果
		{"translation", "Hello", "", true, false},
		{"translation", "Hello world", "", true, false},
		{"translation", "Hello world, this is", "", true, false},
		// 完整翻译结果
		{"translation", "Hello world, this is Alibaba Speech Lab.", "", false, true},
		// TTS结果
		{"tts", "Hello world, this is Alibaba Speech Lab.", "hello world,这里是阿里巴巴语音实验室。", false, false},
	}

	for i, step := range processingSteps {
		t.Run(fmt.Sprintf("Step_%d_%s", i+1, step.stepType), func(t *testing.T) {
			switch step.stepType {
			case "asr":
				// ASR结果应该有空的content字段
				assert.Empty(t, step.content, "ASR result should have empty content")
				assert.NotEmpty(t, step.originalText, "ASR result should have original text")

			case "translation":
				// 翻译结果应该有干净的content，没有重复
				assert.NotEmpty(t, step.content, "Translation result should have content")
				assert.NotContains(t, step.content, "Hello,hello", "Should not contain duplicated text")
				assert.NotContains(t, step.content, "translate to en", "Should not contain processing artifacts")

				// 验证TTS生成时机
				shouldGenTTS := !step.isPartial && len(strings.TrimSpace(step.content)) > 0
				assert.Equal(t, step.shouldHaveTTS, shouldGenTTS, "TTS generation timing should be correct")

			case "tts":
				// TTS结果应该基于干净的翻译内容
				assert.NotEmpty(t, step.content, "TTS result should have clean content")
				assert.NotContains(t, step.content, "Hello,hello", "TTS content should not contain duplicates")
				assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", step.content, "TTS should use clean final translation")
			}
		})
	}
}

// TestTranslationAccumulationLogic 测试翻译累积逻辑的修复
func TestTranslationAccumulationLogic(t *testing.T) {
	// 测试修复前后的逻辑差异

	// 模拟修复前的错误逻辑（累积）
	var wrongAccumulation string
	segments := []string{"Hello", ", world", ", this is", " Alibaba"}

	for _, segment := range segments {
		wrongAccumulation += segment // 错误的累积方式
	}

	// 验证错误逻辑会产生问题
	assert.Equal(t, "Hello, world, this is Alibaba", wrongAccumulation)
	// 这种累积方式在实际使用中会产生重复内容

	// 模拟修复后的正确逻辑（替换）
	var correctTranslation string
	translationResults := []struct {
		text      string
		isPartial bool
	}{
		{"Hello", true},
		{"Hello world", true},
		{"Hello world, this is", true},
		{"Hello world, this is Alibaba Speech Lab.", false}, // 最终完整结果
	}

	for _, result := range translationResults {
		if result.isPartial {
			// 部分结果：不累积，直接使用
			partialContent := result.text
			assert.NotContains(t, partialContent, "Hello,hello")
		} else {
			// 完整结果：替换而不是累积
			correctTranslation = result.text // 正确的替换方式
		}
	}

	// 验证正确逻辑产生干净的结果
	assert.Equal(t, "Hello world, this is Alibaba Speech Lab.", correctTranslation)
	assert.NotContains(t, correctTranslation, "Hello,hello")
	assert.NotContains(t, correctTranslation, "translate to en")
}

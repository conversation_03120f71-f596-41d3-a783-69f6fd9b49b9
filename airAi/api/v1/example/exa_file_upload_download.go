package example

import (
	"airAi/global"
	"airAi/other_api"
	"airAi/other_api/chatgpt"
	"airAi/other_api/firebase"
	"airAi/other_api/gemini"
	"encoding/base64"
	"fmt"
	"io"
	"model/common/response"
	"model/example"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	exampleRes "model/example/response"
)

type FileUploadAndDownloadApi struct{}

// UploadFile
// @Tags      ExaFileUploadAndDownload
// @Summary   上传文件示例
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     file  formData  file                                                           true  "上传文件示例"
// @Success   200   {object}  response.Response{data=exampleRes.ExaFileResponse,msg=string}  "上传文件示例,返回包括文件详情"
// @Router    /v1/fileUploadAndDownload/upload [post]
func (b *FileUploadAndDownloadApi) UploadFile(c *gin.Context) {
	var file example.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}
	file, err = fileUploadAndDownloadService.UploadFile(header, noSave) // 文件上传后拿到文件路径
	if err != nil {
		global.GVA_LOG.Error("上传文件失败!", zap.Error(err))
		response.FailWithMessage("上传文件失败", c)
		return
	}
	response.OkWithDetailed(exampleRes.ExaFileResponse{File: file}, "上传成功", c)
}

func (b *FileUploadAndDownloadApi) Recognition(c *gin.Context) {
	var resp other_api.Animal
	var err error
	// 获取上传的文件
	aiType := c.DefaultPostForm("type", "chatgpt")
	token := c.PostForm("token")
	uuid := c.PostForm("uuid")
	fmt.Println("aiType:", aiType)
	fmt.Println("token:", token)
	fmt.Println("uuid:", uuid)
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upload image"})
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to open image"})
		return
	}
	defer src.Close()

	// 读取文件内容
	fileBytes, err := io.ReadAll(src)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read image"})
		return
	}

	// 转换为 Base64
	base64Str := base64.StdEncoding.EncodeToString(fileBytes)

	switch aiType {
	case "chatgpt":
		resp, err = chatgpt.GenerateContent(base64Str)
	case "google":
		resp, err = gemini.GenerateContent(base64Str)
	}
	data := make(map[string]string)
	data["filename"] = file.Filename
	//data["base64"] = base64Str
	if resp.Name == "" {
		resp.Name = "Mystery Visitor"
	}
	data["name"] = resp.Name
	data["uuid"] = uuid
	data["token"] = token
	if err != nil {
		response.OkWithData(gin.H{
			"filename": file.Filename,
			"base64":   base64Str,
			"resp": map[string]interface{}{
				"name": "Mystery Visitor",
			},
		}, c)
		return
	}

	go func() {
		f, err := firebase.NewFirebase(token)
		if err != nil {
			global.GVA_LOG.Error("firebase NewFirebase err", zap.Error(err))
		}
		str, err := f.SendToToken(data)
		if err != nil {
			global.GVA_LOG.Error("firebase SendToToken err", zap.Error(err))
		}
		global.GVA_LOG.Info("SendToToken", zap.Any("str", str))
	}()
	//f, err := firebase.NewFirebase("")
	//if err != nil {
	//	global.GVA_LOG.Error("firebase NewFirebase err", zap.Error(err))
	//}
	//str, err := f.SendToToken(data)
	//if err != nil {
	//	global.GVA_LOG.Error("firebase SendToToken err", zap.Error(err))
	//}
	//global.GVA_LOG.Info("SendToToken", zap.Any("str", str))

	response.OkWithData(gin.H{
		"filename": file.Filename,
		"base64":   base64Str,
		"resp":     resp,
	}, c)

}

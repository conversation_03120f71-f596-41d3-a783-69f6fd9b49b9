package main

import (
	"airAi/core"
	"airAi/core/i18n"
	"airAi/global"
	"airAi/initialize"
	"log"
	"net/http"
	_ "net/http/pprof"

	"go.uber.org/zap"
)

// @title           AirAi API
// @version         1.0
// @description     AirAi WebSocket音频识别和翻译API服务
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8888
// @BasePath  /v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download
func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm()
	initialize.Redis()

	// 初始化国际化系统
	if err := initializeI18n(); err != nil {
		global.GVA_LOG.Error("Failed to initialize i18n system", zap.Error(err))
		// 继续运行，使用备选方案
	}

	if global.GVA_DB != nil {
		initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()
	core.RunWindowsServer()
}

// initializeI18n 初始化国际化系统
func initializeI18n() error {
	return i18n.InitI18n()
}

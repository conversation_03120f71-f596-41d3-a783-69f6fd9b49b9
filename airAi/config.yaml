aliyun-oss:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    bucket-url: yourBucketUrl
    base-path: yourBasePath
autocode:
    server-model: /model/%s
    server-router: /router/%s
    server: /server
    server-api: /api/v1/%s
    server-plug: /plugin/%s
    server-initialize: /initialize
    root: /Users/<USER>/projects/golang/Guarantee
    web-table: /view
    web: /web/src
    server-service: /service/%s
    server-request: /model/%s/request/
    web-api: /api
    web-form: /view
    transfer-restart: true
aws-s3:
    bucket: xxxxx-10005608
    region: ap-shanghai
    endpoint: ""
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
    s3-force-path-style: false
    disable-ssl: false
captcha:
    key-long: 6
    img-width: 240
    img-height: 80
    open-captcha: 0
    open-captcha-timeout: 3600
cors:
    mode: strict-whitelist
    whitelist:
        - allow-origin: example1.com
          allow-methods: POST, GET
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
        - allow-origin: example2.com
          allow-methods: GET, POST
          allow-headers: content-type
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
db-list:
    - type: ""
      alias-name: ""
      prefix: ""
      port: ""
      config: ""
      db-name: ""
      username: ""
      password: ""
      path: ""
      engine: ""
      log-mode: ""
      max-idle-conns: 10
      max-open-conns: 100
      singular: false
      log-zap: false
      disable: true
email:
    to: <EMAIL>
    from: <EMAIL>
    host: smtp.163.com
    secret: xxx
    nickname: test
    port: 465
    is-ssl: true
excel:
    dir: ./resource/excel/
hua-wei-obs:
    path: you-path
    bucket: you-bucket
    endpoint: you-endpoint
    access-key: you-access-key
    secret-key: you-secret-key
jwt:
    signing-key: 8e585ce4-29c7-4339-96b7-b1d85c3b898a
    expires-time: 7d
    buffer-time: 1d
    issuer: qmPlus
local:
    path: uploads/file
    store-path: uploads/file
mongo:
    coll: ""
    options: ""
    database: ""
    username: ""
    password: ""
    auth-source: ""
    min-pool-size: 0
    max-pool-size: 100
    socket-timeout-ms: 0
    connect-timeout-ms: 0
    is-zap: false
    hosts:
        - host: ""
          port: ""
mssql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
mysql:
    prefix: ""
    port: "3306"
    config: charset=utf8mb4&parseTime=True&loc=Local
    db-name: airport
    username: airport
    password: bDBzjJTJyheyztMc
    path: **************
    engine: ""
    log-mode: error
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
oracle:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
pgsql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
qiniu:
    zone: ZoneHuaDong
    bucket: ""
    img-path: ""
    access-key: ""
    secret-key: ""
    use-https: false
    use-cdn-domains: false
redis:
#    addr: 127.0.0.1:6379
    addr: **************:6379
    password: "redis_rfBzy6"
    db: 0
    useCluster: false
    clusterAddrs:
        - **********:7000
        - **********:7001
        - **********:7002
sqlite:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
system:
    db-type: mysql
    oss-type: local
    router-prefix: ""
    addr: 8888
    iplimit-count: 15000
    iplimit-time: 3600
    use-multipoint: true
    use-redis: false
    use-mongo: false
tencent-cos:
    bucket: xxxxx-10005608
    region: ap-shanghai
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
zap:
    level: info
    prefix: '[airport]'
    format: console
    director: log
    encode-level: LowercaseColorLevelEncoder
    stacktrace-key: stacktrace
    max-age: 0
    show-line: true
    log-in-console: true
gemini:
    key: ""
chatgpt:
    key: ""
xunfei:
    appid: 3c8f9bf3
    ws: wss://iat-api.xfyun.cn
    http: ""
    api-secret: YWI5MzI4NTcyN2NiZjA1MTVjMTg5YzQz
    api-key: eebde6e169b02d131d64ed8a0942c9c9

# Qwen AI服务配置
qwen:
    # API密钥 - 从环境变量QWEN_API_KEY读取，或在此处配置
    api-key: sk-130c858aa2a345949409d91ff45e0367

    # 基础URL
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1/

    # 默认模型
    default-model: qwen-plus

    # ASR语音识别配置
    asr:
        # 默认ASR模型
        default-model: paraformer-realtime-v2

        # 支持翻译的模型
        translation-model: gummy-realtime-v1

        # 默认音频格式
        default-format: mp3

        # 默认源语言
        default-source-language: zh

        # 默认目标语言列表
        default-target-languages:
            - en
            - ja

        # 连接超时时间（秒）
        connection-timeout: 30

        # 最大重连次数
        max-reconnect-attempts: 3

    # 翻译配置
    translation:
        # 默认翻译模型
        default-model: qwen-plus

        # 默认源语言
        default-source-language: zh

        # 默认目标语言
        default-target-language: en

        # 请求超时时间（秒）
        request-timeout: 30

    # TTS语音合成配置
    tts:
        # 默认TTS模型
        default-model: sambert-zhichu-v1

        # 默认语音
        default-voice: zhichu

        # 默认语音速率
        default-speech-rate: 1.0

        # 默认音频格式
        default-format: mp3

        # 请求超时时间（秒）
        request-timeout: 30

# 微信OAuth配置
wechat:
    # 是否启用微信登录
    enabled: false

    # 微信应用ID（从微信开放平台获取）
    app-id: "your_wechat_app_id"

    # 微信应用密钥（从微信开放平台获取）
    app-secret: "your_wechat_app_secret"

    # 授权回调地址（需要在微信开放平台配置）
    redirect-url: "https://your-domain.com/api/v1/wechat/callback"

    # 微信OAuth授权URL（一般不需要修改）
    auth-url: "https://open.weixin.qq.com/connect/oauth2/authorize"

    # 微信获取access_token的URL（一般不需要修改）
    token-url: "https://api.weixin.qq.com/sns/oauth2/access_token"

    # 微信获取用户信息的URL（一般不需要修改）
    user-info-url: "https://api.weixin.qq.com/sns/userinfo"

    # 授权作用域（snsapi_base: 静默授权，snsapi_userinfo: 用户信息授权）
    scope: "snsapi_userinfo"


{"success": {"description": "General success message", "other": "Success"}, "operation_success": {"description": "Operation successful message", "other": "Operation successful"}, "operation_failed": {"description": "Operation failed message", "other": "Operation failed"}, "internal_error": {"description": "Internal server error", "other": "Internal server error"}, "service_unavailable": {"description": "Service unavailable", "other": "Service temporarily unavailable"}, "request_timeout": {"description": "Request timeout", "other": "Request timeout"}, "rate_limit_exceeded": {"description": "Rate limit exceeded", "other": "Rate limit exceeded, please try again later"}, "invalid_params": {"description": "Invalid parameters", "other": "Invalid parameters"}, "missing_params": {"description": "Missing required parameters", "other": "Missing required parameters"}, "invalid_format": {"description": "Invalid parameter format", "other": "Invalid parameter format"}, "invalid_language": {"description": "Unsupported language code", "other": "Unsupported language code"}, "invalid_file": {"description": "Invalid file", "other": "Invalid file"}, "file_too_large": {"description": "File size exceeds limit", "other": "File size exceeds limit"}, "unsupported_format": {"description": "Unsupported file format", "other": "Unsupported file format"}, "unauthorized": {"description": "Unauthorized access", "other": "Unauthorized access"}, "token_invalid": {"description": "Invalid access token", "other": "Invalid access token"}, "token_expired": {"description": "Access token expired", "other": "Access token expired"}, "insufficient_balance": {"description": "Insufficient account balance", "other": "Insufficient account balance"}, "permission_denied": {"description": "Permission denied", "other": "Permission denied"}, "account_locked": {"description": "Account locked", "other": "Account locked"}, "user_not_found": {"description": "User not found", "other": "User not found"}, "user_already_exists": {"description": "User already exists", "other": "User already exists"}, "invalid_credentials": {"description": "Invalid username or password", "other": "Invalid username or password"}, "code_invalid": {"description": "Invalid verification code", "other": "Invalid verification code"}, "code_expired": {"description": "Verification code expired", "other": "Verification code expired"}, "password_weak": {"description": "Password strength insufficient", "other": "Password strength insufficient"}, "file_upload_failed": {"description": "File upload failed", "other": "File upload failed"}, "file_open_failed": {"description": "Failed to open file", "other": "Failed to open file"}, "file_read_failed": {"description": "Failed to read file", "other": "Failed to read file"}, "file_write_failed": {"description": "Failed to write file", "other": "Failed to write file"}, "file_not_found": {"description": "File not found", "other": "File not found"}, "asr_failed": {"description": "Speech recognition failed", "other": "Speech recognition failed"}, "asr_timeout": {"description": "Speech recognition timeout", "other": "Speech recognition timeout"}, "asr_unsupported_lang": {"description": "Unsupported speech recognition language", "other": "Unsupported speech recognition language"}, "asr_audio_format": {"description": "Unsupported audio format", "other": "Unsupported audio format"}, "asr_audio_too_short": {"description": "Audio duration too short", "other": "Audio duration too short"}, "asr_audio_too_long": {"description": "Audio duration too long", "other": "Audio duration too long"}, "translate_failed": {"description": "Translation failed", "other": "Translation failed"}, "translate_timeout": {"description": "Translation timeout", "other": "Translation timeout"}, "translate_unsupported_lang": {"description": "Unsupported translation language", "other": "Unsupported translation language"}, "translate_text_too_long": {"description": "Text too long", "other": "Text too long"}, "tts_failed": {"description": "Text-to-speech failed", "other": "Text-to-speech failed"}, "tts_timeout": {"description": "Text-to-speech timeout", "other": "Text-to-speech timeout"}, "tts_unsupported_lang": {"description": "Unsupported text-to-speech language", "other": "Unsupported text-to-speech language"}, "tts_text_too_long": {"description": "Text too long", "other": "Text too long"}, "meeting_failed": {"description": "Meeting summary generation failed", "other": "Meeting summary generation failed"}, "meeting_timeout": {"description": "Meeting summary generation timeout", "other": "Meeting summary generation timeout"}, "meeting_content_empty": {"description": "Meeting content cannot be empty", "other": "Meeting content cannot be empty"}, "meeting_content_too_long": {"description": "Meeting content too long", "other": "Meeting content too long"}, "stream_failed": {"description": "Streaming processing failed", "other": "Streaming processing failed"}, "stream_interrupted": {"description": "Streaming processing interrupted", "other": "Streaming processing interrupted"}, "stream_timeout": {"description": "Streaming processing timeout", "other": "Streaming processing timeout"}, "websocket_failed": {"description": "WebSocket connection failed", "other": "WebSocket connection failed"}}